"""
Enhanced GUI Configuration Interface for Centralized Template Scanner
Provides comprehensive template management with auto-discovery, testing, and visual feedback
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import glob
import io
import time
from typing import Dict, List, Any, Optional
from PIL import Image, ImageTk, ImageDraw, ImageFont
import cv2
import numpy as np
import logging
import pyautogui
import win32gui
import win32con


class CentralizedScannerGUI:
    """Enhanced GUI for configuring the centralized template scanner"""

    def __init__(self, parent, main_controller):
        self.parent = parent
        self.main_controller = main_controller
        self.centralized_scanner = main_controller.get_centralized_scanner()
        self.logger = logging.getLogger("LastWar.ScannerGUI")

        # Configuration window
        self.window = None
        self.template_tree = None
        self.selected_mapping = None

        # Available modules and their actions
        self.available_modules = {
            "dig": ["execute", "start_digging", "check_completion"],
            "daily_tasks": ["complete_tasks", "navigate", "claim_rewards"],
            "help_click": ["1_HelpButton", "2_HelpChat", "individual_helps", "click_help_all"],
            "alliance_donation": ["start_sequence", "navigate", "process_research", "process_store", "process_technology"],
            "zombie_invasion": ["check_squads", "deploy_squad", "check_squad_status", "navigate_events", "search_zombies", "attack_zombie", "march_troops"],
            "map_trade": ["start_trading", "navigate", "find_resources", "gather_resource"],
            "find_truck": ["attack_truck", "find_and_attack", "scan_for_trucks"]
        }

        # Template discovery
        self.templates_path = "templates/"
        self.discovered_templates = []
        self.template_preview_cache = {}

        # Detection testing
        self.test_image = None
        self.detection_results = []

        # Discover templates on initialization
        self._discover_templates()

    def _discover_templates(self):
        """Discover all template files in the templates directory"""
        try:
            template_patterns = [
                os.path.join(self.templates_path, "*.png"),
                os.path.join(self.templates_path, "*.jpg"),
                os.path.join(self.templates_path, "*.jpeg")
            ]

            self.discovered_templates = []
            for pattern in template_patterns:
                files = glob.glob(pattern)
                for file_path in files:
                    template_name = os.path.splitext(os.path.basename(file_path))[0]
                    self.discovered_templates.append({
                        'name': template_name,
                        'path': file_path,
                        'size': self._get_image_size(file_path)
                    })

            self.discovered_templates.sort(key=lambda x: x['name'])
            self.logger.info(f"Discovered {len(self.discovered_templates)} templates")

        except Exception as e:
            self.logger.error(f"Failed to discover templates: {e}")
            self.discovered_templates = []

    def _get_image_size(self, image_path: str) -> tuple:
        """Get image dimensions with cache bypass"""
        try:
            # Force fresh file read by checking file modification time
            import os
            if not os.path.exists(image_path):
                return (0, 0)

            with Image.open(image_path) as img:
                # Force load to ensure we get current file data
                img.load()
                return img.size
        except Exception as e:
            self.logger.debug(f"Could not get size for {image_path}: {e}")
            return (0, 0)

    def _load_template_preview(self, template_path: str, max_size: tuple = (100, 100)) -> Optional[ImageTk.PhotoImage]:
        """Load and cache template preview image with modification time checking"""
        try:
            # Check if file exists
            if not os.path.exists(template_path):
                return None

            # Get file modification time
            file_mtime = os.path.getmtime(template_path)
            cache_key = f"{template_path}_{file_mtime}"

            # Check if we have a cached version with the same modification time
            if cache_key in self.template_preview_cache:
                return self.template_preview_cache[cache_key]

            # Remove old cached versions of this file
            old_keys = [key for key in self.template_preview_cache.keys() if key.startswith(template_path + "_")]
            for old_key in old_keys:
                del self.template_preview_cache[old_key]

            # Load fresh image
            with Image.open(template_path) as img:
                # Force load to ensure we get current file data
                img.load()
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(img)
                self.template_preview_cache[cache_key] = photo
                return photo

        except Exception as e:
            self.logger.error(f"Failed to load template preview {template_path}: {e}")
            return None
        
    def open_configuration_window(self):
        """Open the enhanced centralized scanner configuration window"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return

        self.window = tk.Toplevel(self.parent)
        self.window.title("🎯 Enhanced Template Scanner Configuration")
        self.window.geometry("1400x900")
        self.window.configure(bg='#2b2b2b')

        # Create main frame with notebook for tabs
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title and mode toggle
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 10))

        title_label = ttk.Label(header_frame, text="🎯 Enhanced Template Scanner Configuration",
                               font=('Arial', 16, 'bold'))
        title_label.pack(side=tk.LEFT)

        # Scanner mode toggle
        self.scanner_mode_var = tk.BooleanVar(value=self.main_controller.use_centralized_scanner)
        mode_toggle = ttk.Checkbutton(header_frame, text="Use Centralized Scanner",
                                     variable=self.scanner_mode_var,
                                     command=self._toggle_scanner_mode)
        mode_toggle.pack(side=tk.RIGHT)

        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Create tabs
        self._create_template_mappings_tab()
        self._create_template_discovery_tab()
        self._create_detection_testing_tab()
        self._create_scanner_settings_tab()
        self._create_exclusion_rules_tab()

        # Control buttons at bottom
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # Left side buttons
        ttk.Button(button_frame, text="💾 Save All",
                  command=self._save_all_configuration).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="🔄 Reload",
                  command=self._reload_configuration).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📤 Export",
                  command=self._export_configuration).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📥 Import",
                  command=self._import_configuration).pack(side=tk.LEFT, padx=5)

        # Right side buttons
        ttk.Button(button_frame, text="❌ Close",
                  command=self.window.destroy).pack(side=tk.RIGHT)

        # Refresh data
        self._refresh_all_data()

    def _create_template_mappings_tab(self):
        """Create the template mappings configuration tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📋 Template Mappings")

        # Add informational note about TRIGGER templates
        info_frame = ttk.Frame(tab_frame)
        info_frame.pack(fill=tk.X, padx=5, pady=(5, 0))

        info_text = ("ℹ️ TRIGGER Templates: These templates detect game states and activate modules.\n"
                    "For module-specific EXECUTION templates, use the Module Configuration interface.")
        info_label = ttk.Label(info_frame, text=info_text, font=('Arial', 9),
                              foreground='blue', wraplength=800)
        info_label.pack(anchor=tk.W)

        # Create paned window for list and details
        paned = ttk.PanedWindow(tab_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Template list
        left_frame = ttk.Frame(paned)
        paned.add(left_frame, weight=2)

        # Template list with toolbar
        list_toolbar = ttk.Frame(left_frame)
        list_toolbar.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(list_toolbar, text="Template Mappings:", font=('Arial', 12, 'bold')).pack(side=tk.LEFT)

        ttk.Button(list_toolbar, text="➕ Add", command=self._add_template_mapping).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(list_toolbar, text="🗑️ Delete", command=self._delete_template_mapping).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(list_toolbar, text="🔄 Refresh", command=self._refresh_template_mappings).pack(side=tk.RIGHT, padx=(5, 0))

        # Template mappings tree
        columns = ('Priority', 'Template', 'Module', 'Action', 'Enabled', 'Threshold', 'Cooldown')
        self.template_tree = ttk.Treeview(left_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.template_tree.heading(col, text=col)
            if col == 'Template':
                self.template_tree.column(col, width=120)
            elif col in ['Priority', 'Enabled', 'Threshold', 'Cooldown']:
                self.template_tree.column(col, width=80)
            else:
                self.template_tree.column(col, width=100)

        # Scrollbars for tree
        tree_scroll_y = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.template_tree.yview)
        tree_scroll_x = ttk.Scrollbar(left_frame, orient=tk.HORIZONTAL, command=self.template_tree.xview)
        self.template_tree.configure(yscrollcommand=tree_scroll_y.set, xscrollcommand=tree_scroll_x.set)

        self.template_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        tree_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        self.template_tree.bind('<<TreeviewSelect>>', self._on_template_select)

        # Right panel - Template details and preview
        right_frame = ttk.Frame(paned)
        paned.add(right_frame, weight=1)

        self._create_template_details_panel(right_frame)

    def _create_template_details_panel(self, parent):
        """Create the template details editing panel"""
        details_label = ttk.Label(parent, text="Template Details:", font=('Arial', 12, 'bold'))
        details_label.pack(anchor=tk.W, pady=(0, 10))

        # Template preview
        preview_frame = ttk.LabelFrame(parent, text="Template Preview")
        preview_frame.pack(fill=tk.X, pady=(0, 10))

        self.template_preview_label = ttk.Label(preview_frame, text="Select a template to preview")
        self.template_preview_label.pack(pady=10)

        # Template configuration
        config_frame = ttk.LabelFrame(parent, text="Configuration")
        config_frame.pack(fill=tk.BOTH, expand=True)

        # Template name dropdown (auto-populated from discovered templates)
        ttk.Label(config_frame, text="Template:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.template_name_var = tk.StringVar()
        self.template_name_combo = ttk.Combobox(config_frame, textvariable=self.template_name_var, width=25)
        self.template_name_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        self.template_name_combo.bind('<<ComboboxSelected>>', self._on_template_name_change)

        # Priority
        ttk.Label(config_frame, text="Priority:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.priority_var = tk.IntVar()
        priority_spin = ttk.Spinbox(config_frame, from_=1, to=100, textvariable=self.priority_var, width=10)
        priority_spin.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)

        # Module dropdown
        ttk.Label(config_frame, text="Module:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.module_var = tk.StringVar()
        self.module_combo = ttk.Combobox(config_frame, textvariable=self.module_var,
                                        values=list(self.available_modules.keys()), width=25)
        self.module_combo.grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        self.module_combo.bind('<<ComboboxSelected>>', self._on_module_change)

        # Action dropdown (populated based on selected module)
        ttk.Label(config_frame, text="Action:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.action_var = tk.StringVar()
        self.action_combo = ttk.Combobox(config_frame, textvariable=self.action_var, width=25)
        self.action_combo.grid(row=3, column=1, sticky=tk.W, padx=5, pady=2)

        # Threshold
        ttk.Label(config_frame, text="Threshold:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=2)
        self.threshold_var = tk.DoubleVar(value=0.8)
        threshold_spin = ttk.Spinbox(config_frame, from_=0.1, to=1.0, increment=0.1,
                                   textvariable=self.threshold_var, width=10)
        threshold_spin.grid(row=4, column=1, sticky=tk.W, padx=5, pady=2)

        # Cooldown
        ttk.Label(config_frame, text="Cooldown (s):").grid(row=5, column=0, sticky=tk.W, padx=5, pady=2)
        self.cooldown_var = tk.DoubleVar()
        cooldown_spin = ttk.Spinbox(config_frame, from_=0, to=3600, increment=1,
                                  textvariable=self.cooldown_var, width=10)
        cooldown_spin.grid(row=5, column=1, sticky=tk.W, padx=5, pady=2)

        # Enabled checkbox
        self.enabled_var = tk.BooleanVar(value=True)
        enabled_check = ttk.Checkbutton(config_frame, text="Enabled", variable=self.enabled_var)
        enabled_check.grid(row=6, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        # Description
        ttk.Label(config_frame, text="Description:").grid(row=7, column=0, sticky=tk.NW, padx=5, pady=2)
        self.description_var = tk.StringVar()
        description_entry = ttk.Entry(config_frame, textvariable=self.description_var, width=30)
        description_entry.grid(row=7, column=1, sticky=tk.W, padx=5, pady=2)

        # Scan Region section
        region_frame = ttk.LabelFrame(config_frame, text="Scan Region")
        region_frame.grid(row=8, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)

        # Scan region coordinates
        ttk.Label(region_frame, text="X:").grid(row=0, column=0, sticky=tk.W, padx=2, pady=2)
        self.region_x_var = tk.IntVar(value=0)
        ttk.Spinbox(region_frame, from_=0, to=2560, textvariable=self.region_x_var, width=8).grid(row=0, column=1, padx=2, pady=2)

        ttk.Label(region_frame, text="Y:").grid(row=0, column=2, sticky=tk.W, padx=2, pady=2)
        self.region_y_var = tk.IntVar(value=0)
        ttk.Spinbox(region_frame, from_=0, to=1440, textvariable=self.region_y_var, width=8).grid(row=0, column=3, padx=2, pady=2)

        ttk.Label(region_frame, text="Width:").grid(row=1, column=0, sticky=tk.W, padx=2, pady=2)
        self.region_width_var = tk.IntVar(value=2560)
        ttk.Spinbox(region_frame, from_=1, to=2560, textvariable=self.region_width_var, width=8).grid(row=1, column=1, padx=2, pady=2)

        ttk.Label(region_frame, text="Height:").grid(row=1, column=2, sticky=tk.W, padx=2, pady=2)
        self.region_height_var = tk.IntVar(value=1440)
        ttk.Spinbox(region_frame, from_=1, to=1440, textvariable=self.region_height_var, width=8).grid(row=1, column=3, padx=2, pady=2)

        # Region description
        ttk.Label(region_frame, text="Region Desc:").grid(row=2, column=0, sticky=tk.W, padx=2, pady=2)
        self.region_desc_var = tk.StringVar(value="Full screen scan")
        ttk.Entry(region_frame, textvariable=self.region_desc_var, width=25).grid(row=2, column=1, columnspan=3, sticky=tk.W, padx=2, pady=2)

        # Region capture button
        ttk.Button(region_frame, text="📷 Capture Region", command=self._capture_scan_region).grid(row=3, column=0, columnspan=2, pady=5)
        ttk.Button(region_frame, text="🖥️ Full Screen", command=self._set_full_screen_region).grid(row=3, column=2, columnspan=2, pady=5)

        # Buttons
        button_frame = ttk.Frame(config_frame)
        button_frame.grid(row=9, column=0, columnspan=2, pady=10)

        ttk.Button(button_frame, text="💾 Save", command=self._save_template_mapping).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🧪 Test", command=self._test_selected_template_mapping).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="👁️ View", command=self._view_template_file).pack(side=tk.LEFT, padx=5)

    def _create_template_discovery_tab(self):
        """Create the template discovery tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🔍 Template Discovery")

        # Header with refresh button
        header_frame = ttk.Frame(tab_frame)
        header_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(header_frame, text="Discovered Templates:", font=('Arial', 12, 'bold')).pack(side=tk.LEFT)
        ttk.Button(header_frame, text="🔄 Refresh", command=self._refresh_discovered_templates).pack(side=tk.RIGHT)
        ttk.Button(header_frame, text="✏️ Edit Selected", command=self._edit_selected_template).pack(side=tk.RIGHT, padx=(0, 5))
        ttk.Button(header_frame, text="➕ Add Selected", command=self._add_selected_templates).pack(side=tk.RIGHT, padx=(0, 5))

        # Create paned window
        paned = ttk.PanedWindow(tab_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Discovered templates list
        left_frame = ttk.Frame(paned)
        paned.add(left_frame, weight=1)

        # Templates listbox with checkboxes
        listbox_frame = ttk.Frame(left_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True)

        self.discovered_listbox = tk.Listbox(listbox_frame, selectmode=tk.EXTENDED)
        self.discovered_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.discovered_listbox.bind('<<ListboxSelect>>', self._on_discovered_template_select)

        discovered_scroll = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=self.discovered_listbox.yview)
        discovered_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.discovered_listbox.configure(yscrollcommand=discovered_scroll.set)

        # Right panel - Template preview and info
        right_frame = ttk.Frame(paned)
        paned.add(right_frame, weight=1)

        # Template info
        info_frame = ttk.LabelFrame(right_frame, text="Template Information")
        info_frame.pack(fill=tk.X, pady=(0, 10))

        self.template_info_text = tk.Text(info_frame, height=4, wrap=tk.WORD)
        self.template_info_text.pack(fill=tk.X, padx=5, pady=5)

        # Template preview
        preview_frame = ttk.LabelFrame(right_frame, text="Preview")
        preview_frame.pack(fill=tk.BOTH, expand=True)

        self.discovery_preview_label = ttk.Label(preview_frame, text="Select a template to preview")
        self.discovery_preview_label.pack(expand=True)

        # Status
        status_frame = ttk.Frame(right_frame)
        status_frame.pack(fill=tk.X, pady=(10, 0))

        self.discovery_status_label = ttk.Label(status_frame, text="Ready")
        self.discovery_status_label.pack()

    def _create_detection_testing_tab(self):
        """Create the detection testing tab with visual feedback"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🧪 Detection Testing")

        # Template selection section at top
        selection_frame = ttk.LabelFrame(tab_frame, text="Template Selection (Sorted by Priority)")
        selection_frame.pack(fill=tk.X, padx=5, pady=5)

        # Template mappings dropdown (sorted by priority)
        ttk.Label(selection_frame, text="Template Mapping:").pack(side=tk.LEFT, padx=5)
        self.test_mapping_var = tk.StringVar()
        self.test_mapping_combo = ttk.Combobox(selection_frame, textvariable=self.test_mapping_var, width=30)
        self.test_mapping_combo.pack(side=tk.LEFT, padx=5)
        self.test_mapping_combo.bind('<<ComboboxSelected>>', self._on_test_mapping_select)

        # Or discovered templates dropdown
        ttk.Label(selection_frame, text="Or Discovered Template:").pack(side=tk.LEFT, padx=(20, 5))
        self.test_template_var = tk.StringVar()
        self.test_template_combo = ttk.Combobox(selection_frame, textvariable=self.test_template_var, width=25)
        self.test_template_combo.pack(side=tk.LEFT, padx=5)

        # Header with controls
        header_frame = ttk.Frame(tab_frame)
        header_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(header_frame, text="Detection Testing:", font=('Arial', 12, 'bold')).pack(side=tk.LEFT)
        ttk.Button(header_frame, text="📷 LW Screenshot", command=self._take_lw_screenshot).pack(side=tk.RIGHT)
        ttk.Button(header_frame, text="📁 Load Image", command=self._load_test_image).pack(side=tk.RIGHT, padx=(0, 5))
        ttk.Button(header_frame, text="🔍 Test Selected", command=self._test_selected_template_detection).pack(side=tk.RIGHT, padx=(0, 5))
        ttk.Button(header_frame, text="🔍 Test All", command=self._test_all_templates).pack(side=tk.RIGHT, padx=(0, 5))
        ttk.Button(header_frame, text="🧹 Clear", command=self._clear_detection_results).pack(side=tk.RIGHT, padx=(0, 5))

        # Create paned window
        paned = ttk.PanedWindow(tab_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Test image display
        left_frame = ttk.Frame(paned)
        paned.add(left_frame, weight=2)

        image_frame = ttk.LabelFrame(left_frame, text="Test Image with Detections")
        image_frame.pack(fill=tk.BOTH, expand=True)

        # Canvas for image display with detections
        self.test_canvas = tk.Canvas(image_frame, bg='black')
        self.test_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Right panel - Detection results
        right_frame = ttk.Frame(paned)
        paned.add(right_frame, weight=1)

        results_frame = ttk.LabelFrame(right_frame, text="Detection Results")
        results_frame.pack(fill=tk.BOTH, expand=True)

        # Results tree
        result_columns = ('Template', 'Confidence', 'Position', 'Status')
        self.results_tree = ttk.Treeview(results_frame, columns=result_columns, show='headings', height=15)

        for col in result_columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=80)

        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        results_scroll = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        results_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.results_tree.configure(yscrollcommand=results_scroll.set)

        # Test controls
        controls_frame = ttk.Frame(right_frame)
        controls_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(controls_frame, text="Confidence Threshold:").pack(anchor=tk.W)
        self.test_threshold_var = tk.DoubleVar(value=0.7)
        threshold_scale = ttk.Scale(controls_frame, from_=0.1, to=1.0, variable=self.test_threshold_var,
                                  orient=tk.HORIZONTAL, command=self._on_threshold_change)
        threshold_scale.pack(fill=tk.X, pady=2)

        self.threshold_label = ttk.Label(controls_frame, text="0.7")
        self.threshold_label.pack(anchor=tk.W)

        # Status
        self.test_status_label = ttk.Label(controls_frame, text="Load an image to start testing")
        self.test_status_label.pack(anchor=tk.W, pady=(10, 0))

    def _create_scanner_settings_tab(self):
        """Create the scanner settings tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="⚙️ Scanner Settings")

        # Settings frame
        settings_frame = ttk.LabelFrame(tab_frame, text="Scanner Configuration")
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Scan interval
        ttk.Label(settings_frame, text="Scan Interval (seconds):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.scan_interval_var = tk.DoubleVar(value=1.0)
        scan_interval_spin = ttk.Spinbox(settings_frame, from_=0.1, to=10.0, increment=0.1,
                                       textvariable=self.scan_interval_var, width=10)
        scan_interval_spin.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Max concurrent detections
        ttk.Label(settings_frame, text="Max Concurrent Detections:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.max_concurrent_var = tk.IntVar(value=3)
        max_concurrent_spin = ttk.Spinbox(settings_frame, from_=1, to=10,
                                        textvariable=self.max_concurrent_var, width=10)
        max_concurrent_spin.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Template cache size
        ttk.Label(settings_frame, text="Template Cache Size:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.cache_size_var = tk.IntVar(value=100)
        cache_size_spin = ttk.Spinbox(settings_frame, from_=10, to=1000, increment=10,
                                    textvariable=self.cache_size_var, width=10)
        cache_size_spin.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        # Detection timeout
        ttk.Label(settings_frame, text="Detection Timeout (seconds):").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.detection_timeout_var = tk.DoubleVar(value=5.0)
        timeout_spin = ttk.Spinbox(settings_frame, from_=1.0, to=30.0, increment=1.0,
                                 textvariable=self.detection_timeout_var, width=10)
        timeout_spin.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)

        # Priority override enabled
        self.priority_override_var = tk.BooleanVar(value=True)
        priority_check = ttk.Checkbutton(settings_frame, text="Enable Priority Override",
                                       variable=self.priority_override_var)
        priority_check.grid(row=4, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        # Debug mode
        self.debug_mode_var = tk.BooleanVar(value=False)
        debug_check = ttk.Checkbutton(settings_frame, text="Enable Debug Mode",
                                    variable=self.debug_mode_var)
        debug_check.grid(row=5, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        # Save button
        ttk.Button(settings_frame, text="💾 Save Settings",
                  command=self._save_scanner_settings).grid(row=6, column=0, columnspan=2, pady=20)

    def _create_exclusion_rules_tab(self):
        """Create the exclusion rules tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🚫 Exclusion Rules")

        # Header
        header_frame = ttk.Frame(tab_frame)
        header_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(header_frame, text="Template Exclusion Rules:", font=('Arial', 12, 'bold')).pack(side=tk.LEFT)
        ttk.Button(header_frame, text="➕ Add Rule", command=self._add_exclusion_rule).pack(side=tk.RIGHT)

        # Rules list
        columns = ('Name', 'Primary Template', 'Excluded Templates', 'Enabled')
        self.exclusion_tree = ttk.Treeview(tab_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.exclusion_tree.heading(col, text=col)
            self.exclusion_tree.column(col, width=150)

        self.exclusion_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Rule details
        details_frame = ttk.LabelFrame(tab_frame, text="Rule Details")
        details_frame.pack(fill=tk.X, padx=5, pady=5)

        # Rule configuration controls would go here
        ttk.Label(details_frame, text="Select a rule to edit").pack(pady=10)

    # Event handlers and utility methods
    def _toggle_scanner_mode(self):
        """Toggle between centralized and legacy scanner modes"""
        self.main_controller.use_centralized_scanner = self.scanner_mode_var.get()
        mode = "Centralized" if self.scanner_mode_var.get() else "Legacy"
        self.logger.info(f"Scanner mode changed to: {mode}")

        # Update main GUI scanner mode indicator if it exists
        if hasattr(self.parent, '_update_scanner_mode_indicator'):
            self.parent._update_scanner_mode_indicator()

    def _refresh_all_data(self):
        """Refresh all data in all tabs"""
        self._refresh_template_mappings()
        self._refresh_discovered_templates()
        self._refresh_scanner_settings()
        self._refresh_exclusion_rules()
        self._refresh_detection_testing_dropdowns()

    def _refresh_template_mappings(self):
        """Refresh the template mappings tree"""
        # Clear existing items
        for item in self.template_tree.get_children():
            self.template_tree.delete(item)

        # Populate template name dropdown
        template_names = [t['name'] for t in self.discovered_templates]
        self.template_name_combo['values'] = template_names

        # Add current mappings
        try:
            mappings = self.centralized_scanner.template_mappings
            for mapping in mappings:
                values = (
                    mapping.priority,
                    mapping.template_name,
                    mapping.module,
                    mapping.action,
                    "✓" if mapping.enabled else "✗",
                    f"{mapping.threshold:.2f}",
                    f"{mapping.cooldown}s"
                )
                self.template_tree.insert('', tk.END, values=values)

            # Also refresh detection testing dropdowns
            self._refresh_detection_testing_dropdowns()

        except Exception as e:
            self.logger.error(f"Failed to refresh template mappings: {e}")

    def _refresh_discovered_templates(self):
        """Refresh the discovered templates list and clear all caches"""
        try:
            # Clear template preview cache to force reload of edited images
            self.template_preview_cache.clear()
            self.logger.info("🗑️ Cleared template preview cache")

            # Clear PIL image cache to ensure fresh image loading
            try:
                from PIL import Image
                Image.MAX_IMAGE_PIXELS = None  # Reset any limits
                # Force garbage collection to clear PIL internal caches
                import gc
                gc.collect()
                self.logger.info("🗑️ Cleared PIL image cache")
            except Exception as e:
                self.logger.warning(f"Could not clear PIL cache: {e}")

            # Clear screen scanner template cache if available
            if hasattr(self.main_controller, 'screen_scanner') and self.main_controller.screen_scanner:
                if hasattr(self.main_controller.screen_scanner, 'templates'):
                    template_count = len(self.main_controller.screen_scanner.templates)
                    # Reload templates to get fresh versions
                    self.main_controller.screen_scanner.templates = self.main_controller.screen_scanner._load_templates()
                    self.logger.info(f"🔄 Reloaded {template_count} templates in screen scanner")

            # Rediscover templates with fresh data
            self._discover_templates()

            # Clear and populate listbox
            self.discovered_listbox.delete(0, tk.END)
            for template in self.discovered_templates:
                display_text = f"{template['name']} ({template['size'][0]}x{template['size'][1]})"
                self.discovered_listbox.insert(tk.END, display_text)

            self.discovery_status_label.config(text=f"Found {len(self.discovered_templates)} templates")
            self.logger.info(f"✅ Refreshed template discovery - found {len(self.discovered_templates)} templates")

        except Exception as e:
            self.logger.error(f"Failed to refresh discovered templates: {e}")
            self.discovery_status_label.config(text="Error refreshing templates")

    def _refresh_scanner_settings(self):
        """Load current scanner settings into the form"""
        try:
            settings = self.centralized_scanner.scanner_settings
            self.scan_interval_var.set(settings.get('scan_interval', 1.0))
            self.max_concurrent_var.set(settings.get('max_concurrent_detections', 3))
            self.cache_size_var.set(settings.get('template_cache_size', 100))
            self.detection_timeout_var.set(settings.get('detection_timeout', 5.0))
            self.priority_override_var.set(settings.get('priority_override_enabled', True))
            self.debug_mode_var.set(settings.get('debug_mode', False))
        except Exception as e:
            self.logger.error(f"Failed to refresh scanner settings: {e}")

    def _refresh_exclusion_rules(self):
        """Refresh the exclusion rules tree"""
        # Clear existing items
        for item in self.exclusion_tree.get_children():
            self.exclusion_tree.delete(item)

        # Add current rules
        try:
            rules = self.centralized_scanner.exclusion_rules
            for rule in rules:
                excluded_str = ", ".join(rule.excluded_templates)
                values = (
                    rule.name,
                    rule.primary_template,
                    excluded_str,
                    "✓" if rule.enabled else "✗"
                )
                self.exclusion_tree.insert('', tk.END, values=values)
        except Exception as e:
            self.logger.error(f"Failed to refresh exclusion rules: {e}")

    def _on_template_select(self, event):
        """Handle template selection in the mappings tree"""
        selection = self.template_tree.selection()
        if not selection:
            return

        item = self.template_tree.item(selection[0])
        values = item['values']

        if len(values) >= 7:
            # Populate form with selected mapping data
            self.template_name_var.set(values[1])  # Template name
            self.priority_var.set(values[0])       # Priority
            self.module_var.set(values[2])         # Module
            self.action_var.set(values[3])         # Action
            self.enabled_var.set(values[4] == "✓") # Enabled

            # Load threshold and cooldown values
            try:
                threshold_str = values[5]  # Threshold (e.g., "0.80")
                self.threshold_var.set(float(threshold_str))
            except (ValueError, IndexError):
                self.threshold_var.set(0.8)  # Default fallback

            try:
                cooldown_str = values[6].rstrip('s')  # Cooldown (e.g., "0.0s" -> "0.0")
                self.cooldown_var.set(float(cooldown_str))
            except (ValueError, IndexError):
                self.cooldown_var.set(0.0)  # Default fallback

            # Load scan region data from configuration
            self._load_scan_region_data(values[1])  # Pass template name

            # Load template preview
            self._update_template_preview(values[1])

            # Update action dropdown based on module
            self._on_module_change()

    def _on_template_name_change(self, event=None):
        """Handle template name selection change"""
        template_name = self.template_name_var.get()
        self._update_template_preview(template_name)

    def _on_module_change(self, event=None):
        """Handle module selection change - update available actions"""
        module = self.module_var.get()
        if module in self.available_modules:
            actions = self.available_modules[module]
            self.action_combo['values'] = actions
            if actions and not self.action_var.get():
                self.action_var.set(actions[0])  # Set first action as default

    def _update_template_preview(self, template_name: str):
        """Update the template preview image"""
        if not template_name:
            self.template_preview_label.config(image='', text="No template selected")
            return

        # Find template file
        template_file = None
        for template in self.discovered_templates:
            if template['name'] == template_name:
                template_file = template['path']
                break

        if not template_file or not os.path.exists(template_file):
            self.template_preview_label.config(image='', text="Template file not found")
            return

        # Load and display preview
        try:
            preview_image = self._load_template_preview(template_file, (150, 150))
            if preview_image:
                self.template_preview_label.config(image=preview_image, text="")
                # Keep a reference to prevent garbage collection
                self.template_preview_label.image = preview_image
            else:
                self.template_preview_label.config(image='', text="Failed to load preview")
        except Exception as e:
            self.logger.error(f"Failed to update template preview: {e}")
            self.template_preview_label.config(image='', text="Preview error")

    def _on_discovered_template_select(self, event):
        """Handle selection in discovered templates list"""
        selection = self.discovered_listbox.curselection()
        if not selection:
            return

        index = selection[0]
        if index < len(self.discovered_templates):
            template = self.discovered_templates[index]

            # Update info text
            info_text = f"Name: {template['name']}\n"
            info_text += f"Size: {template['size'][0]}x{template['size'][1]}\n"
            info_text += f"Path: {template['path']}\n"

            self.template_info_text.delete(1.0, tk.END)
            self.template_info_text.insert(1.0, info_text)

            # Update preview
            preview_image = self._load_template_preview(template['path'], (200, 200))
            if preview_image:
                self.discovery_preview_label.config(image=preview_image, text="")
                self.discovery_preview_label.image = preview_image
            else:
                self.discovery_preview_label.config(image='', text="Failed to load preview")

    def _save_template_mapping(self):
        """Save the current template mapping"""
        try:
            # Validate inputs
            template_name = self.template_name_var.get().strip()
            if not template_name:
                messagebox.showerror("Error", "Please select a template name")
                return

            module = self.module_var.get().strip()
            if not module:
                messagebox.showerror("Error", "Please select a module")
                return

            action = self.action_var.get().strip()
            if not action:
                messagebox.showerror("Error", "Please select an action")
                return

            # Create new mapping data
            mapping_data = {
                "template_name": template_name,
                "priority": self.priority_var.get(),
                "module": module,
                "action": action,
                "description": self.description_var.get().strip() or f"{template_name} -> {module}.{action}",
                "enabled": self.enabled_var.get(),
                "threshold": self.threshold_var.get(),
                "cooldown": self.cooldown_var.get(),
                "scan_region": {
                    "x": self.region_x_var.get(),
                    "y": self.region_y_var.get(),
                    "width": self.region_width_var.get(),
                    "height": self.region_height_var.get(),
                    "description": self.region_desc_var.get().strip() or "Scan region"
                }
            }

            # Add or update mapping
            success = self._add_or_update_mapping(mapping_data)
            if success:
                # Keep window in front after save
                self._bring_window_to_front()
                self._refresh_template_mappings()
                # Ensure window stays in front
                self._bring_window_to_front()
            else:
                messagebox.showerror("Error", "Failed to save template mapping")
                self._bring_window_to_front()

        except Exception as e:
            self.logger.error(f"Failed to save template mapping: {e}")
            messagebox.showerror("Error", f"Failed to save template mapping: {str(e)}")
            self._bring_window_to_front()

    def _add_or_update_mapping(self, mapping_data: dict) -> bool:
        """Add or update a template mapping in the configuration"""
        try:
            # Load current configuration
            config_file = self.centralized_scanner.config_file
            with open(config_file, 'r') as f:
                config = json.load(f)

            # Find existing mapping or add new one
            mappings = config.get('template_mappings', [])
            existing_index = -1

            for i, mapping in enumerate(mappings):
                if mapping['template_name'] == mapping_data['template_name']:
                    existing_index = i
                    break

            if existing_index >= 0:
                # Update existing mapping
                mappings[existing_index] = mapping_data
            else:
                # Add new mapping
                mappings.append(mapping_data)

            # Save configuration
            config['template_mappings'] = mappings
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)

            # Reload scanner configuration
            self.centralized_scanner._load_configuration()
            return True

        except Exception as e:
            self.logger.error(f"Failed to add/update mapping: {e}")
            return False

    def _add_template_mapping(self):
        """Add a new template mapping"""
        # Clear form for new entry
        self.template_name_var.set("")
        self.priority_var.set(1)
        self.module_var.set("")
        self.action_var.set("")
        self.threshold_var.set(0.8)
        self.cooldown_var.set(0.0)
        self.enabled_var.set(True)
        self.description_var.set("")

        # Clear scan region fields
        self.region_x_var.set(0)
        self.region_y_var.set(0)
        self.region_width_var.set(2560)
        self.region_height_var.set(1440)
        self.region_desc_var.set("Full screen scan")

        # Clear preview
        self.template_preview_label.config(image='', text="Select a template")

    def _delete_template_mapping(self):
        """Delete selected template mapping"""
        selection = self.template_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template mapping to delete")
            return

        item = self.template_tree.item(selection[0])
        template_name = item['values'][1]

        if messagebox.askyesno("Confirm Delete", f"Delete mapping for '{template_name}'?"):
            try:
                self._remove_mapping(template_name)
                self._refresh_template_mappings()
            except Exception as e:
                self.logger.error(f"Failed to delete mapping: {e}")
                messagebox.showerror("Error", f"Failed to delete mapping: {str(e)}")

    def _remove_mapping(self, template_name: str):
        """Remove a template mapping from configuration"""
        config_file = self.centralized_scanner.config_file
        with open(config_file, 'r') as f:
            config = json.load(f)

        mappings = config.get('template_mappings', [])
        config['template_mappings'] = [m for m in mappings if m['template_name'] != template_name]

        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)

        # Reload scanner configuration
        self.centralized_scanner._load_configuration()

    def _remove_template_mapping(self, template_name: str):
        """Remove a template mapping from configuration (alias for _remove_mapping)"""
        return self._remove_mapping(template_name)

    def _edit_selected_template(self):
        """Edit selected template in external image editor (Paint)"""
        selection = self.discovered_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to edit")
            return

        if len(selection) > 1:
            messagebox.showwarning("Warning", "Please select only one template to edit")
            return

        # Get selected template
        index = selection[0]
        if index >= len(self.discovered_templates):
            messagebox.showerror("Error", "Invalid template selection")
            return

        template = self.discovered_templates[index]
        template_path = template['path']

        try:
            # Check if file exists
            if not os.path.exists(template_path):
                messagebox.showerror("Error", f"Template file not found: {template_path}")
                return

            # Try to open with Paint first (Windows)
            import subprocess
            import platform

            if platform.system() == "Windows":
                # Try Paint first
                try:
                    subprocess.Popen(['mspaint', template_path])
                    self.logger.info(f"Opened template '{template['name']}' in Paint for editing")
                    return
                except (FileNotFoundError, OSError):
                    pass

                # Fallback to default image viewer
                try:
                    os.startfile(template_path)
                    self.logger.info(f"Opened template '{template['name']}' in default image viewer")
                    return
                except OSError:
                    pass

            # Cross-platform fallback
            try:
                if platform.system() == "Darwin":  # macOS
                    subprocess.Popen(['open', template_path])
                elif platform.system() == "Linux":
                    subprocess.Popen(['xdg-open', template_path])
                else:
                    # Windows fallback
                    subprocess.Popen(['start', template_path], shell=True)

                self.logger.info(f"Opened template '{template['name']}' in system default application")

            except Exception as e:
                messagebox.showerror("Error", f"Could not open template for editing: {str(e)}")
                self.logger.error(f"Failed to open template for editing: {e}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to edit template: {str(e)}")
            self.logger.error(f"Failed to edit template: {e}")

    def _add_selected_templates(self):
        """Add selected discovered templates as new mappings"""
        selection = self.discovered_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select templates to add")
            return

        added_count = 0
        for index in selection:
            if index < len(self.discovered_templates):
                template = self.discovered_templates[index]

                # Create basic mapping
                mapping_data = {
                    "template_name": template['name'],
                    "priority": 10,  # Default priority
                    "module": "help_click",  # Default module
                    "action": "1_HelpButton",  # Default action
                    "description": f"Auto-added: {template['name']}",
                    "enabled": False,  # Disabled by default
                    "threshold": 0.8,
                    "cooldown": 0.0
                }

                if self._add_or_update_mapping(mapping_data):
                    added_count += 1

        if added_count > 0:
            self._refresh_template_mappings()
        else:
            messagebox.showerror("Error", "Failed to add any template mappings")

    def _save_scanner_settings(self):
        """Save scanner settings"""
        try:
            settings = {
                "scan_interval": self.scan_interval_var.get(),
                "max_concurrent_detections": self.max_concurrent_var.get(),
                "template_cache_size": self.cache_size_var.get(),
                "detection_timeout": self.detection_timeout_var.get(),
                "priority_override_enabled": self.priority_override_var.get(),
                "debug_mode": self.debug_mode_var.get()
            }

            # Update configuration file
            config_file = self.centralized_scanner.config_file
            with open(config_file, 'r') as f:
                config = json.load(f)

            config['scanner_settings'] = settings

            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)

            # Reload scanner configuration
            self.centralized_scanner._load_configuration()

            # Keep window in front
            self._bring_window_to_front()

        except Exception as e:
            self.logger.error(f"Failed to save scanner settings: {e}")
            messagebox.showerror("Error", f"Failed to save scanner settings: {str(e)}")
            self._bring_window_to_front()

    def _test_template_detection(self):
        """Test detection for selected template"""
        template_name = self.template_name_var.get()
        if not template_name:
            messagebox.showwarning("Warning", "Please select a template to test")
            return

        if not self.test_image:
            messagebox.showwarning("Warning", "Please load a test image first")
            return

        # Find template file
        template_path = None
        for template in self.discovered_templates:
            if template['name'] == template_name:
                template_path = template['path']
                break

        if not template_path:
            messagebox.showerror("Error", "Template file not found")
            return

        # Perform detection
        self._perform_template_detection(template_path, template_name)

    def _view_template_file(self):
        """View the selected template file"""
        template_name = self.template_name_var.get()
        if not template_name:
            messagebox.showwarning("Warning", "Please select a template to view")
            return

        # Find and open template file
        for template in self.discovered_templates:
            if template['name'] == template_name:
                try:
                    os.startfile(template['path'])  # Windows
                except:
                    # Fallback: copy path to clipboard or show in status
                    self.logger.info(f"Template location: {template['path']}")
                return

        messagebox.showerror("Error", "Template file not found")

    def _take_lw_screenshot(self):
        """Take a screenshot of the Last War-Survival Game window only (cache-only, no disk storage)"""
        try:
            # Find Last War-Survival Game window
            lw_window = self._find_lastwar_window()
            if not lw_window:
                messagebox.showerror("Error", "Last War-Survival Game window not found. Please make sure the game is running.")
                return

            # Get window title for confirmation
            window_title = win32gui.GetWindowText(lw_window)

            # Get window position and size
            rect = win32gui.GetWindowRect(lw_window)
            x, y, right, bottom = rect
            width = right - x
            height = bottom - y

            # Take screenshot of specific window area
            screenshot = pyautogui.screenshot(region=(x, y, width, height))

            # Convert to OpenCV format and store in memory only
            self.test_image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            # Display on canvas
            self._display_test_image()

            self.test_status_label.config(text=f"Screenshot captured from '{window_title}': {width}x{height}")
            self.logger.info(f"Screenshot captured from '{window_title}': {width}x{height}")

        except Exception as e:
            self.logger.error(f"Failed to take screenshot: {e}")
            messagebox.showerror("Error", f"Failed to take screenshot: {str(e)}")

    def _find_lastwar_window(self):
        """Find the Last War-Survival Game window handle"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                # Look specifically for "Last War-Survival Game"
                if "Last War-Survival Game" in window_title:
                    windows.append((hwnd, window_title))
                elif "Last War" in window_title and "Survival" in window_title:
                    windows.append((hwnd, window_title))
                elif "Last War" in window_title:
                    windows.append((hwnd, window_title))
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)

        if windows:
            # Prefer exact match first
            for hwnd, title in windows:
                if "Last War-Survival Game" in title:
                    self.logger.info(f"Found exact match: '{title}'")
                    return hwnd

            # Return first match if no exact match
            hwnd, title = windows[0]
            self.logger.info(f"Found window: '{title}'")
            return hwnd

        return None

    def _load_test_image(self):
        """Load an image for testing"""
        file_path = filedialog.askopenfilename(
            title="Select test image",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.bmp *.gif")]
        )
        if file_path:
            try:
                # Load image with OpenCV
                self.test_image = cv2.imread(file_path)
                if self.test_image is None:
                    messagebox.showerror("Error", "Failed to load image file")
                    return

                # Display on canvas
                self._display_test_image()

                self.test_status_label.config(text=f"Loaded: {os.path.basename(file_path)}")
                self.logger.info(f"Test image loaded: {file_path}")

            except Exception as e:
                self.logger.error(f"Failed to load test image: {e}")
                messagebox.showerror("Error", f"Failed to load image: {str(e)}")

    def _display_test_image(self):
        """Display the test image on canvas"""
        if self.test_image is None:
            return

        try:
            # Convert BGR to RGB for display
            display_image = cv2.cvtColor(self.test_image, cv2.COLOR_BGR2RGB)

            # Resize to fit canvas while maintaining aspect ratio
            canvas_width = self.test_canvas.winfo_width()
            canvas_height = self.test_canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                # Canvas not ready yet, try again later
                self.window.after(100, self._display_test_image)
                return

            img_height, img_width = display_image.shape[:2]

            # Calculate scaling factor
            scale_x = canvas_width / img_width
            scale_y = canvas_height / img_height
            scale = min(scale_x, scale_y, 1.0)  # Don't upscale

            new_width = int(img_width * scale)
            new_height = int(img_height * scale)

            # Resize image
            resized_image = cv2.resize(display_image, (new_width, new_height))

            # Convert to PIL and then to PhotoImage
            pil_image = Image.fromarray(resized_image)
            self.test_photo = ImageTk.PhotoImage(pil_image)

            # Clear canvas and display image
            self.test_canvas.delete("all")
            self.test_canvas.create_image(
                canvas_width // 2, canvas_height // 2,
                image=self.test_photo, anchor=tk.CENTER
            )

            # Store scale for detection box positioning
            self.image_scale = scale
            self.image_offset_x = (canvas_width - new_width) // 2
            self.image_offset_y = (canvas_height - new_height) // 2

        except Exception as e:
            self.logger.error(f"Failed to display test image: {e}")

    def _test_all_templates(self):
        """Test all templates against current image"""
        if not self.test_image:
            messagebox.showwarning("Warning", "Please load a test image first")
            return

        try:
            # Clear previous results
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

            self.test_status_label.config(text="Testing all templates...")
            self.window.update()

            threshold = self.test_threshold_var.get()
            detection_count = 0

            # Test each discovered template
            for template in self.discovered_templates:
                try:
                    result = self._detect_template(template['path'], template['name'], threshold)
                    if result:
                        detection_count += 1
                        confidence, position = result
                        status = "✅ DETECTED"

                        # Add to results tree
                        self.results_tree.insert('', tk.END, values=(
                            template['name'],
                            f"{confidence:.3f}",
                            f"({position[0]}, {position[1]})",
                            status
                        ))

                        # Draw detection box on image
                        self._draw_detection_box(template['name'], position, confidence)

                except Exception as e:
                    self.logger.error(f"Error testing template {template['name']}: {e}")

            self.test_status_label.config(text=f"Testing complete: {detection_count} templates detected")

        except Exception as e:
            self.logger.error(f"Failed to test all templates: {e}")
            messagebox.showerror("Error", f"Failed to test templates: {str(e)}")

    def _perform_template_detection(self, template_path: str, template_name: str):
        """Perform template detection on current test image"""
        try:
            threshold = self.test_threshold_var.get()
            result = self._detect_template(template_path, template_name, threshold)

            # Clear previous results for this template
            for item in self.results_tree.get_children():
                if self.results_tree.item(item)['values'][0] == template_name:
                    self.results_tree.delete(item)

            if result:
                confidence, position = result
                status = "✅ DETECTED"

                # Add to results tree
                self.results_tree.insert('', tk.END, values=(
                    template_name,
                    f"{confidence:.3f}",
                    f"({position[0]}, {position[1]})",
                    status
                ))

                # Draw detection box
                self._draw_detection_box(template_name, position, confidence)

                self.test_status_label.config(text=f"Template '{template_name}' detected with {confidence:.3f} confidence")
            else:
                # Add negative result
                self.results_tree.insert('', tk.END, values=(
                    template_name,
                    "< threshold",
                    "N/A",
                    "❌ NOT DETECTED"
                ))

                self.test_status_label.config(text=f"Template '{template_name}' not detected (below threshold)")

        except Exception as e:
            self.logger.error(f"Template detection failed: {e}")
            messagebox.showerror("Error", f"Detection failed: {str(e)}")

    def _detect_template(self, template_path: str, template_name: str, threshold: float) -> Optional[tuple]:
        """Detect template in test image using OpenCV template matching"""
        try:
            # Load template
            template = cv2.imread(template_path, cv2.IMREAD_COLOR)
            if template is None:
                self.logger.error(f"Failed to load template: {template_path}")
                return None

            # Convert both images to grayscale for matching
            gray_image = cv2.cvtColor(self.test_image, cv2.COLOR_BGR2GRAY)
            gray_template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)

            # Perform template matching
            result = cv2.matchTemplate(gray_image, gray_template, cv2.TM_CCOEFF_NORMED)

            # Find the best match
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            if max_val >= threshold:
                # Get template dimensions
                template_height, template_width = gray_template.shape

                # Calculate center position
                center_x = max_loc[0] + template_width // 2
                center_y = max_loc[1] + template_height // 2

                return (max_val, (center_x, center_y))

            return None

        except Exception as e:
            self.logger.error(f"Template detection error: {e}")
            return None

    def _draw_detection_box(self, template_name: str, position: tuple, confidence: float):
        """Draw detection box with template name and confidence on canvas"""
        try:
            if not hasattr(self, 'image_scale'):
                return

            # Scale position to canvas coordinates
            canvas_x = int(position[0] * self.image_scale) + self.image_offset_x
            canvas_y = int(position[1] * self.image_scale) + self.image_offset_y

            # Box size (scaled)
            box_size = int(30 * self.image_scale)

            # Draw green detection box
            self.test_canvas.create_rectangle(
                canvas_x - box_size, canvas_y - box_size,
                canvas_x + box_size, canvas_y + box_size,
                outline='lime', width=2, tags="detection"
            )

            # Draw template name and confidence
            label_text = f"{template_name}\n{confidence:.3f}"
            self.test_canvas.create_text(
                canvas_x, canvas_y - box_size - 20,
                text=label_text, fill='lime', font=('Arial', 8, 'bold'),
                anchor=tk.CENTER, tags="detection"
            )

        except Exception as e:
            self.logger.error(f"Failed to draw detection box: {e}")

    def _on_threshold_change(self, value):
        """Handle threshold slider change"""
        self.threshold_label.config(text=f"{float(value):.2f}")

        # If we have a test image, re-run detection with new threshold
        if hasattr(self, 'test_image') and self.test_image is not None:
            # Clear previous detection boxes
            self.test_canvas.delete("detection")

            # Re-run detection for visible results
            threshold = float(value)
            for item in self.results_tree.get_children():
                values = self.results_tree.item(item)['values']
                template_name = values[0]

                # Find template and re-detect
                for template in self.discovered_templates:
                    if template['name'] == template_name:
                        result = self._detect_template(template['path'], template_name, threshold)
                        if result:
                            confidence, position = result
                            self._draw_detection_box(template_name, position, confidence)
                        break

    def _add_exclusion_rule(self):
        """Add a new exclusion rule"""
        # Create a simple dialog for adding exclusion rules
        dialog = tk.Toplevel(self.window)
        dialog.title("Add Exclusion Rule")
        dialog.geometry("400x300")
        dialog.transient(self.window)
        dialog.grab_set()

        # Keep dialog in front
        dialog.lift()
        dialog.focus_set()

        ttk.Label(dialog, text="Create New Exclusion Rule", font=('Arial', 12, 'bold')).pack(pady=10)

        # Rule name
        ttk.Label(dialog, text="Rule Name:").pack(anchor=tk.W, padx=20)
        name_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=name_var, width=40).pack(padx=20, pady=5)

        # Primary template
        ttk.Label(dialog, text="Primary Template:").pack(anchor=tk.W, padx=20)
        primary_var = tk.StringVar()
        primary_combo = ttk.Combobox(dialog, textvariable=primary_var, width=37)
        primary_combo['values'] = [t['name'] for t in self.discovered_templates]
        primary_combo.pack(padx=20, pady=5)

        # Excluded templates
        ttk.Label(dialog, text="Excluded Templates (comma-separated):").pack(anchor=tk.W, padx=20)
        excluded_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=excluded_var, width=40).pack(padx=20, pady=5)

        # Description
        ttk.Label(dialog, text="Description:").pack(anchor=tk.W, padx=20)
        desc_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=desc_var, width=40).pack(padx=20, pady=5)

        def save_rule():
            try:
                rule_data = {
                    "name": name_var.get().strip(),
                    "description": desc_var.get().strip(),
                    "primary_template": primary_var.get().strip(),
                    "excluded_templates": [t.strip() for t in excluded_var.get().split(',') if t.strip()],
                    "enabled": True
                }

                if not all([rule_data["name"], rule_data["primary_template"], rule_data["excluded_templates"]]):
                    messagebox.showerror("Error", "Please fill in all required fields")
                    return

                # Add to configuration
                config_file = self.centralized_scanner.config_file
                with open(config_file, 'r') as f:
                    config = json.load(f)

                if 'exclusion_rules' not in config:
                    config['exclusion_rules'] = []

                config['exclusion_rules'].append(rule_data)

                with open(config_file, 'w') as f:
                    json.dump(config, f, indent=2)

                # Reload scanner configuration
                self.centralized_scanner._load_configuration()
                self._refresh_exclusion_rules()

                dialog.destroy()

            except Exception as e:
                self.logger.error(f"Failed to add exclusion rule: {e}")
                messagebox.showerror("Error", f"Failed to add rule: {str(e)}")

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)

        ttk.Button(button_frame, text="Save", command=save_rule).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

    def _bring_window_to_front(self):
        """Bring the scanner window to front and keep it focused"""
        try:
            # Lift window above all others
            self.window.lift()
            self.window.attributes('-topmost', True)

            # Focus the window
            self.window.focus_force()

            # Remove topmost after a short delay to allow normal window behavior
            self.window.after(100, lambda: self.window.attributes('-topmost', False))

        except Exception as e:
            self.logger.error(f"Failed to bring window to front: {e}")

    def _refresh_exclusion_rules(self):
        """Refresh the exclusion rules display"""
        try:
            # Clear existing content
            self.exclusion_text.delete(1.0, tk.END)

            # Load and display current exclusion rules
            rules_info = "Current Exclusion Rules:\n\n"
            for rule in self.centralized_scanner.exclusion_rules:
                status = "✅ Enabled" if rule.enabled else "❌ Disabled"
                rules_info += f"• {rule.name} ({status})\n"
                rules_info += f"  Description: {rule.description}\n"
                rules_info += f"  Primary: {rule.primary_template}\n"
                rules_info += f"  Excludes: {', '.join(rule.excluded_templates)}\n\n"

            self.exclusion_text.insert(tk.END, rules_info)

        except Exception as e:
            self.logger.error(f"Failed to refresh exclusion rules: {e}")

    def _clear_detection_results(self):
        """Clear detection results and visual indicators"""
        try:
            # Clear results tree
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

            # Clear detection boxes from canvas
            self.test_canvas.delete("detection")

            # Clear status
            self.test_status_label.config(text="Detection results cleared")

        except Exception as e:
            self.logger.error(f"Failed to clear detection results: {e}")

    def _export_configuration(self):
        """Export current configuration to a file"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="Export Configuration",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if file_path:
                # Load current configuration
                config_file = self.centralized_scanner.config_file
                with open(config_file, 'r') as f:
                    config = json.load(f)

                # Save to selected file
                with open(file_path, 'w') as f:
                    json.dump(config, f, indent=2)

                self._bring_window_to_front()
                self.logger.info(f"Configuration exported to: {file_path}")
                self._bring_window_to_front()

        except Exception as e:
            self.logger.error(f"Failed to export configuration: {e}")
            messagebox.showerror("Error", f"Failed to export configuration: {str(e)}")
            self._bring_window_to_front()

    def _import_configuration(self):
        """Import configuration from a file"""
        try:
            file_path = filedialog.askopenfilename(
                title="Import Configuration",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if file_path:
                # Load configuration from file
                with open(file_path, 'r') as f:
                    imported_config = json.load(f)

                # Validate configuration structure
                required_keys = ['template_mappings', 'exclusion_rules', 'scanner_settings']
                if not all(key in imported_config for key in required_keys):
                    messagebox.showerror("Error", "Invalid configuration file format")
                    return

                # Backup current configuration
                config_file = self.centralized_scanner.config_file
                backup_file = f"{config_file}.backup.{int(time.time())}"

                with open(config_file, 'r') as f:
                    current_config = json.load(f)

                with open(backup_file, 'w') as f:
                    json.dump(current_config, f, indent=2)

                # Save imported configuration
                with open(config_file, 'w') as f:
                    json.dump(imported_config, f, indent=2)

                # Reload scanner configuration
                self.centralized_scanner._load_configuration()

                # Refresh all displays
                self._refresh_template_mappings()
                self._refresh_exclusion_rules()
                self._load_scanner_settings()

                self._bring_window_to_front()

        except Exception as e:
            self.logger.error(f"Failed to import configuration: {e}")
            messagebox.showerror("Error", f"Failed to import configuration: {str(e)}")
            self._bring_window_to_front()

    def _save_all_configuration(self):
        """Save all configuration changes"""
        try:
            # Save scanner settings first
            self._save_scanner_settings()

            # Save any pending template mappings
            if hasattr(self, 'centralized_scanner'):
                self.centralized_scanner.save_configuration()

            self._bring_window_to_front()

        except Exception as e:
            self.logger.error(f"Failed to save all configuration: {e}")
            messagebox.showerror("Error", f"Failed to save configuration: {str(e)}")
            self._bring_window_to_front()

    def _reload_configuration(self):
        """Reload all configuration from files"""
        try:
            # Reload scanner configuration
            self.centralized_scanner._load_configuration()

            # Refresh all displays
            self._refresh_all_data()

            self._bring_window_to_front()
            self.logger.info("Configuration reloaded successfully")
            self._bring_window_to_front()

        except Exception as e:
            self.logger.error(f"Failed to reload configuration: {e}")
            messagebox.showerror("Error", f"Failed to reload configuration: {str(e)}")
            self._bring_window_to_front()

    def _load_scanner_settings(self):
        """Load scanner settings into the GUI controls"""
        try:
            settings = self.centralized_scanner.scanner_settings

            self.scan_interval_var.set(settings.get('scan_interval', 1.0))
            self.max_concurrent_var.set(settings.get('max_concurrent_detections', 3))
            self.cache_size_var.set(settings.get('template_cache_size', 100))
            self.detection_timeout_var.set(settings.get('detection_timeout', 10))
            self.priority_override_var.set(settings.get('priority_override_enabled', True))
            self.debug_mode_var.set(settings.get('debug_mode', False))

        except Exception as e:
            self.logger.error(f"Failed to load scanner settings: {e}")

    def _add_template_mapping(self):
        """Add a new template mapping"""
        try:
            # Create a simple dialog for adding new mappings
            dialog = tk.Toplevel(self.window)
            dialog.title("Add Template Mapping")
            dialog.geometry("400x300")
            dialog.transient(self.window)
            dialog.grab_set()
            dialog.lift()
            dialog.focus_set()

            ttk.Label(dialog, text="Add New Template Mapping", font=('Arial', 12, 'bold')).pack(pady=10)

            # Template selection
            ttk.Label(dialog, text="Template:").pack(anchor=tk.W, padx=20)
            template_var = tk.StringVar()
            template_combo = ttk.Combobox(dialog, textvariable=template_var, width=37)
            template_combo['values'] = [t['name'] for t in self.discovered_templates]
            template_combo.pack(padx=20, pady=5)

            # Module selection
            ttk.Label(dialog, text="Module:").pack(anchor=tk.W, padx=20)
            module_var = tk.StringVar()
            module_combo = ttk.Combobox(dialog, textvariable=module_var, width=37)
            module_combo['values'] = list(self.available_modules.keys())
            module_combo.pack(padx=20, pady=5)

            # Action selection
            ttk.Label(dialog, text="Action:").pack(anchor=tk.W, padx=20)
            action_var = tk.StringVar()
            action_combo = ttk.Combobox(dialog, textvariable=action_var, width=37)
            action_combo.pack(padx=20, pady=5)

            def update_actions(*args):
                module = module_var.get()
                if module in self.available_modules:
                    actions = self.available_modules[module]
                    action_combo['values'] = actions
                    if actions:
                        action_var.set(actions[0])

            module_var.trace('w', update_actions)

            def save_mapping():
                try:
                    mapping_data = {
                        "template_name": template_var.get().strip(),
                        "module": module_var.get().strip(),
                        "action": action_var.get().strip(),
                        "priority": 10,
                        "threshold": 0.8,
                        "cooldown": 0.0,
                        "enabled": True,
                        "description": f"Manual: {template_var.get()} -> {module_var.get()}.{action_var.get()}"
                    }

                    if not all([mapping_data["template_name"], mapping_data["module"], mapping_data["action"]]):
                        messagebox.showerror("Error", "Please fill in all fields")
                        return

                    if self._add_or_update_mapping(mapping_data):
                        self._refresh_template_mappings()
                        dialog.destroy()
                    else:
                        messagebox.showerror("Error", "Failed to add template mapping")

                except Exception as e:
                    messagebox.showerror("Error", f"Failed to add mapping: {str(e)}")

            # Buttons
            button_frame = ttk.Frame(dialog)
            button_frame.pack(pady=20)

            ttk.Button(button_frame, text="Save", command=save_mapping).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

        except Exception as e:
            self.logger.error(f"Failed to create add mapping dialog: {e}")
            messagebox.showerror("Error", f"Failed to create dialog: {str(e)}")

    def _delete_template_mapping(self):
        """Delete selected template mapping"""
        try:
            selection = self.template_tree.selection()
            if not selection:
                messagebox.showwarning("Warning", "Please select a template mapping to delete")
                return

            item = self.template_tree.item(selection[0])
            template_name = item['values'][1]  # Template name is in column 1

            result = messagebox.askyesno("Confirm Delete",
                                       f"Are you sure you want to delete the mapping for '{template_name}'?")
            if result:
                self._remove_template_mapping(template_name)
                self._refresh_template_mappings()

        except Exception as e:
            self.logger.error(f"Failed to delete template mapping: {e}")
            messagebox.showerror("Error", f"Failed to delete mapping: {str(e)}")

    def _test_selected_template_mapping(self):
        """Test the currently selected template mapping with automatic screenshot and detection"""
        try:
            template_name = self.template_name_var.get()
            if not template_name:
                messagebox.showwarning("Warning", "Please select a template to test")
                return

            # Show progress message
            self.logger.info(f"Testing template '{template_name}' - taking screenshot...")

            # Take screenshot of the Last War window
            self._take_lw_screenshot()

            # Check if screenshot was successful
            if not hasattr(self, 'test_image') or self.test_image is None:
                messagebox.showerror("Error", "Failed to capture screenshot from Last War window")
                return

            # Find template file
            template_path = None
            for template in self.discovered_templates:
                if template['name'] == template_name:
                    template_path = template['path']
                    break

            if not template_path:
                messagebox.showerror("Error", f"Template file not found for '{template_name}'")
                return

            # Switch to detection testing tab to show results
            self.notebook.select(2)  # Detection testing is tab index 2

            # Set the template in the detection testing tab
            self.test_template_var.set(template_name)

            # Perform detection and show results
            self._perform_template_detection(template_path, template_name)

            self.logger.info(f"Template test completed for '{template_name}'")

        except Exception as e:
            self.logger.error(f"Failed to test template mapping: {e}")
            messagebox.showerror("Error", f"Failed to test template: {str(e)}")

    def _test_selected_template_detection(self):
        """Test the selected template from the detection testing tab"""
        try:
            # Get selected template (priority: mapping > discovered template)
            template_name = self.test_mapping_var.get()
            if not template_name:
                template_name = self.test_template_var.get()

            if not template_name:
                messagebox.showwarning("Warning", "Please select a template to test")
                return

            # Check if we have a test image, if not try to take a screenshot
            if not hasattr(self, 'test_image') or self.test_image is None:
                self.logger.info("No test image available, taking screenshot...")
                self._take_lw_screenshot()

                # Wait a moment and try again
                if not hasattr(self, 'test_image') or self.test_image is None:
                    messagebox.showwarning("Warning", "Failed to capture screenshot. Please take a screenshot manually or load an image.")
                    return

            # Extract just the template name if it's from mapping (format: "template_name (priority: X)")
            if " (priority:" in template_name:
                template_name = template_name.split(" (priority:")[0]

            # Find template file
            template_path = None
            for template in self.discovered_templates:
                if template['name'] == template_name:
                    template_path = template['path']
                    break

            if not template_path:
                messagebox.showerror("Error", f"Template file not found for '{template_name}'")
                return

            # Perform detection
            self._perform_template_detection(template_path, template_name)

        except Exception as e:
            self.logger.error(f"Failed to test selected template: {e}")
            messagebox.showerror("Error", f"Failed to test template: {str(e)}")

    def _capture_scan_region(self):
        """Capture a scan region using a simple dialog approach"""
        try:
            # Simple approach - just show a dialog with instructions
            result = messagebox.askquestion("Capture Region",
                                          "Would you like to capture a scan region?\n\n"
                                          "This will open a simple capture dialog.\n"
                                          "Click Yes to continue or No to cancel.")

            if result == 'yes':
                self._show_simple_capture_dialog()

        except Exception as e:
            self.logger.error(f"Failed to start region capture: {e}")
            messagebox.showerror("Error", f"Failed to start region capture: {str(e)}")

    def _show_simple_capture_dialog(self):
        """Show a simple capture dialog with manual coordinate entry"""
        try:
            # Create a simple dialog for manual coordinate entry
            dialog = tk.Toplevel(self.window)
            dialog.title("Capture Scan Region")
            dialog.geometry("400x350+300+300")
            dialog.configure(bg='#2b2b2b')
            dialog.transient(self.window)
            dialog.grab_set()

            # Variables for coordinates
            x1_var = tk.IntVar(value=0)
            y1_var = tk.IntVar(value=0)
            x2_var = tk.IntVar(value=400)
            y2_var = tk.IntVar(value=300)

            # Title
            title_label = tk.Label(dialog, text="📷 Capture Scan Region",
                                 font=('Arial', 14, 'bold'), fg='white', bg='#2b2b2b')
            title_label.pack(pady=10)

            # Instructions
            instructions = tk.Label(dialog,
                                  text="Enter the coordinates for your scan region:\n"
                                       "Top-Left corner (X1, Y1) and Bottom-Right corner (X2, Y2)",
                                  font=('Arial', 10), fg='#cccccc', bg='#2b2b2b',
                                  wraplength=350, justify='center')
            instructions.pack(pady=10)

            # Coordinate inputs
            coord_frame = tk.Frame(dialog, bg='#2b2b2b')
            coord_frame.pack(pady=20)

            # Top-left coordinates
            tk.Label(coord_frame, text="Top-Left Corner:", font=('Arial', 10, 'bold'),
                    fg='white', bg='#2b2b2b').grid(row=0, column=0, columnspan=2, pady=5)

            tk.Label(coord_frame, text="X1:", fg='white', bg='#2b2b2b').grid(row=1, column=0, padx=5)
            tk.Entry(coord_frame, textvariable=x1_var, width=8).grid(row=1, column=1, padx=5)

            tk.Label(coord_frame, text="Y1:", fg='white', bg='#2b2b2b').grid(row=1, column=2, padx=5)
            tk.Entry(coord_frame, textvariable=y1_var, width=8).grid(row=1, column=3, padx=5)

            # Bottom-right coordinates
            tk.Label(coord_frame, text="Bottom-Right Corner:", font=('Arial', 10, 'bold'),
                    fg='white', bg='#2b2b2b').grid(row=2, column=0, columnspan=2, pady=(15,5))

            tk.Label(coord_frame, text="X2:", fg='white', bg='#2b2b2b').grid(row=3, column=0, padx=5)
            tk.Entry(coord_frame, textvariable=x2_var, width=8).grid(row=3, column=1, padx=5)

            tk.Label(coord_frame, text="Y2:", fg='white', bg='#2b2b2b').grid(row=3, column=2, padx=5)
            tk.Entry(coord_frame, textvariable=y2_var, width=8).grid(row=3, column=3, padx=5)

            # Current mouse position button
            def get_mouse_pos():
                try:
                    import pyautogui
                    x, y = pyautogui.position()
                    messagebox.showinfo("Mouse Position", f"Current mouse position: ({x}, {y})")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to get mouse position: {e}")

            mouse_btn = tk.Button(coord_frame, text="🖱️ Get Mouse Position",
                                command=get_mouse_pos, bg='#4CAF50', fg='white')
            mouse_btn.grid(row=4, column=0, columnspan=4, pady=15)

            # Buttons
            button_frame = tk.Frame(dialog, bg='#2b2b2b')
            button_frame.pack(pady=20)

            def apply_region():
                try:
                    x1, y1, x2, y2 = x1_var.get(), y1_var.get(), x2_var.get(), y2_var.get()

                    # Calculate proper region coordinates
                    region_x = min(x1, x2)
                    region_y = min(y1, y2)
                    region_width = abs(x2 - x1)
                    region_height = abs(y2 - y1)

                    # Update the main form
                    self.region_x_var.set(region_x)
                    self.region_y_var.set(region_y)
                    self.region_width_var.set(region_width)
                    self.region_height_var.set(region_height)
                    self.region_desc_var.set(f"Manual region ({region_x}, {region_y}) to ({region_x + region_width}, {region_y + region_height})")

                    dialog.destroy()
                    messagebox.showinfo("Success", f"Region set successfully!\n"
                                      f"X: {region_x}, Y: {region_y}\n"
                                      f"Width: {region_width}, Height: {region_height}")

                except Exception as e:
                    messagebox.showerror("Error", f"Failed to apply region: {e}")

            def cancel():
                dialog.destroy()

            tk.Button(button_frame, text="✅ Apply Region", command=apply_region,
                     bg='#4CAF50', fg='white', padx=20).pack(side=tk.LEFT, padx=10)
            tk.Button(button_frame, text="❌ Cancel", command=cancel,
                     bg='#f44336', fg='white', padx=20).pack(side=tk.LEFT, padx=10)

        except Exception as e:
            self.logger.error(f"Failed to show capture dialog: {e}")
            messagebox.showerror("Error", f"Failed to show capture dialog: {str(e)}")

    def _fallback_region_capture(self):
        """Simple fallback region capture method"""
        try:
            import pyautogui

            messagebox.showinfo("Simple Region Capture",
                              "This will set a default 400x300 region around your current mouse position.\n"
                              "You can adjust the coordinates manually after capture.")

            # Get current mouse position and create default region
            x, y = pyautogui.position()
            width, height = 400, 300
            x = max(0, x - width//2)
            y = max(0, y - height//2)

            # Update the region variables
            self.region_x_var.set(x)
            self.region_y_var.set(y)
            self.region_width_var.set(width)
            self.region_height_var.set(height)
            self.region_desc_var.set(f"Default region at ({x}, {y})")

            messagebox.showinfo("Region Set",
                              f"Region set to: X={x}, Y={y}, Width={width}, Height={height}\n"
                              "You can adjust these values manually if needed.")

        except Exception as e:
            self.logger.error(f"Failed fallback region capture: {e}")
            messagebox.showerror("Error", f"Failed to capture region: {str(e)}")



    def _fallback_region_capture(self):
        """Fallback region capture method if keyboard module is not available"""
        try:
            import pyautogui

            messagebox.showinfo("Fallback Capture",
                              "Keyboard module not available.\n"
                              "Using mouse position for a default 400x300 region.")

            # Get current mouse position and create default region
            x, y = pyautogui.position()
            width, height = 400, 300
            x = max(0, x - width//2)
            y = max(0, y - height//2)

            # Update the region variables
            self.region_x_var.set(x)
            self.region_y_var.set(y)
            self.region_width_var.set(width)
            self.region_height_var.set(height)
            self.region_desc_var.set(f"Default region at ({x}, {y})")

            # Show the window again
            self.window.deiconify()

            messagebox.showinfo("Region Set",
                              f"Region set to: X={x}, Y={y}, Width={width}, Height={height}\n"
                              "You can adjust these values manually if needed.")

        except Exception as e:
            self.window.deiconify()
            self.logger.error(f"Failed fallback region capture: {e}")
            messagebox.showerror("Error", f"Failed to capture region: {str(e)}")

    def _set_full_screen_region(self):
        """Set the scan region to full screen"""
        try:
            self.region_x_var.set(0)
            self.region_y_var.set(0)
            self.region_width_var.set(2560)
            self.region_height_var.set(1440)
            self.region_desc_var.set("Full screen scan")

        except Exception as e:
            self.logger.error(f"Failed to set full screen region: {e}")
            messagebox.showerror("Error", f"Failed to set full screen region: {str(e)}")

    def _load_scan_region_data(self, template_name: str):
        """Load scan region data for the selected template"""
        try:
            # Load configuration file
            config_file = self.centralized_scanner.config_file
            with open(config_file, 'r') as f:
                config = json.load(f)

            # Find the template mapping
            mappings = config.get('template_mappings', [])
            for mapping in mappings:
                if mapping.get('template_name') == template_name:
                    # Load scan region data
                    scan_region = mapping.get('scan_region', {})

                    self.region_x_var.set(scan_region.get('x', 0))
                    self.region_y_var.set(scan_region.get('y', 0))
                    self.region_width_var.set(scan_region.get('width', 2560))
                    self.region_height_var.set(scan_region.get('height', 1440))
                    self.region_desc_var.set(scan_region.get('description', 'Full screen scan'))

                    # Also load description from mapping
                    self.description_var.set(mapping.get('description', ''))
                    return

            # If template not found, set defaults
            self.region_x_var.set(0)
            self.region_y_var.set(0)
            self.region_width_var.set(2560)
            self.region_height_var.set(1440)
            self.region_desc_var.set('Full screen scan')
            self.description_var.set('')

        except Exception as e:
            self.logger.error(f"Failed to load scan region data: {e}")
            # Set defaults on error
            self.region_x_var.set(0)
            self.region_y_var.set(0)
            self.region_width_var.set(2560)
            self.region_height_var.set(1440)
            self.region_desc_var.set('Full screen scan')
            self.description_var.set('')

    def _on_test_mapping_select(self, event=None):
        """Handle template mapping selection in detection testing"""
        try:
            # Clear the discovered template selection when mapping is selected
            if self.test_mapping_var.get():
                self.test_template_var.set("")
        except Exception as e:
            self.logger.error(f"Error in mapping selection: {e}")

    def _refresh_detection_testing_dropdowns(self):
        """Refresh the dropdown lists in detection testing tab"""
        try:
            # Refresh template mappings (sorted by priority)
            mappings = []
            for mapping in sorted(self.centralized_scanner.template_mappings, key=lambda x: x.priority):
                status = "✅" if mapping.enabled else "❌"
                mappings.append(f"{mapping.template_name} (priority: {mapping.priority}) {status}")

            self.test_mapping_combo['values'] = mappings

            # Refresh discovered templates
            template_names = [t['name'] for t in self.discovered_templates]
            self.test_template_combo['values'] = template_names

        except Exception as e:
            self.logger.error(f"Failed to refresh detection testing dropdowns: {e}")


