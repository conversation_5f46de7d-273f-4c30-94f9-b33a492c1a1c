"""
Comprehensive Test Suite for the Dig Module Implementation
Validates workflow, template detection, coordinate accuracy, and integration
"""

import sys
import os
import time
import logging
from typing import Dict, Any

# Add modules directory to path
sys.path.append('modules')
sys.path.append('.')

from modules.dig import DigModule
from unified_config_manager import UnifiedConfigManager


def setup_test_logging():
    """Setup logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_dig_comprehensive.log')
        ]
    )


def test_dig_module_initialization():
    """Test dig module initialization"""
    print("=" * 60)
    print("Testing Dig Module Initialization")
    print("=" * 60)
    
    try:
        dig_module = DigModule()
        
        # Test basic properties
        assert dig_module.name == "dig", f"Expected name 'dig', got '{dig_module.name}'"
        assert dig_module.priority == -1, f"Expected priority -1, got {dig_module.priority}"
        assert dig_module.enabled == True, f"Expected enabled True, got {dig_module.enabled}"
        
        # Test configuration loading
        assert hasattr(dig_module, 'rapid_click_speed'), "Missing rapid_click_speed attribute"
        assert hasattr(dig_module, 'rapid_click_duration'), "Missing rapid_click_duration attribute"
        assert hasattr(dig_module, 'timer_threshold'), "Missing timer_threshold attribute"
        assert hasattr(dig_module, 'thank_you_message'), "Missing thank_you_message attribute"
        
        # Test default values
        assert dig_module.rapid_click_speed == 0.005, f"Expected speed 0.005, got {dig_module.rapid_click_speed}"
        assert dig_module.rapid_click_duration == 20.0, f"Expected duration 20.0, got {dig_module.rapid_click_duration}"
        assert dig_module.timer_threshold == 10, f"Expected threshold 10, got {dig_module.timer_threshold}"
        
        print("✓ Initialization test passed")
        return True
        
    except Exception as e:
        print(f"✗ Initialization test failed: {str(e)}")
        return False


def test_template_detection():
    """Test template detection logic"""
    print("=" * 60)
    print("Testing Template Detection")
    print("=" * 60)
    
    try:
        dig_module = DigModule()
        
        # Test required templates
        required_templates = dig_module.get_required_templates()
        expected_templates = [
            'dig_icon', 'dig_up_dropdown', 'test_flight_treasure', 'dig_up_treasure',
            'alliance_chat_is_on', 'send_to_dig_1', 'send_to_dig_2', 'send_to_dig_3',
            'send_to_dig_flight', 'send_to_dig_flight_2', 'march_to_dig', 'select_tile',
            'quit_game_dialog'
        ]
        
        for template in expected_templates:
            assert template in required_templates, f"Missing required template: {template}"
        
        # Test trigger detection
        test_screen_data = {
            'templates_found': {
                'dig_icon': [{'x': 100, 'y': 100, 'w': 50, 'h': 50}]
            }
        }
        
        can_execute = dig_module.custom_can_execute(test_screen_data)
        assert can_execute == True, "Should be able to execute when dig_icon detected"
        
        # Test no trigger detection
        empty_screen_data = {'templates_found': {}}
        can_execute = dig_module.custom_can_execute(empty_screen_data)
        assert can_execute == False, "Should not execute when no triggers detected"
        
        print("✓ Template detection test passed")
        return True
        
    except Exception as e:
        print(f"✗ Template detection test failed: {str(e)}")
        return False


def test_configuration_methods():
    """Test configuration methods"""
    print("=" * 60)
    print("Testing Configuration Methods")
    print("=" * 60)
    
    try:
        dig_module = DigModule()
        
        # Test rapid click speed setting
        dig_module.set_rapid_click_speed(0.01)
        assert dig_module.rapid_click_speed == 0.01, f"Expected speed 0.01, got {dig_module.rapid_click_speed}"
        
        # Test clamping
        dig_module.set_rapid_click_speed(2.0)  # Should clamp to 1.0
        assert dig_module.rapid_click_speed == 1.0, f"Expected clamped speed 1.0, got {dig_module.rapid_click_speed}"
        
        dig_module.set_rapid_click_speed(0.0001)  # Should clamp to 0.001
        assert dig_module.rapid_click_speed == 0.001, f"Expected clamped speed 0.001, got {dig_module.rapid_click_speed}"
        
        # Test duration setting
        dig_module.set_rapid_click_duration(30.0)
        assert dig_module.rapid_click_duration == 30.0, f"Expected duration 30.0, got {dig_module.rapid_click_duration}"
        
        # Test threshold setting
        dig_module.set_timer_threshold(15)
        assert dig_module.timer_threshold == 15, f"Expected threshold 15, got {dig_module.timer_threshold}"
        
        # Test message setting
        test_message = "Test completion message"
        dig_module.set_thank_you_message(test_message)
        assert dig_module.thank_you_message == test_message, f"Expected message '{test_message}', got '{dig_module.thank_you_message}'"
        
        # Test info method
        info = dig_module.get_rapid_click_info()
        assert isinstance(info, dict), "get_rapid_click_info should return dict"
        assert 'speed' in info, "Info should contain speed"
        assert 'duration' in info, "Info should contain duration"
        assert 'expected_clicks' in info, "Info should contain expected_clicks"
        
        print("✓ Configuration methods test passed")
        return True
        
    except Exception as e:
        print(f"✗ Configuration methods test failed: {str(e)}")
        return False


def test_status_reporting():
    """Test status reporting"""
    print("=" * 60)
    print("Testing Status Reporting")
    print("=" * 60)
    
    try:
        dig_module = DigModule()
        
        # Test initial status
        status = dig_module.get_dig_status()
        assert status == "Ready", f"Expected status 'Ready', got '{status}'"
        
        # Test in-progress status
        dig_module.dig_in_progress = True
        dig_module.current_step = dig_module.STEP_OPEN_CHAT
        status = dig_module.get_dig_status()
        assert "Opening Chat" in status, f"Expected 'Opening Chat' in status, got '{status}'"
        
        # Test different steps
        dig_module.current_step = dig_module.STEP_TIMER_MANAGEMENT
        status = dig_module.get_dig_status()
        assert "Managing Timer" in status, f"Expected 'Managing Timer' in status, got '{status}'"
        
        print("✓ Status reporting test passed")
        return True
        
    except Exception as e:
        print(f"✗ Status reporting test failed: {str(e)}")
        return False


def test_error_handling():
    """Test error handling mechanisms"""
    print("=" * 60)
    print("Testing Error Handling")
    print("=" * 60)
    
    try:
        dig_module = DigModule()
        
        # Test direct click method
        import pyautogui
        original_click = pyautogui.click
        def mock_click(x, y):
            pass  # Mock click for testing
        pyautogui.click = mock_click

        # This should not crash even if click fails
        pyautogui.click(0, 0)

        # Restore original click
        pyautogui.click = original_click
        
        # Test emergency recovery
        dig_module._emergency_recovery("test recovery")
        # Should reset state
        assert dig_module.dig_in_progress == False, "Emergency recovery should reset dig_in_progress"
        
        print("✓ Error handling test passed")
        return True
        
    except Exception as e:
        print(f"✗ Error handling test failed: {str(e)}")
        return False


def test_unified_config_integration():
    """Test integration with unified configuration"""
    print("=" * 60)
    print("Testing Unified Config Integration")
    print("=" * 60)
    
    try:
        config_manager = UnifiedConfigManager()
        config = config_manager.get_config()
        
        # Check if dig module config exists
        assert 'modules' in config, "Config should contain modules section"
        assert 'dig' in config['modules'], "Config should contain dig module"
        
        dig_config = config['modules']['dig']
        assert 'settings' in dig_config, "Dig config should contain settings"
        assert 'templates' in dig_config, "Dig config should contain templates"
        assert 'coordinates' in dig_config, "Dig config should contain coordinates"
        
        # Check settings
        settings = dig_config['settings']
        assert 'rapid_click_speed' in settings, "Settings should contain rapid_click_speed"
        assert 'rapid_click_duration' in settings, "Settings should contain rapid_click_duration"
        assert 'timer_threshold' in settings, "Settings should contain timer_threshold"
        assert 'thank_you_message' in settings, "Settings should contain thank_you_message"
        
        # Check coordinates
        coordinates = dig_config['coordinates']
        expected_coords = ['open_chat', 'alliance_chat', 'navigation_click', 'send_squad', 
                          'confirm_deployment', 'chat_input', 'rapid_click_target']
        
        for coord in expected_coords:
            assert coord in coordinates, f"Missing coordinate: {coord}"
        
        print("✓ Unified config integration test passed")
        return True
        
    except Exception as e:
        print(f"✗ Unified config integration test failed: {str(e)}")
        return False


def run_all_tests():
    """Run all tests"""
    print("Starting Comprehensive Dig Module Test Suite")
    print("=" * 60)
    
    setup_test_logging()
    
    tests = [
        test_dig_module_initialization,
        test_template_detection,
        test_configuration_methods,
        test_status_reporting,
        test_error_handling,
        test_unified_config_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"Test {test.__name__} crashed: {str(e)}")
            failed += 1
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed} passed, {failed} failed")
    print("=" * 60)
    
    if failed == 0:
        print("🎉 All tests passed! Dig module implementation is ready.")
        print("\n📋 Next Steps:")
        print("1. Ensure all required template images are in templates/ folder")
        print("2. Test with actual game screenshots")
        print("3. Fine-tune coordinates and timing as needed")
        print("4. Integrate with main controller for live testing")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
    
    return failed == 0


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
