"""
Dig Module Configuration GUI
Provides interface for configuring dig module settings including rapid click interval and completion message
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
from typing import Dict, Any
from unified_config_manager import UnifiedConfigManager


class DigConfigGUI:
    """GUI for configuring dig module settings"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.config_manager = UnifiedConfigManager()
        self.window = None
        self.vars = {}
        
    def show_config_window(self):
        """Show the dig configuration window"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("Dig Module Configuration")
        self.window.geometry("500x400")
        self.window.resizable(True, True)
        
        # Load current configuration
        self._load_current_config()
        
        # Create GUI elements
        self._create_widgets()
        
        # Center window
        self.window.transient(self.parent)
        self.window.grab_set()
        
    def _load_current_config(self):
        """Load current dig module configuration"""
        try:
            config = self.config_manager.get_module_config("dig")
            if config:
                settings = config.get("settings", {})
                self.current_config = {
                    'rapid_click_speed': settings.get('rapid_click_speed', 0.005),
                    'rapid_click_duration': settings.get('rapid_click_duration', 20.0),
                    'timer_threshold': settings.get('timer_threshold', 10),
                    'thank_you_message': settings.get('thank_you_message', 'Thank you for the dig!')
                }
            else:
                # Default configuration
                self.current_config = {
                    'rapid_click_speed': 0.005,
                    'rapid_click_duration': 20.0,
                    'timer_threshold': 10,
                    'thank_you_message': 'Thank you for the dig!'
                }
        except Exception as e:
            print(f"Error loading dig config: {e}")
            self.current_config = {
                'rapid_click_speed': 0.005,
                'rapid_click_duration': 20.0,
                'timer_threshold': 10,
                'thank_you_message': 'Thank you for the dig!'
            }
    
    def _create_widgets(self):
        """Create GUI widgets"""
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        row = 0
        
        # Title
        title_label = ttk.Label(main_frame, text="Dig Module Configuration", 
                               font=('Arial', 14, 'bold'))
        title_label.grid(row=row, column=0, columnspan=2, pady=(0, 20))
        row += 1
        
        # Rapid Click Speed
        ttk.Label(main_frame, text="Rapid Click Interval (seconds):").grid(
            row=row, column=0, sticky=tk.W, pady=5)
        self.vars['rapid_click_speed'] = tk.DoubleVar(value=self.current_config['rapid_click_speed'])
        speed_frame = ttk.Frame(main_frame)
        speed_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5)
        
        speed_spinbox = ttk.Spinbox(speed_frame, from_=0.001, to=1.0, increment=0.001,
                                   textvariable=self.vars['rapid_click_speed'],
                                   width=10, format="%.3f")
        speed_spinbox.pack(side=tk.LEFT)
        
        # Calculate and display clicks per second
        self.clicks_per_sec_label = ttk.Label(speed_frame, text="")
        self.clicks_per_sec_label.pack(side=tk.LEFT, padx=(10, 0))
        self._update_clicks_display()
        
        # Bind to update display when value changes
        self.vars['rapid_click_speed'].trace('w', lambda *args: self._update_clicks_display())
        row += 1
        
        # Rapid Click Duration
        ttk.Label(main_frame, text="Rapid Click Duration (seconds):").grid(
            row=row, column=0, sticky=tk.W, pady=5)
        self.vars['rapid_click_duration'] = tk.DoubleVar(value=self.current_config['rapid_click_duration'])
        ttk.Spinbox(main_frame, from_=1.0, to=60.0, increment=1.0,
                   textvariable=self.vars['rapid_click_duration'],
                   width=10).grid(row=row, column=1, sticky=tk.W, pady=5)
        row += 1
        
        # Timer Threshold
        ttk.Label(main_frame, text="Timer Threshold (seconds):").grid(
            row=row, column=0, sticky=tk.W, pady=5)
        self.vars['timer_threshold'] = tk.IntVar(value=self.current_config['timer_threshold'])
        ttk.Spinbox(main_frame, from_=1, to=30, increment=1,
                   textvariable=self.vars['timer_threshold'],
                   width=10).grid(row=row, column=1, sticky=tk.W, pady=5)
        row += 1
        
        # Thank You Message
        ttk.Label(main_frame, text="Completion Message:").grid(
            row=row, column=0, sticky=(tk.W, tk.N), pady=5)
        self.vars['thank_you_message'] = tk.StringVar(value=self.current_config['thank_you_message'])
        message_text = tk.Text(main_frame, height=3, width=40)
        message_text.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5)
        message_text.insert('1.0', self.current_config['thank_you_message'])
        self.message_text = message_text
        row += 1
        
        # Info section
        info_frame = ttk.LabelFrame(main_frame, text="Information", padding="5")
        info_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(20, 10))
        info_frame.columnconfigure(0, weight=1)
        
        info_text = ("• Rapid Click Interval: Time between clicks during timer phase\n"
                    "• Duration: How long to rapid click when timer is low\n"
                    "• Threshold: Start rapid clicking when timer ≤ this value\n"
                    "• Message: Sent to alliance chat after dig completion")
        ttk.Label(info_frame, text=info_text, justify=tk.LEFT).grid(row=0, column=0, sticky=tk.W)
        row += 1
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, columnspan=2, pady=(20, 0))
        
        ttk.Button(button_frame, text="Save", command=self._save_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Reset to Defaults", command=self._reset_defaults).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=self.window.destroy).pack(side=tk.LEFT)
        
    def _update_clicks_display(self):
        """Update the clicks per second display"""
        try:
            speed = self.vars['rapid_click_speed'].get()
            if speed > 0:
                clicks_per_sec = 1.0 / speed
                self.clicks_per_sec_label.config(text=f"({clicks_per_sec:.1f} clicks/sec)")
            else:
                self.clicks_per_sec_label.config(text="")
        except:
            self.clicks_per_sec_label.config(text="")
    
    def _save_config(self):
        """Save the configuration"""
        try:
            # Get values from GUI
            new_config = {
                'rapid_click_speed': self.vars['rapid_click_speed'].get(),
                'rapid_click_duration': self.vars['rapid_click_duration'].get(),
                'timer_threshold': self.vars['timer_threshold'].get(),
                'thank_you_message': self.message_text.get('1.0', tk.END).strip()
            }
            
            # Validate values
            if new_config['rapid_click_speed'] <= 0:
                messagebox.showerror("Error", "Rapid click speed must be greater than 0")
                return
            
            if new_config['rapid_click_duration'] <= 0:
                messagebox.showerror("Error", "Rapid click duration must be greater than 0")
                return
                
            if new_config['timer_threshold'] <= 0:
                messagebox.showerror("Error", "Timer threshold must be greater than 0")
                return
            
            # Update configuration
            config = self.config_manager.get_config()
            if 'modules' not in config:
                config['modules'] = {}
            if 'dig' not in config['modules']:
                config['modules']['dig'] = {}
            if 'settings' not in config['modules']['dig']:
                config['modules']['dig']['settings'] = {}
                
            config['modules']['dig']['settings'].update(new_config)
            
            # Save configuration
            self.config_manager.save_config(config)
            
            messagebox.showinfo("Success", "Dig module configuration saved successfully!")
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {str(e)}")
    
    def _reset_defaults(self):
        """Reset to default values"""
        defaults = {
            'rapid_click_speed': 0.005,
            'rapid_click_duration': 20.0,
            'timer_threshold': 10,
            'thank_you_message': 'Thank you for the dig!'
        }
        
        self.vars['rapid_click_speed'].set(defaults['rapid_click_speed'])
        self.vars['rapid_click_duration'].set(defaults['rapid_click_duration'])
        self.vars['timer_threshold'].set(defaults['timer_threshold'])
        self.message_text.delete('1.0', tk.END)
        self.message_text.insert('1.0', defaults['thank_you_message'])


def main():
    """Test the GUI"""
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    gui = DigConfigGUI(root)
    gui.show_config_window()
    
    root.mainloop()


if __name__ == "__main__":
    main()
