#!/usr/bin/env python3
"""
Test script to verify the Map Trade button functionality
"""

def test_map_trade_button_integration():
    """Test that the map trade button has the new functionality"""
    print("=" * 60)
    print("TESTING MAP TRADE BUTTON INTEGRATION")
    print("=" * 60)
    
    try:
        # Test 1: Import GUI module
        print("📦 Testing GUI import...")
        from gui import LastWarGUI
        print("✅ GUI import successful")
        
        # Test 2: Check if the new method exists
        print("\n🔍 Checking for new ESC sequence method...")
        if hasattr(LastWarGUI, '_execute_esc_to_main_panel'):
            print("✅ _execute_esc_to_main_panel method exists")
        else:
            print("❌ _execute_esc_to_main_panel method missing")
            return False
        
        # Test 3: Check if pyautogui is imported
        print("\n🔍 Checking pyautogui import...")
        import gui
        if hasattr(gui, 'pyautogui'):
            print("✅ pyautogui imported in GUI module")
        else:
            print("❌ pyautogui not imported in GUI module")
            return False
        
        # Test 4: Check the modified trigger method
        print("\n🔍 Checking modified _trigger_map_trade_no_popup method...")
        import inspect
        source = inspect.getsource(LastWarGUI._trigger_map_trade_no_popup)
        
        if "_execute_esc_to_main_panel" in source:
            print("✅ ESC sequence call found in trigger method")
        else:
            print("❌ ESC sequence call not found in trigger method")
            return False
            
        if "_focus_and_resize_game_window" in source:
            print("✅ Window focus call found in trigger method")
        else:
            print("❌ Window focus call not found in trigger method")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED!")
        print("✅ Map Trade button now includes:")
        print("   • Game window focus and resize")
        print("   • ESC sequence to reach main panel")
        print("   • Map trade module execution")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_map_trade_button_integration()
    
    if success:
        print("\n🚀 INTEGRATION READY!")
        print("The Map Trade button is now enhanced with:")
        print("1. Automatic game window focus and resize")
        print("2. ESC sequence to ensure main panel")
        print("3. Safe map trade module execution")
    else:
        print("\n❌ INTEGRATION FAILED!")
        print("Please check the error messages above.")
