#!/usr/bin/env python3
"""
Test script to verify all fixes are working
"""
import os

def test_focus_game_dimensions():
    """Test that Focus Game button uses correct dimensions"""
    print("Testing Focus Game button dimensions...")
    
    try:
        with open("gui.py", 'r') as f:
            content = f.read()
        
        if "target_width = 2560" in content and "target_height = 1440" in content:
            print("  ✅ Focus Game button uses correct 2560x1440 dimensions")
            return True
        else:
            print("  ❌ Focus Game button dimensions incorrect")
            return False
            
    except Exception as e:
        print(f"  ❌ Error checking Focus Game dimensions: {str(e)}")
        return False

def test_config_helper_dimensions():
    """Test that Configuration Helper uses correct dimensions"""
    print("Testing Configuration Helper dimensions...")
    
    try:
        with open("config_helper.py", 'r') as f:
            content = f.read()
        
        if "target_width = 2560" in content and "target_height = 1440" in content:
            print("  ✅ Configuration Helper uses correct 2560x1440 dimensions")
            return True
        else:
            print("  ❌ Configuration Helper dimensions incorrect")
            return False
            
    except Exception as e:
        print(f"  ❌ Error checking Configuration Helper dimensions: {str(e)}")
        return False

def test_gear_buttons():
    """Test that gear buttons call Configuration Helper"""
    print("Testing gear buttons...")
    
    try:
        with open("gui.py", 'r') as f:
            content = f.read()
        
        if "command=self._open_configuration" in content:
            print("  ✅ Gear buttons call Configuration Helper")
            return True
        else:
            print("  ❌ Gear buttons still call old config editor")
            return False
            
    except Exception as e:
        print(f"  ❌ Error checking gear buttons: {str(e)}")
        return False

def test_popup_removal():
    """Test that popups are removed"""
    print("Testing popup removal...")
    
    try:
        with open("gui.py", 'r') as f:
            gui_content = f.read()
        
        with open("config_helper.py", 'r') as f:
            helper_content = f.read()
        
        # Check Focus Game popup removal
        focus_popup_removed = "messagebox.showinfo(\"Game Focus Complete\"" not in gui_content
        
        # Check test coordinate popup removal  
        test_popup_removed = "messagebox.askyesno(\"Test Click Coordinate\"" not in helper_content
        
        if focus_popup_removed and test_popup_removed:
            print("  ✅ All popups removed")
            return True
        else:
            if not focus_popup_removed:
                print("  ❌ Focus Game popup still exists")
            if not test_popup_removed:
                print("  ❌ Test coordinate popup still exists")
            return False
            
    except Exception as e:
        print(f"  ❌ Error checking popup removal: {str(e)}")
        return False

def test_legacy_module_loading():
    """Test that Configuration Helper loads legacy modules"""
    print("Testing legacy module loading...")
    
    try:
        with open("config_helper.py", 'r') as f:
            content = f.read()
        
        if "_load_legacy_modules" in content and "_convert_legacy_module" in content:
            print("  ✅ Configuration Helper has legacy module loading")
            return True
        else:
            print("  ❌ Configuration Helper missing legacy module loading")
            return False
            
    except Exception as e:
        print(f"  ❌ Error checking legacy module loading: {str(e)}")
        return False

def test_files_exist():
    """Test that all required files exist"""
    print("Testing required files...")
    
    files = [
        "gui.py",
        "config_helper.py", 
        "unified_config.json",
        "module_configs.json"
    ]
    
    all_exist = True
    for file_path in files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} missing")
            all_exist = False
    
    return all_exist

def main():
    """Run all tests"""
    print("🔍 Testing Final Configuration Fixes")
    print("=" * 50)
    
    results = []
    
    results.append(test_files_exist())
    results.append(test_focus_game_dimensions())
    results.append(test_config_helper_dimensions())
    results.append(test_gear_buttons())
    results.append(test_popup_removal())
    results.append(test_legacy_module_loading())
    
    print("\n📊 Test Results:")
    print("=" * 30)
    
    if all(results):
        print("🎉 All tests passed! All fixes are working correctly.")
        print("\n✅ SUMMARY OF FIXES:")
        print("1. Focus Game button uses correct 2560x1440 dimensions")
        print("2. Configuration Helper uses correct 2560x1440 dimensions")
        print("3. Gear buttons now open Configuration Helper (unified panel)")
        print("4. All popups removed from Focus Game and test coordinate buttons")
        print("5. Configuration Helper loads both unified and legacy modules")
        print("\n🎯 WHAT'S FIXED:")
        print("• No more hardcoded 1260x1300 resize")
        print("• No more popups after pressing buttons")
        print("• All modules (including daily_tasks) now visible in config panel")
        print("• Single unified configuration interface")
        return True
    else:
        print("❌ Some tests failed. Please check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
