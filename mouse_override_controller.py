"""
Mouse Override Controller - Centralized control for mouse override system
Prevents false positives during module execution by managing mouse detection state
"""

import time
import logging
from typing import Optional, Any
from contextlib import contextmanager


class MouseOverrideController:
    """
    Centralized controller for mouse override system
    Provides context managers and direct control methods for modules
    """
    
    def __init__(self, main_controller=None):
        """Initialize mouse override controller"""
        self.main_controller = main_controller
        self.logger = logging.getLogger(__name__)
        
        # Track override states
        self.override_stack = []  # Stack of override reasons
        self.cooldown_end_time = 0
        self.default_cooldown = 3.0  # Default 3-second cooldown after module execution

        # Mouse movement detection - OPTIMIZED
        self.last_mouse_pos = None
        self.mouse_pause_duration = 10.0  # Pause for 10 seconds when mouse movement detected
        self.mouse_pause_end_time = 0
        self.mouse_detection_enabled = True  # ENABLED - optimized to prevent false positives
        self.mouse_check_interval = 2.0  # Check mouse every 2 seconds instead of every scan
        self.last_mouse_check = 0

        # Track programmatic mouse movements to avoid false positives - MEMORY OPTIMIZED
        self.programmatic_movements = []  # List of (x, y, timestamp) tuples
        self.movement_tolerance = 20  # Increased tolerance to reduce false positives
        self.movement_timeout = 2.0  # Reduced timeout to prevent memory buildup
        self.max_programmatic_movements = 10  # Limit list size to prevent memory issues
        
    def set_main_controller(self, controller):
        """Set reference to main controller"""
        self.main_controller = controller
        
    def disable_mouse_override(self, reason: str = "Module execution"):
        """
        Disable mouse override detection
        
        Args:
            reason: Reason for disabling (for logging and debugging)
        """
        if not self.main_controller:
            self.logger.warning("[MOUSE_OVERRIDE] No main controller reference - cannot disable")
            return
            
        # Add to stack
        self.override_stack.append({
            'reason': reason,
            'timestamp': time.time()
        })
        
        # Set flags on main controller
        self.main_controller.module_executing = True
        self.main_controller.mouse_detection_enabled = False
        
        self.logger.info(f"[MOUSE_OVERRIDE] Disabled - Reason: {reason}")
        
    def enable_mouse_override(self, reason: str = "Module execution complete", cooldown: float = None):
        """
        Enable mouse override detection with optional cooldown
        
        Args:
            reason: Reason for enabling (for logging and debugging)
            cooldown: Cooldown period in seconds (uses default if None)
        """
        if not self.main_controller:
            self.logger.warning("[MOUSE_OVERRIDE] No main controller reference - cannot enable")
            return
            
        # Remove from stack if it exists
        if self.override_stack:
            self.override_stack.pop()
            
        # Only re-enable if stack is empty (no other modules running)
        if not self.override_stack:
            # Set cooldown period
            cooldown_duration = cooldown if cooldown is not None else self.default_cooldown
            self.cooldown_end_time = time.time() + cooldown_duration

            # Clear execution flag
            self.main_controller.module_executing = False
            self.main_controller.module_end_time = time.time()

            # CRITICAL FIX: Only re-enable mouse detection if it was enabled by the user via GUI
            # Check if mouse detection was manually disabled via GUI toggle
            if hasattr(self, 'mouse_detection_enabled') and self.mouse_detection_enabled:
                # Mouse detection is enabled at the controller level, so restore it
                self.main_controller.mouse_detection_enabled = True
                self.logger.info(f"[MOUSE_OVERRIDE] Enabled - Reason: {reason}, Cooldown: {cooldown_duration}s")
            else:
                # Mouse detection was disabled by user via GUI, keep it disabled
                self.main_controller.mouse_detection_enabled = False
                self.logger.info(f"[MOUSE_OVERRIDE] Module finished but keeping disabled - User disabled via GUI")
        else:
            self.logger.info(f"[MOUSE_OVERRIDE] Keeping disabled - {len(self.override_stack)} modules still running")
            
    def force_enable_mouse_override(self):
        """Force enable mouse override (clears all overrides)"""
        if not self.main_controller:
            return
            
        # Clear stack
        self.override_stack.clear()
        
        # Force enable
        self.main_controller.module_executing = False
        self.main_controller.mouse_detection_enabled = True
        self.main_controller.module_end_time = time.time()
        
        self.logger.info("[MOUSE_OVERRIDE] Force enabled - all overrides cleared")
        
    def is_mouse_override_disabled(self) -> bool:
        """Check if mouse override is currently disabled"""
        if not self.main_controller:
            return False
            
        return (self.main_controller.module_executing or 
                not self.main_controller.mouse_detection_enabled or
                len(self.override_stack) > 0)
                
    def get_status(self) -> dict:
        """Get current mouse override status"""
        if not self.main_controller:
            return {
                'enabled': False,
                'reason': 'No main controller',
                'active_overrides': 0,
                'cooldown_remaining': 0
            }
            
        cooldown_remaining = max(0, self.cooldown_end_time - time.time())
        
        return {
            'enabled': self.main_controller.mouse_detection_enabled and not self.main_controller.module_executing,
            'module_executing': self.main_controller.module_executing,
            'detection_enabled': self.main_controller.mouse_detection_enabled,
            'active_overrides': len(self.override_stack),
            'override_reasons': [item['reason'] for item in self.override_stack],
            'cooldown_remaining': cooldown_remaining,
            'in_cooldown': cooldown_remaining > 0
        }
        
    @contextmanager
    def disabled(self, reason: str = "Context execution", cooldown: float = None):
        """
        Context manager to temporarily disable mouse override
        
        Usage:
            with mouse_controller.disabled("My module execution"):
                # Mouse override is disabled here
                perform_automation_tasks()
            # Mouse override is automatically re-enabled here
        """
        self.disable_mouse_override(reason)
        try:
            yield
        finally:
            self.enable_mouse_override(f"{reason} complete", cooldown)
            
    def set_cooldown_duration(self, duration: float):
        """Set default cooldown duration"""
        self.default_cooldown = max(0.5, duration)  # Minimum 0.5 seconds
        self.logger.info(f"[MOUSE_OVERRIDE] Default cooldown set to {self.default_cooldown}s")

    def enable_mouse_detection(self):
        """Enable mouse movement detection for auto-pause (GUI toggle)"""
        self.mouse_detection_enabled = True
        # Also enable on main controller if no modules are currently executing
        if not self.override_stack:
            self.main_controller.mouse_detection_enabled = True
        self.logger.info("[MOUSE_OVERRIDE] Mouse movement detection enabled via GUI")

    def disable_mouse_detection(self):
        """Disable mouse movement detection (GUI toggle)"""
        self.mouse_detection_enabled = False
        self.mouse_pause_end_time = 0  # Clear any active pause
        # Always disable on main controller regardless of module execution
        self.main_controller.mouse_detection_enabled = False
        self.logger.info("[MOUSE_OVERRIDE] Mouse movement detection disabled via GUI")

    def set_mouse_pause_duration(self, duration: float):
        """Set the duration for mouse movement pause"""
        self.mouse_pause_duration = max(1.0, duration)  # Minimum 1 second
        self.logger.info(f"[MOUSE_OVERRIDE] Mouse pause duration set to {self.mouse_pause_duration}s")

    def cleanup_memory(self):
        """Clean up memory used by mouse override system"""
        try:
            # Clear old programmatic movements
            current_time = time.time()
            old_count = len(self.programmatic_movements)
            self.programmatic_movements = [
                (px, py, pt) for px, py, pt in self.programmatic_movements
                if current_time - pt < self.movement_timeout
            ]

            # Limit list size
            if len(self.programmatic_movements) > self.max_programmatic_movements:
                self.programmatic_movements = self.programmatic_movements[-self.max_programmatic_movements:]

            cleaned_count = old_count - len(self.programmatic_movements)
            if cleaned_count > 0:
                self.logger.debug(f"[MOUSE_OVERRIDE] Cleaned {cleaned_count} old programmatic movements")

        except Exception as e:
            self.logger.error(f"[MOUSE_OVERRIDE] Error during memory cleanup: {e}")

    def register_programmatic_movement(self, x: int, y: int):
        """Register a programmatic mouse movement to avoid false positives - MEMORY OPTIMIZED"""
        current_time = time.time()

        # Add new movement
        self.programmatic_movements.append((x, y, current_time))

        # MEMORY OPTIMIZATION: Limit list size and clean old movements
        if len(self.programmatic_movements) > self.max_programmatic_movements:
            # Keep only the most recent movements
            self.programmatic_movements = self.programmatic_movements[-self.max_programmatic_movements:]

        # Clean old movements (older than timeout)
        self.programmatic_movements = [
            (px, py, pt) for px, py, pt in self.programmatic_movements
            if current_time - pt < self.movement_timeout
        ]

        self.logger.debug(f"[MOUSE_OVERRIDE] Registered programmatic movement at ({x}, {y}), tracking {len(self.programmatic_movements)} movements")

    def _check_mouse_movement(self) -> bool:
        """
        Check for physical mouse movement and trigger auto-pause if detected - OPTIMIZED
        Returns True if mouse movement detected and pause triggered
        """
        try:
            if not self.mouse_detection_enabled:
                return False

            # Skip mouse detection during module execution to avoid false positives
            if len(self.override_stack) > 0:
                return False

            # OPTIMIZATION: Only check mouse position every N seconds to reduce CPU/memory usage
            current_time = time.time()
            if current_time - self.last_mouse_check < self.mouse_check_interval:
                return False

            self.last_mouse_check = current_time

            try:
                import pyautogui
                current_mouse_pos = pyautogui.position()
            except Exception as e:
                self.logger.debug(f"[MOUSE_OVERRIDE] Could not get mouse position: {e}")
                return False

            # Initialize last position if not set
            if self.last_mouse_pos is None:
                self.last_mouse_pos = current_mouse_pos
                return False

            # Calculate distance moved
            distance = ((current_mouse_pos.x - self.last_mouse_pos.x) ** 2 +
                       (current_mouse_pos.y - self.last_mouse_pos.y) ** 2) ** 0.5

            # INCREASED THRESHOLD: Require more movement to reduce false positives
            if distance > 10:  # Mouse moved more than 10 pixels (was 5)
                # Check if this was a programmatic movement
                is_programmatic = False
                for px, py, pt in self.programmatic_movements:
                    if (current_time - pt < self.movement_timeout and
                        abs(current_mouse_pos.x - px) <= self.movement_tolerance and
                        abs(current_mouse_pos.y - py) <= self.movement_tolerance):
                        self.logger.debug(f"[MOUSE_OVERRIDE] Ignoring programmatic movement to ({current_mouse_pos.x}, {current_mouse_pos.y})")
                        is_programmatic = True
                        break

                if not is_programmatic:
                    # This appears to be human movement
                    self.logger.info(f"[MOUSE_OVERRIDE] Human mouse movement detected: {distance:.1f} pixels - pausing automation for {self.mouse_pause_duration}s")
                    self.mouse_pause_end_time = time.time() + self.mouse_pause_duration
                    self.last_mouse_pos = current_mouse_pos
                    return True

            # Update last position
            self.last_mouse_pos = current_mouse_pos
            return False

        except Exception as e:
            self.logger.error(f"[MOUSE_OVERRIDE] Error checking mouse movement: {str(e)}")
            return False

    def _is_mouse_paused(self) -> bool:
        """
        Check if automation should be paused due to mouse movement
        Returns True if currently in mouse pause period
        """
        if self.mouse_pause_end_time > 0 and time.time() < self.mouse_pause_end_time:
            return True
        elif self.mouse_pause_end_time > 0 and time.time() >= self.mouse_pause_end_time:
            # Pause period ended
            self.mouse_pause_end_time = 0
            self.logger.info("[MOUSE_OVERRIDE] Mouse pause period ended - resuming automation")
            return False
        return False

    def should_pause_automation(self) -> bool:
        """
        Check if automation should be paused due to mouse override
        This replaces the old mouse detection system
        Returns True if automation should be paused
        """
        try:
            # Check if we're in cooldown period
            current_time = time.time()
            if self.cooldown_end_time > 0 and current_time < self.cooldown_end_time:
                return True
            elif self.cooldown_end_time > 0 and current_time >= self.cooldown_end_time:
                # Cooldown period ended
                self.cooldown_end_time = 0
                self.logger.info("[MOUSE_OVERRIDE] Cooldown period ended - resuming automation")
                return False

            # Check if mouse override is disabled (modules executing)
            if len(self.override_stack) > 0:
                self.logger.debug(f"[MOUSE_OVERRIDE] Pausing automation - {len(self.override_stack)} active overrides")
                return True

            # Check for mouse movement and mouse pause
            if self._check_mouse_movement() or self._is_mouse_paused():
                return True

            return False

        except Exception as e:
            self.logger.error(f"[MOUSE_OVERRIDE] Error in should_pause_automation: {str(e)}")
            return False

    def should_pause_for_human_movement(self) -> bool:
        """
        Check if automation should be paused ONLY for human mouse movement
        This separates human movement detection from module execution overrides
        Returns True if automation should be paused due to human mouse movement
        """
        try:
            # Check if we're in cooldown period (after module execution)
            current_time = time.time()
            if self.cooldown_end_time > 0 and current_time < self.cooldown_end_time:
                return True
            elif self.cooldown_end_time > 0 and current_time >= self.cooldown_end_time:
                # Cooldown period ended
                self.cooldown_end_time = 0
                self.logger.info("[MOUSE_OVERRIDE] Cooldown period ended - resuming automation")
                return False

            # ONLY check for human mouse movement, NOT module execution overrides
            # This allows the scanner to continue running even when modules are executing
            if self._check_mouse_movement() or self._is_mouse_paused():
                return True

            return False

        except Exception as e:
            self.logger.error(f"[MOUSE_OVERRIDE] Error in should_pause_for_human_movement: {str(e)}")
            return False


# Global instance for easy access
_global_mouse_controller: Optional[MouseOverrideController] = None


def get_mouse_controller() -> MouseOverrideController:
    """Get global mouse override controller instance"""
    global _global_mouse_controller
    if _global_mouse_controller is None:
        _global_mouse_controller = MouseOverrideController()
    return _global_mouse_controller


def initialize_mouse_controller(main_controller):
    """Initialize global mouse controller with main controller reference"""
    global _global_mouse_controller
    _global_mouse_controller = MouseOverrideController(main_controller)
    return _global_mouse_controller


# Convenience functions for easy module integration
def disable_mouse_override(reason: str = "Module execution"):
    """Convenience function to disable mouse override"""
    get_mouse_controller().disable_mouse_override(reason)


def enable_mouse_override(reason: str = "Module execution complete", cooldown: float = None):
    """Convenience function to enable mouse override"""
    get_mouse_controller().enable_mouse_override(reason, cooldown)


def mouse_override_disabled(reason: str = "Context execution", cooldown: float = None):
    """Convenience function to get context manager"""
    return get_mouse_controller().disabled(reason, cooldown)
