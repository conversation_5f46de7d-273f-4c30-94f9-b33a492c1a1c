#!/usr/bin/env python3
"""
Test script to verify the optimized window focus behavior
"""
import sys
import os

# Add modules directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

def test_window_focus_behavior():
    """Test the new optimized window focus behavior"""
    
    print("🔍 Testing Window Focus Optimization")
    print("=" * 50)
    
    try:
        # Import the GUI class
        from gui import LastWarGUI
        from unified_config_manager import UnifiedConfigManager
        
        # Create a minimal GUI instance for testing
        unified_config = UnifiedConfigManager()
        config_data = unified_config.load_config()
        
        # Create GUI instance (this won't show the actual GUI)
        gui = LastWarGUI(None, config_data)
        
        print("✅ GUI instance created successfully")
        
        # Test the focus method with skip_resize_for_fullscreen=True
        print("\n🎯 Testing optimized focus (skip resize for fullscreen):")
        print("   - This should focus the window without intermediate resize")
        print("   - Window should go directly to fullscreen preparation")
        
        # Note: This will actually try to focus the game window if it's running
        result = gui._focus_and_resize_game_window(skip_resize_for_fullscreen=True)
        
        if result:
            print("✅ Focus method completed successfully")
            print("   Expected behavior: Window focused without intermediate resize")
        else:
            print("ℹ️ Focus method returned False (game window not found - this is normal if game isn't running)")
        
        print("\n📋 Expected Log Messages:")
        print("   ✅ 'Game window focused'")
        print("   ✅ 'Skipping resize - going directly to fullscreen'")
        print("   ❌ Should NOT see: 'Window resized to 1260x740'")
        
        print("\n🎉 Test completed!")
        print("   The map trade function will now skip the intermediate resize step")
        print("   and go directly from current size to fullscreen.")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure you're running this from the correct directory")
    except Exception as e:
        print(f"❌ Error during test: {e}")

if __name__ == "__main__":
    test_window_focus_behavior()
