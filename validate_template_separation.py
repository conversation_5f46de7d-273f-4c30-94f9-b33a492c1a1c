#!/usr/bin/env python3
"""
Template Separation Validator
Checks for template duplication between TRIGGER templates (centralized scanner) 
and EXECUTION templates (module-specific configurations)
"""
import json
import os
from typing import Dict, List, Set, Tuple


def load_trigger_templates() -> Dict[str, Dict]:
    """Load TRIGGER templates from centralized scanner config"""
    config_file = "template_scanner_config.json"
    if not os.path.exists(config_file):
        print(f"❌ TRIGGER template config not found: {config_file}")
        return {}
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        trigger_templates = {}
        for mapping in config.get('template_mappings', []):
            template_name = mapping.get('template_name', '')
            if template_name:
                trigger_templates[template_name] = {
                    'module': mapping.get('module', ''),
                    'action': mapping.get('action', ''),
                    'priority': mapping.get('priority', 999),
                    'enabled': mapping.get('enabled', False),
                    'threshold': mapping.get('threshold', 0.8)
                }
        
        return trigger_templates
    except Exception as e:
        print(f"❌ Error loading TRIGGER templates: {str(e)}")
        return {}


def load_execution_templates() -> Dict[str, List[str]]:
    """Load EXECUTION templates from module configs"""
    config_file = "module_configs.json"
    if not os.path.exists(config_file):
        print(f"❌ EXECUTION template config not found: {config_file}")
        return {}
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        execution_templates = {}
        for module_name, module_config in config.items():
            templates = module_config.get('templates', [])
            template_names = []
            
            for template in templates:
                if isinstance(template, dict):
                    template_name = template.get('name', '')
                elif isinstance(template, str):
                    template_name = template
                else:
                    continue
                
                if template_name:
                    template_names.append(template_name)
            
            if template_names:
                execution_templates[module_name] = template_names
        
        return execution_templates
    except Exception as e:
        print(f"❌ Error loading EXECUTION templates: {str(e)}")
        return {}


def check_template_files_exist(template_names: Set[str]) -> Dict[str, bool]:
    """Check which template files actually exist"""
    templates_dir = "templates"
    if not os.path.exists(templates_dir):
        print(f"❌ Templates directory not found: {templates_dir}")
        return {}
    
    file_existence = {}
    for template_name in template_names:
        # Check for common image extensions
        extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.gif']
        found = False
        
        for ext in extensions:
            file_path = os.path.join(templates_dir, f"{template_name}{ext}")
            if os.path.exists(file_path):
                found = True
                break
        
        file_existence[template_name] = found
    
    return file_existence


def validate_template_separation():
    """Main validation function"""
    print("🔍 Template Separation Validator")
    print("=" * 50)
    
    # Load configurations
    trigger_templates = load_trigger_templates()
    execution_templates = load_execution_templates()
    
    if not trigger_templates and not execution_templates:
        print("❌ No template configurations found!")
        return
    
    print(f"📋 Loaded {len(trigger_templates)} TRIGGER templates")
    print(f"📋 Loaded {sum(len(templates) for templates in execution_templates.values())} EXECUTION templates across {len(execution_templates)} modules")
    print()
    
    # Find all unique template names
    trigger_names = set(trigger_templates.keys())
    execution_names = set()
    for module_templates in execution_templates.values():
        execution_names.update(module_templates)
    
    all_template_names = trigger_names | execution_names
    
    # Check for duplications
    duplicated_templates = trigger_names & execution_names
    
    if duplicated_templates:
        print("❌ TEMPLATE DUPLICATION DETECTED!")
        print("=" * 40)
        for template_name in sorted(duplicated_templates):
            print(f"🔄 '{template_name}' appears in both TRIGGER and EXECUTION configs")
            
            # Show where it appears
            trigger_info = trigger_templates.get(template_name, {})
            print(f"   TRIGGER: module={trigger_info.get('module', 'N/A')}, action={trigger_info.get('action', 'N/A')}")
            
            for module_name, module_templates in execution_templates.items():
                if template_name in module_templates:
                    print(f"   EXECUTION: module={module_name}")
        print()
    else:
        print("✅ No template duplication found!")
        print()
    
    # Check for missing template files
    file_existence = check_template_files_exist(all_template_names)
    missing_files = [name for name, exists in file_existence.items() if not exists]
    
    if missing_files:
        print("⚠️ MISSING TEMPLATE FILES:")
        print("=" * 30)
        for template_name in sorted(missing_files):
            print(f"📄 '{template_name}' - file not found in templates/ directory")
            
            # Show where it's referenced
            if template_name in trigger_templates:
                trigger_info = trigger_templates[template_name]
                print(f"   Referenced in TRIGGER: module={trigger_info.get('module', 'N/A')}")
            
            for module_name, module_templates in execution_templates.items():
                if template_name in module_templates:
                    print(f"   Referenced in EXECUTION: module={module_name}")
        print()
    else:
        print("✅ All template files exist!")
        print()
    
    # Summary statistics
    print("📊 SUMMARY:")
    print("=" * 20)
    print(f"Total unique templates: {len(all_template_names)}")
    print(f"TRIGGER templates: {len(trigger_names)}")
    print(f"EXECUTION templates: {len(execution_names)}")
    print(f"Duplicated templates: {len(duplicated_templates)}")
    print(f"Missing template files: {len(missing_files)}")
    print()
    
    # Recommendations
    if duplicated_templates or missing_files:
        print("🔧 RECOMMENDATIONS:")
        print("=" * 20)
        
        if duplicated_templates:
            print("1. Review duplicated templates and decide:")
            print("   - Keep in TRIGGER config if used for module activation")
            print("   - Keep in EXECUTION config if used for module internal logic")
            print("   - Rename one of them to clarify purpose")
        
        if missing_files:
            print("2. Create missing template files or remove references:")
            print("   - Capture template images and save to templates/ directory")
            print("   - Remove unused template references from configurations")
        
        print("\n📖 See TEMPLATE_ARCHITECTURE_GUIDE.md for detailed guidelines")
    else:
        print("🎉 Template separation is properly configured!")
    
    return len(duplicated_templates) == 0 and len(missing_files) == 0


if __name__ == "__main__":
    success = validate_template_separation()
    exit(0 if success else 1)
