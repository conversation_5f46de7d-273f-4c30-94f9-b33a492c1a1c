"""
Centralized Template Scanner for Last War Automation
Manages all template detection and module routing with priority system
"""
import json
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from screen_scanner import ScreenScanner

try:
    import win32gui
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    logging.warning("win32gui not available - will scan entire screen instead of game window")


@dataclass
class TemplateMapping:
    """Data class for template-to-module mappings"""
    template_name: str
    priority: int
    module: str
    action: str
    description: str
    enabled: bool
    threshold: float
    cooldown: float
    last_executed: Optional[datetime] = None


@dataclass
class ExclusionRule:
    """Data class for template exclusion rules"""
    name: str
    description: str
    primary_template: str
    excluded_templates: List[str]
    enabled: bool


class CentralizedTemplateScanner:
    """
    Centralized scanner that manages all template detection and module routing
    """
    
    def __init__(self, screen_scanner: 'ScreenScanner' = None, config_file: str = "template_scanner_config.json", controller=None):
        self.logger = logging.getLogger("LastWar.CentralizedScanner")
        self.config_file = config_file
        self.controller = controller  # Reference to main controller for mouse override control

        # Initialize screen scanner
        if screen_scanner is not None:
            self.screen_scanner = screen_scanner
        else:
            from screen_scanner import ScreenScanner
            self.screen_scanner = ScreenScanner()

        # Load configuration
        self.template_mappings: List[TemplateMapping] = []
        self.exclusion_rules: List[ExclusionRule] = []
        self.scanner_settings: Dict[str, Any] = {}
        self._load_configuration()

        # ESC recovery system
        self.failed_scan_count = 0
        self.max_failed_scans = 10  # Trigger ESC recovery after 10 failed scans
        self.last_esc_recovery = 0  # Timestamp of last ESC recovery
        self.esc_recovery_cooldown = 30.0  # Wait 30 seconds between ESC recoveries

        # Runtime state
        self.last_scan_time = None
        self.detection_cache: Dict[str, Any] = {}
        self.module_registry: Dict[str, Any] = {}

        # Game window detection
        self.game_window_region = None
        self.last_window_check = 0
        self.window_check_interval = 30  # Check window position every 30 seconds

        self.logger.info(f"[INIT] Centralized Template Scanner initialized with {len(self.template_mappings)} mappings")
    
    def _load_configuration(self):
        """Load template mappings and settings from configuration file"""
        try:
            with open(self.config_file, 'r') as f:
                config = json.load(f)
            
            # Load template mappings
            self.template_mappings = []
            for mapping_data in config.get('template_mappings', []):
                mapping = TemplateMapping(**mapping_data)
                self.template_mappings.append(mapping)
            
            # Load exclusion rules
            self.exclusion_rules = []
            for rule_data in config.get('exclusion_rules', []):
                rule = ExclusionRule(**rule_data)
                self.exclusion_rules.append(rule)
            
            # Load scanner settings
            self.scanner_settings = config.get('scanner_settings', {})
            
            # Sort mappings by priority (lower number = higher priority)
            self.template_mappings.sort(key=lambda x: x.priority)
            
            self.logger.info(f"[CONFIG] Loaded {len(self.template_mappings)} template mappings")
            self.logger.info(f"[CONFIG] Loaded {len(self.exclusion_rules)} exclusion rules")
            
        except Exception as e:
            self.logger.error(f"[ERROR] Failed to load configuration: {e}")
            raise
    
    def register_module(self, module_name: str, module_instance):
        """Register a module instance for template-based execution"""
        self.module_registry[module_name] = module_instance
        self.logger.info(f"[REGISTER] Module: {module_name}")

    def _find_game_window_region(self) -> Optional[Tuple[int, int, int, int]]:
        """
        Find the Last War game window and return its region coordinates
        Returns: (left, top, width, height) or None if not found
        """
        if not WIN32_AVAILABLE:
            return None

        try:
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    # Look specifically for "Last War-Survival Game"
                    if "Last War-Survival Game" in window_title:
                        windows.append((hwnd, window_title))
                    elif "Last War" in window_title and "Survival" in window_title:
                        windows.append((hwnd, window_title))
                    elif "Last War" in window_title:
                        windows.append((hwnd, window_title))
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)

            if windows:
                # Prefer exact match first
                for hwnd, title in windows:
                    if "Last War-Survival Game" in title:
                        rect = win32gui.GetWindowRect(hwnd)
                        left, top, right, bottom = rect
                        width = right - left
                        height = bottom - top
                        self.logger.info(f"[WINDOW] Found Last War window: '{title}' at ({left}, {top}, {width}, {height})")
                        return (left, top, width, height)

                # Return first match if no exact match
                hwnd, title = windows[0]
                rect = win32gui.GetWindowRect(hwnd)
                left, top, right, bottom = rect
                width = right - left
                height = bottom - top
                self.logger.info(f"[WINDOW] Found Last War window: '{title}' at ({left}, {top}, {width}, {height})")
                return (left, top, width, height)

            return None

        except Exception as e:
            self.logger.error(f"Error finding game window: {e}")
            return None

    def _get_game_window_region(self) -> Optional[Tuple[int, int, int, int]]:
        """
        Get the current game window region, checking periodically for updates
        Returns: (left, top, width, height) or None if not found
        """
        current_time = time.time()

        # Check if we need to update window position
        if (self.game_window_region is None or
            current_time - self.last_window_check > self.window_check_interval):

            self.game_window_region = self._find_game_window_region()
            self.last_window_check = current_time

            if self.game_window_region:
                self.logger.debug(f"[WINDOW] Game window region updated: {self.game_window_region}")
            else:
                self.logger.warning("[WARNING] Last War game window not found - scanning entire screen")

        return self.game_window_region

    def _perform_esc_recovery(self) -> bool:
        """
        Perform ESC recovery sequence to return to main menu
        ESC once, check if dialog is there, if not press again until main menu or quit dialog
        """
        try:
            import pyautogui
            from mouse_override_controller import mouse_override_disabled

            self.logger.warning(f"[ESC_RECOVERY] Starting ESC recovery after {self.failed_scan_count} failed scans")

            # Disable mouse override during ESC sequence with cooldown to prevent false positives
            with mouse_override_disabled("Centralized scanner ESC recovery", cooldown=3.0):
                max_esc_attempts = 8  # Maximum ESC presses to prevent infinite loops
                esc_attempt = 0

                while esc_attempt < max_esc_attempts:
                    esc_attempt += 1
                    self.logger.info(f"[ESC_RECOVERY] ESC attempt {esc_attempt}/{max_esc_attempts}")

                    # Check current screen state with STANDARD detection (enhanced detection disabled due to false positives)
                    screen_data = self.screen_scanner.scan_screen_cache_optimized(
                        required_templates=['events_button', 'quit_game_dialog'],
                        enhanced_detection=False,  # Disable enhanced detection to prevent false positives
                        debug_screenshots=True     # Keep debug screenshots for troubleshooting
                    )
                    templates_detected = screen_data.get('templates_detected', {})
                    detection_details = screen_data.get('detection_details', {})

                    # Enhanced debug logging with detection details
                    self.logger.debug(f"[ESC_RECOVERY] Template detection results: {templates_detected}")
                    for template_name, details in detection_details.items():
                        method = details.get('method', 'unknown')
                        confidence = details.get('confidence', 0.0)
                        detected = details.get('detected', False)
                        self.logger.debug(f"[ESC_RECOVERY] {template_name}: {detected} (method: {method}, confidence: {confidence:.3f})")

                    if not templates_detected:
                        self.logger.debug("[ESC_RECOVERY] No templates detected - checking screen_data structure")
                        self.logger.debug(f"[ESC_RECOVERY] Screen data keys: {list(screen_data.keys())}")

                    # Log debug screenshot path if available
                    debug_info = screen_data.get('debug_info', {})
                    if 'screenshot_path' in debug_info:
                        self.logger.debug(f"[ESC_RECOVERY] Debug screenshot saved: {debug_info['screenshot_path']}")

                    # Log enhanced debug visualizations if available
                    for template_name, details in detection_details.items():
                        template_debug_info = details.get('debug_info', {})
                        if 'debug_visualization_path' in template_debug_info:
                            self.logger.debug(f"[ESC_RECOVERY] Debug visualization for {template_name}: {template_debug_info['debug_visualization_path']}")
                        if 'heatmap_path' in template_debug_info:
                            self.logger.debug(f"[ESC_RECOVERY] Heatmap for {template_name}: {template_debug_info['heatmap_path']}")

                    # SUCCESS: Main menu detected (events button visible)
                    if templates_detected.get('events_button', False):
                        self.logger.info(f"[ESC_RECOVERY] ✅ SUCCESS: Main menu detected after {esc_attempt} ESC attempts")
                        self.failed_scan_count = 0  # Reset failed scan counter
                        self.last_esc_recovery = time.time()
                        return True

                    # SUCCESS: Quit dialog detected - press ESC once more to dismiss it
                    elif templates_detected.get('quit_game_dialog', False):
                        self.logger.info(f"[ESC_RECOVERY] ✅ SUCCESS: Quit dialog detected after {esc_attempt} ESC attempts - dismissing it")
                        pyautogui.press('escape')
                        time.sleep(1.5)  # Wait for quit dialog to close

                        # Verify we're back to main menu with standard detection
                        screen_data = self.screen_scanner.scan_screen_cache_optimized(
                            required_templates=['events_button'],
                            enhanced_detection=False,  # Use standard detection for verification
                            debug_screenshots=True
                        )
                        templates_detected = screen_data.get('templates_detected', {})
                        detection_details = screen_data.get('detection_details', {})

                        # Log verification details
                        events_details = detection_details.get('events_button', {})
                        method = events_details.get('method', 'unknown')
                        confidence = events_details.get('confidence', 0.0)
                        self.logger.debug(f"[ESC_RECOVERY] Main menu verification: {templates_detected.get('events_button', False)} (method: {method}, confidence: {confidence:.3f})")

                        if templates_detected.get('events_button', False):
                            self.logger.info("[ESC_RECOVERY] ✅ Successfully returned to main menu after dismissing quit dialog")
                            self.failed_scan_count = 0  # Reset failed scan counter
                            self.last_esc_recovery = time.time()
                            return True

                    # CONTINUE: Neither main menu nor quit dialog detected - press ESC and continue
                    else:
                        self.logger.info(f"[ESC_RECOVERY] Neither main menu nor quit dialog detected - pressing ESC {esc_attempt}")
                        pyautogui.press('escape')
                        time.sleep(1.2)  # Wait for ESC effect

                # If we reach here, we've exceeded max attempts
                self.logger.warning(f"[ESC_RECOVERY] ⚠️ Reached max ESC attempts ({max_esc_attempts}) without reaching main menu")
                self.last_esc_recovery = time.time()
                return False

        except Exception as e:
            self.logger.error(f"[ESC_RECOVERY] Error during ESC recovery: {str(e)}")
            self.last_esc_recovery = time.time()
            return False

    def scan_and_execute(self) -> Optional[Tuple[str, str, bool]]:
        """
        Perform template scan and execute highest priority detected action
        Returns: (template_name, module_name, execution_success) or None
        """
        try:
            # Get game window region for targeted scanning
            game_region = self._get_game_window_region()

            # Get enabled templates with their thresholds
            enabled_templates = []
            template_thresholds = {}

            for mapping in self.template_mappings:
                if mapping.enabled:
                    enabled_templates.append(mapping.template_name)
                    template_thresholds[mapping.template_name] = mapping.threshold

            # Debug logging for enabled templates
            if 'map_button' in enabled_templates:
                self.logger.debug(f"[DEBUG] map_button is enabled with threshold: {template_thresholds.get('map_button', 'N/A')}")

            if not enabled_templates:
                self.logger.warning("[WARNING] No enabled templates to scan for")
                return None

            # Perform screen scan (either game window or full screen) with custom thresholds
            if game_region:
                screen_data = self.screen_scanner.scan_screen(required_templates=enabled_templates, region=game_region, template_thresholds=template_thresholds)
                self.logger.debug(f"[SCAN] Scanning game window region: {game_region} for {len(enabled_templates)} templates")
            else:
                screen_data = self.screen_scanner.scan_screen(required_templates=enabled_templates, template_thresholds=template_thresholds)
                self.logger.debug(f"[SCAN] Scanning entire screen for {len(enabled_templates)} templates")

            templates_detected = screen_data.get('templates_detected', {})

            # Check for modules with ongoing execution (like dig_in_progress)
            self.logger.info("[DEBUG] Checking for ongoing module execution...")
            ongoing_execution = self._check_ongoing_module_execution(screen_data)
            if ongoing_execution:
                self.logger.info(f"[ONGOING] Found ongoing execution: {ongoing_execution}")
                return ongoing_execution
            else:
                self.logger.info("[DEBUG] No ongoing execution found")

            if not templates_detected:
                return None

            # Find highest priority template that's detected and ready
            for mapping in self.template_mappings:
                if not mapping.enabled:
                    continue

                # Check if template is detected (simplified - use only templates_detected)
                template_detected = templates_detected.get(mapping.template_name, False)

                if not template_detected:
                    continue
                
                # Check cooldown
                if self._is_on_cooldown(mapping):
                    continue
                
                # Check exclusion rules
                if self._is_excluded(mapping.template_name, templates_detected):
                    continue
                
                # Execute the action
                success = self._execute_template_action(mapping, screen_data)

                # Update last executed time
                mapping.last_executed = datetime.now()

                # Reset failed scan counter on successful detection
                self.failed_scan_count = 0

                return (mapping.template_name, mapping.module, success)

            # No templates detected - increment failed scan counter
            self.failed_scan_count += 1

            # Check if ESC recovery is needed
            current_time = time.time()
            if (self.failed_scan_count >= self.max_failed_scans and
                current_time - self.last_esc_recovery > self.esc_recovery_cooldown):

                self.logger.warning(f"[ESC_RECOVERY] {self.failed_scan_count} consecutive failed scans - triggering ESC recovery")

                # Perform ESC recovery
                recovery_success = self._perform_esc_recovery()

                if recovery_success:
                    self.logger.info("[ESC_RECOVERY] ✅ ESC recovery successful - resuming normal scanning")
                else:
                    self.logger.warning("[ESC_RECOVERY] ⚠️ ESC recovery completed but main menu not confirmed")

                # Reset counter regardless of success to prevent spam
                self.failed_scan_count = 0

            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error in scan_and_execute: {e}")
            return None
    
    def _is_on_cooldown(self, mapping: TemplateMapping) -> bool:
        """Check if a template mapping is on cooldown"""
        if mapping.last_executed is None:
            return False
        
        if mapping.cooldown <= 0:
            return False
        
        time_since_last = datetime.now() - mapping.last_executed
        return time_since_last.total_seconds() < mapping.cooldown
    
    def _is_excluded(self, template_name: str, templates_detected: Dict[str, bool]) -> bool:
        """Check if a template should be excluded based on exclusion rules"""
        for rule in self.exclusion_rules:
            if not rule.enabled:
                continue

            # If primary template is detected, exclude the specified templates
            if (rule.primary_template in templates_detected and
                templates_detected[rule.primary_template] and
                template_name in rule.excluded_templates):
                
                self.logger.debug(f"[EXCLUDE] Template {template_name} excluded by rule: {rule.name}")
                return True
        
        return False
    
    def _execute_template_action(self, mapping: TemplateMapping, screen_data: Dict[str, Any]) -> bool:
        """Execute the action for a detected template"""
        try:
            module_instance = self.module_registry.get(mapping.module)
            if not module_instance:
                self.logger.warning(f"[WARNING] Module {mapping.module} not registered")
                return False

            self.logger.info(f"[EXECUTE] {mapping.template_name} -> {mapping.module}.{mapping.action}")

            # Disable mouse override during module execution
            if self.controller:
                self.controller.module_executing = True
                self.logger.info(f"[MOUSE] Override disabled for {mapping.module}.{mapping.action} execution")

            try:
                # Call the appropriate action on the module
                if hasattr(module_instance, mapping.action):
                    action_method = getattr(module_instance, mapping.action)
                    result = action_method(screen_data)
                elif hasattr(module_instance, 'execute'):
                    # Fallback to generic execute method
                    result = module_instance.execute(screen_data)
                else:
                    self.logger.warning(f"[WARNING] Action {mapping.action} not found in module {mapping.module}")
                    result = False

                return result

            finally:
                # Re-enable mouse override after module execution
                if self.controller:
                    self.controller.module_executing = False
                    self.controller.module_end_time = time.time()  # Set cooldown period
                    self.logger.info(f"[MOUSE] Override re-enabled after {mapping.module}.{mapping.action} execution")

        except Exception as e:
            self.logger.error(f"❌ Error executing {mapping.template_name}: {e}")
            return False

    def _check_ongoing_module_execution(self, screen_data: Dict[str, Any]) -> Optional[Tuple[str, str, bool]]:
        """Check for modules that have ongoing execution and need to continue"""
        try:
            self.logger.info(f"[DEBUG] Checking {len(self.module_registry)} registered modules for ongoing execution")

            # Check each registered module for ongoing execution
            for module_name, module_instance in self.module_registry.items():
                self.logger.info(f"[DEBUG] Checking module: {module_name}")

                # Check if module has custom_can_execute method (like dig module)
                if hasattr(module_instance, 'custom_can_execute'):
                    self.logger.info(f"[DEBUG] {module_name} has custom_can_execute method")
                    try:
                        # If the module says it can execute (e.g., dig_in_progress = True)
                        can_execute = module_instance.custom_can_execute(screen_data)
                        self.logger.info(f"[DEBUG] {module_name}.custom_can_execute() = {can_execute}")

                        if can_execute:
                            # Check if this is truly ongoing execution (not just a new trigger)
                            # Look for common ongoing execution flags
                            ongoing_flags = ['dig_in_progress', 'in_progress', 'executing', 'active']
                            is_ongoing = False

                            self.logger.info(f"[DEBUG] {module_name} can execute - checking ongoing flags: {ongoing_flags}")

                            for flag in ongoing_flags:
                                if hasattr(module_instance, flag):
                                    flag_value = getattr(module_instance, flag)
                                    self.logger.info(f"[DEBUG] {module_name}.{flag} = {flag_value}")
                                    if flag_value:
                                        is_ongoing = True
                                        self.logger.info(f"[ONGOING] Continuing {module_name} execution ({flag}=True)")
                                        break
                                else:
                                    self.logger.info(f"[DEBUG] {module_name} does not have flag: {flag}")

                            if is_ongoing:
                                self.logger.info(f"[ONGOING] Found ongoing execution for {module_name} - looking for enabled mapping")
                                # Find the appropriate mapping for this module
                                for mapping in self.template_mappings:
                                    if mapping.module == module_name and mapping.enabled:
                                        self.logger.info(f"[ONGOING] Executing ongoing {module_name} via mapping: {mapping.template_name}")
                                        # Execute the ongoing module
                                        success = self._execute_template_action(mapping, screen_data)

                                        # Don't update last_executed time for ongoing execution
                                        # This allows the module to continue without cooldown restrictions

                                        return (f"{module_name}_ongoing", mapping.module, success)

                                self.logger.warning(f"[ONGOING] No enabled mapping found for ongoing module: {module_name}")
                            else:
                                # Module can execute but no ongoing flags - might be a new trigger
                                self.logger.debug(f"[DEBUG] {module_name} can execute but no ongoing flags detected")
                    except Exception as e:
                        self.logger.debug(f"Error checking ongoing execution for {module_name}: {e}")
                        continue

            return None

        except Exception as e:
            self.logger.error(f"Error checking ongoing module execution: {e}")
            return None
    
    def get_status_info(self) -> Dict[str, Any]:
        """Get current status information for GUI display"""
        active_mappings = [m for m in self.template_mappings if m.enabled]
        cooldown_info = []
        
        for mapping in active_mappings:
            if mapping.last_executed and mapping.cooldown > 0:
                time_since = datetime.now() - mapping.last_executed
                remaining = max(0, mapping.cooldown - time_since.total_seconds())
                if remaining > 0:
                    cooldown_info.append({
                        'template': mapping.template_name,
                        'remaining': remaining,
                        'total': mapping.cooldown
                    })
        
        return {
            'total_mappings': len(self.template_mappings),
            'active_mappings': len(active_mappings),
            'cooldowns': cooldown_info,
            'last_scan': self.last_scan_time,
            'registered_modules': list(self.module_registry.keys())
        }
    
    def update_template_priority(self, template_name: str, new_priority: int):
        """Update the priority of a template mapping"""
        for mapping in self.template_mappings:
            if mapping.template_name == template_name:
                mapping.priority = new_priority
                # Re-sort by priority
                self.template_mappings.sort(key=lambda x: x.priority)
                self.logger.info(f"[PRIORITY] Updated {template_name} priority to {new_priority}")
                return True
        return False
    
    def toggle_template(self, template_name: str, enabled: bool):
        """Enable or disable a template mapping"""
        for mapping in self.template_mappings:
            if mapping.template_name == template_name:
                mapping.enabled = enabled
                status = "enabled" if enabled else "disabled"
                self.logger.info(f"[TOGGLE] Template {template_name} {status}")
                return True
        return False

    def sync_module_state(self, module_name: str, enabled: bool):
        """Sync module enabled state from GUI to centralized scanner"""
        try:
            # Update all template mappings for this module
            updated_count = 0
            for mapping in self.template_mappings:
                if mapping.module == module_name:
                    mapping.enabled = enabled
                    updated_count += 1

            if updated_count > 0:
                # Save the updated configuration
                self.save_configuration()
                self.logger.info(f"[SYNC] {module_name} state: {'enabled' if enabled else 'disabled'} ({updated_count} mappings)")
            else:
                self.logger.debug(f"No template mappings found for module {module_name}")

        except Exception as e:
            self.logger.error(f"Failed to sync module state for {module_name}: {e}")

    def get_module_enabled_state(self, module_name: str) -> bool:
        """Get the enabled state of a module based on its template mappings"""
        try:
            # Check if any template mapping for this module is enabled
            for mapping in self.template_mappings:
                if mapping.module == module_name and mapping.enabled:
                    return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to get module state for {module_name}: {e}")
            return False

    def save_configuration(self):
        """Save current configuration back to file"""
        try:
            config = {
                'template_mappings': [
                    {
                        'template_name': m.template_name,
                        'priority': m.priority,
                        'module': m.module,
                        'action': m.action,
                        'description': m.description,
                        'enabled': m.enabled,
                        'threshold': m.threshold,
                        'cooldown': m.cooldown
                    }
                    for m in self.template_mappings
                ],
                'exclusion_rules': [
                    {
                        'name': r.name,
                        'description': r.description,
                        'primary_template': r.primary_template,
                        'excluded_templates': r.excluded_templates,
                        'enabled': r.enabled
                    }
                    for r in self.exclusion_rules
                ],
                'scanner_settings': self.scanner_settings
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            self.logger.info("[SAVE] Configuration saved successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to save configuration: {e}")
            return False

    def get_current_screen_data(self):
        """Get fresh screen data for modules that need it during execution"""
        try:
            # Get enabled templates
            enabled_templates = []
            for template_name, mapping in self.template_mappings.items():
                if mapping.get('enabled', True):
                    enabled_templates.append(template_name)

            if not enabled_templates:
                return None

            # Get game window region
            game_region = self._get_game_window_region()

            # Perform fresh screen scan
            if game_region:
                screen_data = self.screen_scanner.scan_screen(required_templates=enabled_templates, region=game_region)
            else:
                screen_data = self.screen_scanner.scan_screen(required_templates=enabled_templates)

            return screen_data

        except Exception as e:
            self.logger.error(f"Failed to get current screen data: {e}")
            return None

    def trigger_esc_recovery(self, reason="Manual trigger"):
        """Manually trigger ESC recovery sequence"""
        try:
            self.logger.info(f"[ESC_RECOVERY] Manual trigger: {reason}")
            self.consecutive_failed_scans = 10  # Set to trigger threshold
            # The next scan cycle will automatically trigger ESC recovery

        except Exception as e:
            self.logger.error(f"Failed to trigger ESC recovery: {e}")
