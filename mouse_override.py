"""
Mouse Override Utility Module
Simple standalone utility for controlling mouse override functionality
"""

import logging
import time
from typing import Optional

class MouseOverride:
    """Standalone mouse override utility with simple enable/disable functions"""
    
    def __init__(self):
        self.logger = logging.getLogger("LastWar.MouseOverride")
        self._enabled = True
        self._gui_disabled = False  # Track if GUI button disabled it
        self._last_action_time = 0
        
    def disable(self, reason: str = "Module execution") -> None:
        """
        Disable mouse override for automation module execution
        
        Args:
            reason: Optional reason for disabling (for logging)
        """
        if self._gui_disabled:
            self.logger.debug(f"[MOUSE_OVERRIDE] Already disabled by GUI - ignoring disable request: {reason}")
            return
            
        self._enabled = False
        self._last_action_time = time.time()
        self.logger.info(f"[MOUSE_OVERRIDE] Disabled - Reason: {reason}")
    
    def enable(self, reason: str = "Module execution complete") -> None:
        """
        Enable mouse override after automation module execution
        
        Args:
            reason: Optional reason for enabling (for logging)
        """
        if self._gui_disabled:
            self.logger.debug(f"[MOUSE_OVERRIDE] GUI disabled - ignoring enable request: {reason}")
            return
            
        self._enabled = True
        self._last_action_time = time.time()
        self.logger.info(f"[MOUSE_OVERRIDE] Enabled - Reason: {reason}")
    
    def is_enabled(self) -> bool:
        """Check if mouse override is currently enabled"""
        return self._enabled and not self._gui_disabled
    
    def gui_disable(self) -> None:
        """Called when GUI Mouse Override button is pressed to disable"""
        self._gui_disabled = True
        self._enabled = False
        self._last_action_time = time.time()
        self.logger.info("[MOUSE_OVERRIDE] Disabled by GUI button")
    
    def gui_enable(self) -> None:
        """Called when GUI Mouse Override button is pressed to enable"""
        self._gui_disabled = False
        self._enabled = True
        self._last_action_time = time.time()
        self.logger.info("[MOUSE_OVERRIDE] Enabled by GUI button")
    
    def get_status(self) -> dict:
        """Get current status for debugging/monitoring"""
        return {
            'enabled': self._enabled,
            'gui_disabled': self._gui_disabled,
            'is_active': self.is_enabled(),
            'last_action_time': self._last_action_time
        }


# Global instance for easy access
_mouse_override_instance: Optional[MouseOverride] = None

def get_instance() -> MouseOverride:
    """Get the global mouse override instance"""
    global _mouse_override_instance
    if _mouse_override_instance is None:
        _mouse_override_instance = MouseOverride()
    return _mouse_override_instance

def disable(reason: str = "Module execution") -> None:
    """Disable mouse override - convenience function"""
    get_instance().disable(reason)

def enable(reason: str = "Module execution complete") -> None:
    """Enable mouse override - convenience function"""
    get_instance().enable(reason)

def is_enabled() -> bool:
    """Check if mouse override is enabled - convenience function"""
    return get_instance().is_enabled()

def gui_disable() -> None:
    """GUI disable - convenience function"""
    get_instance().gui_disable()

def gui_enable() -> None:
    """GUI enable - convenience function"""
    get_instance().gui_enable()

def get_status() -> dict:
    """Get status - convenience function"""
    return get_instance().get_status()


# Example usage:
# import mouse_override
# 
# # In any automation module:
# mouse_override.disable("Starting dig sequence")
# # ... perform clicks ...
# mouse_override.enable("Dig sequence complete")
#
# # Or using the instance directly:
# override = mouse_override.get_instance()
# override.disable("Map trade execution")
# # ... perform clicks ...
# override.enable("Map trade complete")
