#!/usr/bin/env python3
"""
Test script to verify the final dig module and map trade fixes
"""

import logging
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dig_module_execution_flow():
    """Test that dig module properly continues execution through all steps"""
    try:
        logger.info("🧪 Testing Dig Module Execution Flow...")
        
        from modules.dig import DigModule
        
        # Create dig module
        dig_module = DigModule()
        
        # Test the step execution logic
        logger.info("Testing step execution return values...")
        
        # Simulate step 2 (open chat) - should return False to continue
        dig_module.current_step = dig_module.STEP_OPEN_CHAT
        dig_module.dig_in_progress = True
        
        # Mock the _safe_click method to avoid actual clicking
        def mock_safe_click(x, y, desc=""):
            logger.info(f"Mock click at ({x}, {y}) - {desc}")
            return True
        
        dig_module._safe_click = mock_safe_click
        
        # Test step 2
        result = dig_module._step_open_chat()
        if result == False and dig_module.current_step == dig_module.STEP_FIND_TREASURE:
            logger.info("✅ Step 2 correctly returns False and advances to STEP_FIND_TREASURE")
        else:
            logger.error(f"❌ Step 2 returned {result}, current step: {dig_module.current_step}")
            return False
        
        # Test step 9 (return control) - should return True to complete
        dig_module.current_step = dig_module.STEP_RETURN_CONTROL
        
        # Mock pyautogui.press to avoid actual key press
        import pyautogui
        original_press = pyautogui.press
        def mock_press(key):
            logger.info(f"Mock key press: {key}")
        pyautogui.press = mock_press
        
        try:
            result = dig_module._step_return_control()
            if result == True and not dig_module.dig_in_progress:
                logger.info("✅ Step 9 correctly returns True and resets dig state")
            else:
                logger.error(f"❌ Step 9 returned {result}, dig_in_progress: {dig_module.dig_in_progress}")
                return False
        finally:
            pyautogui.press = original_press
        
        logger.info("✅ Dig module execution flow verified")
        return True
        
    except Exception as e:
        logger.error(f"❌ Dig module test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_map_trade_esc_recovery():
    """Test that map trade module executes ESC recovery after completion"""
    try:
        logger.info("🧪 Testing Map Trade ESC Recovery...")
        
        from modules.map_trade import MapTradeModule
        
        # Create map trade module
        map_trade = MapTradeModule()
        
        # Test the ESC recovery method directly
        logger.info("Testing ESC recovery method...")
        
        # Mock the controller and centralized scanner
        class MockCentralizedScanner:
            def trigger_esc_recovery(self, reason):
                logger.info(f"✅ ESC recovery triggered: {reason}")
                return True
        
        class MockController:
            def __init__(self):
                self.centralized_scanner = MockCentralizedScanner()
        
        map_trade.controller = MockController()
        
        # Test the ESC recovery method
        map_trade._execute_esc_recovery_after_completion()
        
        logger.info("✅ Map trade ESC recovery method verified")
        return True
        
    except Exception as e:
        logger.error(f"❌ Map trade test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    logger.info("🚀 Testing Final Dig Module and Map Trade Fixes...")
    logger.info("=" * 60)
    
    # Test dig module execution flow
    dig_test_ok = test_dig_module_execution_flow()
    
    # Test map trade ESC recovery
    map_trade_test_ok = test_map_trade_esc_recovery()
    
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST RESULTS:")
    logger.info(f"Dig Module Execution Flow: {'✅ PASSED' if dig_test_ok else '❌ FAILED'}")
    logger.info(f"Map Trade ESC Recovery: {'✅ PASSED' if map_trade_test_ok else '❌ FAILED'}")
    
    if dig_test_ok and map_trade_test_ok:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("💡 Expected behavior:")
        logger.info("   • Dig module will now continue through all steps instead of stopping after chat")
        logger.info("   • Dig module returns False to stay active, True only when complete")
        logger.info("   • Map trade will execute ESC recovery after completion")
        logger.info("   • Both modules properly integrate with centralized systems")
    else:
        logger.warning("\n⚠️ Some tests failed - manual investigation needed")
    
    return dig_test_ok and map_trade_test_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
