"""
Refactored Dig Module - Configuration-Driven Implementation
All hardcoded values removed - everything loaded from dig_module_config.json
"""

import time
import json
import pyautogui
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import logging

from modules.base_module import BaseModule

class DigModule(BaseModule):
    """
    Configuration-driven dig module with complete external control
    NO hardcoded values - everything loaded from dig_module_config.json
    """

    def __init__(self):
        super().__init__(name="dig", priority=-1, enabled=True)
        
        # Configuration file
        self.config_file = "dig_module_config.json"
        self.config_data = {}
        
        # State tracking
        self.dig_in_progress = False
        self.current_step = 0
        self.execution_start_time = None
        self.step_start_time = None
        self.step_attempts = 0
        self.execution_log = []
        
        # Load configuration
        self.reload_configuration()
        
        # Initialize step constants from config
        self._initialize_step_constants()
        
    def reload_configuration(self, config_data: Optional[Dict[str, Any]] = None):
        """Reload configuration from file or provided data"""
        try:
            if config_data:
                self.config_data = config_data
            else:
                with open(self.config_file, 'r') as f:
                    self.config_data = json.load(f)
            
            # Update module properties from config
            general_settings = self.config_data.get('general_settings', {})
            self.enabled = general_settings.get('enabled', True)
            self.priority = general_settings.get('priority', -1)
            
            self.logger.info(f"✅ Configuration reloaded from {self.config_file}")
            self._log_execution("Configuration reloaded successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Error loading configuration: {e}")
            self._load_default_configuration()
    
    def _load_default_configuration(self):
        """Load minimal default configuration if file loading fails"""
        self.config_data = {
            "general_settings": {
                "enabled": True,
                "priority": -1,
                "debug_mode": True,
                "execution_timeout": 300
            },
            "execution_steps": {},
            "trigger_templates": {}
        }
        self.logger.warning("Using default configuration due to loading error")
    
    def _initialize_step_constants(self):
        """Initialize step constants from configuration"""
        steps = self.config_data.get('execution_steps', {})
        
        # Map step names to numbers
        self.STEP_DETECT = 0
        self.STEP_OPEN_CHAT = 1
        self.STEP_FIND_TREASURE = 2
        self.STEP_VERIFY_NAVIGATION = 3
        self.STEP_DEPLOY_SQUAD = 4
        self.STEP_TIMER_MANAGEMENT = 5
        self.STEP_EXIT_MENU = 6
        
        # Create step mapping
        self.step_mapping = {
            1: 'step_1_open_chat',
            2: 'step_2_find_treasure',
            3: 'step_3_verify_navigation',
            4: 'step_4_deploy_squad',
            5: 'step_5_timer_management',
            6: 'step_6_exit_menu'
        }
    
    def custom_can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Check if dig module can execute based on configuration"""
        try:
            # If dig is in progress, continue execution
            if self.dig_in_progress:
                return True
            
            # Check trigger templates from configuration
            trigger_templates = self.config_data.get('trigger_templates', {})
            templates_detected = screen_data.get('templates_detected', {})
            templates_found = screen_data.get('templates_found', {})
            
            for template_name, template_config in trigger_templates.items():
                if not template_config.get('enabled', True):
                    continue
                
                # Check if template is detected
                detected = (template_name in templates_detected and templates_detected[template_name]) or \
                          (template_name in templates_found and len(templates_found.get(template_name, [])) > 0)
                
                if detected:
                    self.logger.info(f"🎯 Dig trigger detected: {template_name}")
                    self._log_execution(f"Trigger detected: {template_name}")
                    
                    # Set starting step based on trigger
                    execution_path = template_config.get('execution_path', 'full_sequence')
                    starting_step = template_config.get('starting_step', 1)
                    
                    if execution_path == 'chat_trigger':
                        self.current_step = self.STEP_FIND_TREASURE
                        self._log_execution(f"Chat trigger - starting from step {starting_step}")
                    elif execution_path == 'timer_trigger':
                        self.current_step = self.STEP_TIMER_MANAGEMENT
                        self._log_execution(f"Timer trigger - starting from step {starting_step}")
                    else:
                        self.current_step = self.STEP_OPEN_CHAT
                        self._log_execution(f"Full sequence - starting from step {starting_step}")
                    
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking dig availability: {e}")
            return False
    
    def custom_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Execute dig module based on configuration"""
        try:
            if not self.dig_in_progress:
                self._start_execution()
            
            # Execute current step
            return self._execute_current_step(screen_data)
            
        except Exception as e:
            self.logger.error(f"❌ Error in dig execution: {e}")
            self._log_execution(f"Execution error: {e}")
            self._end_execution()
            return False
    
    def _start_execution(self):
        """Start dig execution"""
        self.dig_in_progress = True
        self.execution_start_time = time.time()
        self.step_attempts = 0
        self._log_execution("🚀 Dig execution started")
        self.logger.info("🚀 Dig execution started")
    
    def _end_execution(self):
        """End dig execution"""
        self.dig_in_progress = False
        self.current_step = 0
        execution_time = time.time() - self.execution_start_time if self.execution_start_time else 0
        self._log_execution(f"⏹️ Dig execution completed in {execution_time:.1f}s")
        self.logger.info(f"⏹️ Dig execution completed in {execution_time:.1f}s")
    
    def _execute_current_step(self, screen_data: Dict[str, Any]) -> bool:
        """Execute the current step based on configuration"""
        try:
            step_config_key = self.step_mapping.get(self.current_step)
            if not step_config_key:
                self.logger.error(f"❌ Unknown step: {self.current_step}")
                self._end_execution()
                return False
            
            step_config = self.config_data.get('execution_steps', {}).get(step_config_key, {})
            if not step_config.get('enabled', True):
                self.logger.info(f"⏭️ Step {self.current_step} disabled, skipping")
                self._advance_to_next_step()
                return True
            
            step_name = step_config.get('name', f'Step {self.current_step}')
            self._log_execution(f"🔄 Executing: {step_name}")
            
            # Execute step based on type
            if self.current_step == self.STEP_OPEN_CHAT:
                result = self._execute_open_chat_step(step_config, screen_data)
            elif self.current_step == self.STEP_FIND_TREASURE:
                result = self._execute_find_treasure_step(step_config, screen_data)
            elif self.current_step == self.STEP_VERIFY_NAVIGATION:
                result = self._execute_verify_navigation_step(step_config, screen_data)
            elif self.current_step == self.STEP_DEPLOY_SQUAD:
                result = self._execute_deploy_squad_step(step_config, screen_data)
            elif self.current_step == self.STEP_TIMER_MANAGEMENT:
                result = self._execute_timer_management_step(step_config, screen_data)
            elif self.current_step == self.STEP_EXIT_MENU:
                result = self._execute_exit_menu_step(step_config, screen_data)
            else:
                self.logger.error(f"❌ Unhandled step: {self.current_step}")
                result = False
            
            if result:
                self._advance_to_next_step()
            else:
                self._handle_step_failure(step_config)
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error executing step {self.current_step}: {e}")
            self._log_execution(f"Step {self.current_step} error: {e}")
            return False
    
    def _execute_open_chat_step(self, step_config: Dict[str, Any], screen_data: Dict[str, Any]) -> bool:
        """Execute open chat step based on configuration"""
        try:
            actions = step_config.get('actions', [])
            
            for action in actions:
                action_type = action.get('type', 'click')
                
                if action_type == 'click':
                    coords = action.get('coordinates', [0, 0])
                    delay = action.get('delay_after', 1.0)
                    description = action.get('description', 'Click action')
                    
                    self._log_execution(f"  🖱️ {description} at ({coords[0]}, {coords[1]})")
                    pyautogui.click(coords[0], coords[1])
                    time.sleep(delay)
            
            # Verify step completion if verification templates are configured
            verification_templates = step_config.get('verification_templates', [])
            if verification_templates:
                return self._verify_step_completion(verification_templates, screen_data)
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error in open chat step: {e}")
            return False
    
    def _execute_find_treasure_step(self, step_config: Dict[str, Any], screen_data: Dict[str, Any]) -> bool:
        """Execute find treasure step with robust detection"""
        try:
            scan_templates = step_config.get('scan_templates', [])
            
            for template_config in scan_templates:
                template_name = template_config.get('name')
                if not template_name:
                    continue
                
                # Check if template is detected
                templates_found = screen_data.get('templates_found', {})
                templates_detected = screen_data.get('templates_detected', {})
                
                detected = (template_name in templates_detected and templates_detected[template_name]) or \
                          (template_name in templates_found and len(templates_found.get(template_name, [])) > 0)
                
                if detected:
                    self._log_execution(f"  ✅ Found template: {template_name}")
                    
                    # Get click coordinates
                    if template_name in templates_found and templates_found[template_name]:
                        match = templates_found[template_name][0]
                        
                        # Apply click adjustment from configuration
                        click_adjustment = template_config.get('click_adjustment', {})
                        x_offset_percent = click_adjustment.get('x_offset_percent', 50)
                        y_offset_percent = click_adjustment.get('y_offset_percent', 50)
                        
                        x = match[0] + int(match[2] * x_offset_percent / 100)
                        y = match[1] + int(match[3] * y_offset_percent / 100)
                        
                        self._log_execution(f"  🖱️ Clicking {template_name} at ({x}, {y})")
                        pyautogui.click(x, y)
                        time.sleep(1.0)
                        return True
            
            # Use fallback action if configured
            fallback_action = step_config.get('fallback_action')
            if fallback_action:
                coords = fallback_action.get('coordinates', [0, 0])
                description = fallback_action.get('description', 'Fallback action')
                self._log_execution(f"  🔄 {description} at ({coords[0]}, {coords[1]})")
                pyautogui.click(coords[0], coords[1])
                time.sleep(fallback_action.get('delay_after', 1.0))
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Error in find treasure step: {e}")
            return False

    def _execute_verify_navigation_step(self, step_config: Dict[str, Any], screen_data: Dict[str, Any]) -> bool:
        """Execute verify navigation step"""
        try:
            # Check verification templates (e.g., Base template)
            verification_templates = step_config.get('verification_templates', [])
            if not self._verify_step_completion(verification_templates, screen_data):
                return False

            # Look for send_to_dig templates
            send_to_dig_templates = step_config.get('send_to_dig_templates', [])
            templates_found = screen_data.get('templates_found', {})
            templates_detected = screen_data.get('templates_detected', {})

            for template_config in send_to_dig_templates:
                template_name = template_config.get('name')
                if not template_name:
                    continue

                detected = (template_name in templates_detected and templates_detected[template_name]) or \
                          (template_name in templates_found and len(templates_found.get(template_name, [])) > 0)

                if detected:
                    coords = template_config.get('coordinates', [1279, 646])
                    self._log_execution(f"  ✅ Found {template_name}, clicking at ({coords[0]}, {coords[1]})")
                    pyautogui.click(coords[0], coords[1])
                    time.sleep(1.5)
                    return True

            # If no send_to_dig templates found, wait
            self._log_execution("  ⏳ No send_to_dig templates found, waiting...")
            return True

        except Exception as e:
            self.logger.error(f"❌ Error in verify navigation step: {e}")
            return False

    def _execute_deploy_squad_step(self, step_config: Dict[str, Any], screen_data: Dict[str, Any]) -> bool:
        """Execute deploy squad step"""
        try:
            actions = step_config.get('actions', [])
            templates_found = screen_data.get('templates_found', {})

            for action in actions:
                action_type = action.get('type', 'click')

                if action_type == 'template_click':
                    template_name = action.get('template')
                    fallback_coords = action.get('fallback_coordinates', [0, 0])
                    delay = action.get('delay_after', 1.0)
                    description = action.get('description', 'Template click')

                    # Try template click first
                    if template_name and template_name in templates_found and templates_found[template_name]:
                        match = templates_found[template_name][0]
                        x, y = match[0] + match[2]//2, match[1] + match[3]//2
                        self._log_execution(f"  🎯 {description} - template at ({x}, {y})")
                        pyautogui.click(x, y)
                    else:
                        # Use fallback coordinates
                        self._log_execution(f"  🖱️ {description} - fallback at ({fallback_coords[0]}, {fallback_coords[1]})")
                        pyautogui.click(fallback_coords[0], fallback_coords[1])

                    time.sleep(delay)

                elif action_type == 'click':
                    coords = action.get('coordinates', [0, 0])
                    delay = action.get('delay_after', 1.0)
                    description = action.get('description', 'Click action')

                    self._log_execution(f"  🖱️ {description} at ({coords[0]}, {coords[1]})")
                    pyautogui.click(coords[0], coords[1])
                    time.sleep(delay)

            return True

        except Exception as e:
            self.logger.error(f"❌ Error in deploy squad step: {e}")
            return False

    def _execute_timer_management_step(self, step_config: Dict[str, Any], screen_data: Dict[str, Any]) -> bool:
        """Execute timer management step with OCR detection"""
        try:
            timer_detection_config = step_config.get('timer_detection', {})
            rapid_clicking_config = step_config.get('rapid_clicking', {})

            # Detect timer using configured regions and OCR
            timer_seconds = self._detect_timer_from_config(timer_detection_config)

            if timer_seconds is not None:
                trigger_threshold = rapid_clicking_config.get('trigger_threshold', 10)

                if timer_seconds <= trigger_threshold:
                    # Start rapid clicking
                    click_speed = rapid_clicking_config.get('click_speed', 0.005)
                    duration = rapid_clicking_config.get('duration', 20.0)
                    coords = rapid_clicking_config.get('coordinates', [1279, 646])

                    self._log_execution(f"  ⚡ Timer at {timer_seconds}s - starting rapid clicking")
                    self._log_execution(f"  🖱️ Rapid clicking at ({coords[0]}, {coords[1]}) for {duration}s")

                    start_time = time.time()
                    clicks = 0

                    while time.time() - start_time < duration:
                        pyautogui.click(coords[0], coords[1])
                        clicks += 1
                        time.sleep(click_speed)

                    self._log_execution(f"  ✅ Rapid clicking completed - {clicks} clicks")
                    return True
                else:
                    self._log_execution(f"  ⏳ Timer at {timer_seconds}s - waiting for {trigger_threshold}s threshold")
                    return True
            else:
                self._log_execution("  🔍 Timer not detected - continuing to scan")
                return True

        except Exception as e:
            self.logger.error(f"❌ Error in timer management step: {e}")
            return False

    def _execute_exit_menu_step(self, step_config: Dict[str, Any], screen_data: Dict[str, Any]) -> bool:
        """Execute exit menu step"""
        try:
            esc_recovery_config = step_config.get('esc_recovery', {})

            if not esc_recovery_config.get('enabled', True):
                self._log_execution("  ⏭️ ESC recovery disabled, completing step")
                self._end_execution()
                return True

            max_esc_presses = esc_recovery_config.get('max_esc_presses', 5)
            delay_between_esc = esc_recovery_config.get('delay_between_esc', 0.5)
            target_template = esc_recovery_config.get('target_template', 'quit_game_dialog')

            templates_found = screen_data.get('templates_found', {})

            # Check if target template is already visible
            if target_template in templates_found:
                if esc_recovery_config.get('final_esc_after_dialog', True):
                    self._log_execution(f"  ✅ {target_template} found - final ESC press")
                    pyautogui.press('escape')
                    time.sleep(1.0)

                self._log_execution("  ✅ Exit sequence completed")
                self._end_execution()
                return True

            # Press ESC and wait
            if self.step_attempts < max_esc_presses:
                self._log_execution(f"  ⌨️ ESC press {self.step_attempts + 1}/{max_esc_presses}")
                pyautogui.press('escape')
                time.sleep(delay_between_esc)
                return True
            else:
                self._log_execution("  ⚠️ Max ESC presses reached - completing step")
                self._end_execution()
                return True

        except Exception as e:
            self.logger.error(f"❌ Error in exit menu step: {e}")
            return False

    def _detect_timer_from_config(self, timer_detection_config: Dict[str, Any]) -> Optional[int]:
        """Detect timer using configuration-defined regions and OCR settings"""
        try:
            import cv2
            import numpy as np
            import pyautogui

            regions = timer_detection_config.get('regions', [])
            ocr_settings = timer_detection_config.get('ocr_settings', {})
            parsing_settings = timer_detection_config.get('parsing', {})

            for region_config in regions:
                region_name = region_config.get('name', 'unknown')
                coordinates = region_config.get('coordinates', [0, 0, 100, 100])
                preprocessing = region_config.get('preprocessing', {})

                try:
                    # Capture region
                    screenshot = pyautogui.screenshot(region=tuple(coordinates))
                    img = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
                    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

                    # Apply preprocessing from configuration
                    if preprocessing.get('color_inversion', False):
                        gray = cv2.bitwise_not(gray)

                    threshold_value = preprocessing.get('threshold', 127)
                    _, thresh = cv2.threshold(gray, threshold_value, 255, cv2.THRESH_BINARY)

                    if preprocessing.get('morphology', False):
                        kernel = np.ones((2,2), np.uint8)
                        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
                        thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)

                    # Try OCR with configured settings
                    try:
                        import pytesseract

                        psm_modes = ocr_settings.get('psm_modes', [6, 8])
                        whitelist = ocr_settings.get('whitelist', '0123456789:')

                        for psm_mode in psm_modes:
                            config = f'--psm {psm_mode} -c tessedit_char_whitelist={whitelist}'
                            text = pytesseract.image_to_string(thresh, config=config).strip()

                            if text:
                                seconds = self._parse_timer_text_from_config(text, parsing_settings)
                                if seconds is not None:
                                    self._log_execution(f"  ✅ Timer detected in {region_name}: {text} = {seconds}s")
                                    return seconds

                    except ImportError:
                        self.logger.warning("pytesseract not available for timer detection")
                        break
                    except Exception as e:
                        self.logger.debug(f"OCR failed for {region_name}: {e}")
                        continue

                except Exception as e:
                    self.logger.debug(f"Timer region {region_name} processing failed: {e}")
                    continue

            return None

        except Exception as e:
            self.logger.error(f"❌ Timer detection error: {e}")
            return None

    def _parse_timer_text_from_config(self, text: str, parsing_settings: Dict[str, Any]) -> Optional[int]:
        """Parse timer text using configuration settings"""
        try:
            text = text.strip().replace(' ', '').replace('\n', '')
            formats = parsing_settings.get('formats', ['MM:SS', 'SS'])
            max_seconds = parsing_settings.get('max_seconds', 3600)
            min_seconds = parsing_settings.get('min_seconds', 0)

            # Try MM:SS format
            if 'MM:SS' in formats and ':' in text:
                parts = text.split(':')
                if len(parts) == 2:
                    try:
                        minutes = int(parts[0])
                        seconds = int(parts[1])
                        total_seconds = minutes * 60 + seconds
                        if min_seconds <= total_seconds <= max_seconds:
                            return total_seconds
                    except ValueError:
                        pass

            # Try SS format
            if 'SS' in formats:
                clean_text = ''.join(c for c in text if c.isdigit())
                if clean_text:
                    try:
                        seconds = int(clean_text)
                        if min_seconds <= seconds <= max_seconds:
                            return seconds
                    except ValueError:
                        pass

            return None

        except Exception as e:
            self.logger.debug(f"Timer text parsing error: {e}")
            return None

    def _verify_step_completion(self, verification_templates: List[Dict[str, Any]], screen_data: Dict[str, Any]) -> bool:
        """Verify step completion using configured templates"""
        try:
            templates_found = screen_data.get('templates_found', {})
            templates_detected = screen_data.get('templates_detected', {})

            for template_config in verification_templates:
                template_name = template_config.get('name')
                required = template_config.get('required', True)

                if not template_name:
                    continue

                detected = (template_name in templates_detected and templates_detected[template_name]) or \
                          (template_name in templates_found and len(templates_found.get(template_name, [])) > 0)

                if required and not detected:
                    self._log_execution(f"  ❌ Required template {template_name} not found")
                    return False
                elif detected:
                    self._log_execution(f"  ✅ Verification template {template_name} found")

            return True

        except Exception as e:
            self.logger.error(f"❌ Error verifying step completion: {e}")
            return False

    def _advance_to_next_step(self):
        """Advance to the next step"""
        self.current_step += 1
        self.step_attempts = 0
        self.step_start_time = time.time()

        # Check if we've completed all steps
        if self.current_step > self.STEP_EXIT_MENU:
            self._send_completion_message()
            self._end_execution()

    def _handle_step_failure(self, step_config: Dict[str, Any]):
        """Handle step failure with retry logic"""
        self.step_attempts += 1
        max_attempts = step_config.get('max_attempts', 3)

        if self.step_attempts >= max_attempts:
            self._log_execution(f"  ❌ Step {self.current_step} failed after {max_attempts} attempts")
            self.logger.error(f"Step {self.current_step} failed after {max_attempts} attempts")
            self._end_execution()
        else:
            self._log_execution(f"  🔄 Step {self.current_step} retry {self.step_attempts}/{max_attempts}")

    def _send_completion_message(self):
        """Send completion message if configured"""
        try:
            completion_settings = self.config_data.get('completion_settings', {})
            message_config = completion_settings.get('thank_you_message', {})

            if not message_config.get('enabled', True):
                return

            message = message_config.get('message', 'Thank you for the dig!')
            chat_coords = message_config.get('chat_coordinates', [1073, 1327])
            send_key = message_config.get('send_key', 'Return')

            self._log_execution(f"  💬 Sending completion message: '{message}'")

            # Click chat input
            pyautogui.click(chat_coords[0], chat_coords[1])
            time.sleep(0.5)

            # Type message
            pyautogui.typewrite(message)
            time.sleep(0.5)

            # Send message
            pyautogui.press(send_key.lower())
            time.sleep(1.0)

        except Exception as e:
            self.logger.error(f"❌ Error sending completion message: {e}")

    def _log_execution(self, message: str):
        """Log execution message with timestamp"""
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        log_entry = f"[{timestamp}] {message}"
        self.execution_log.append(log_entry)

        # Keep only last 100 log entries
        if len(self.execution_log) > 100:
            self.execution_log = self.execution_log[-100:]

        # Log to main logger if debug mode is enabled
        if self.config_data.get('general_settings', {}).get('debug_mode', False):
            self.logger.info(log_entry)

    def get_execution_log(self) -> List[str]:
        """Get current execution log"""
        return self.execution_log.copy()

    def get_current_status(self) -> Dict[str, Any]:
        """Get current dig module status"""
        execution_time = time.time() - self.execution_start_time if self.execution_start_time else 0

        return {
            'dig_in_progress': self.dig_in_progress,
            'current_step': self.current_step,
            'step_attempts': self.step_attempts,
            'execution_time': execution_time,
            'enabled': self.enabled,
            'last_log_entries': self.execution_log[-5:] if self.execution_log else []
        }

    def force_stop(self):
        """Force stop dig execution"""
        self._log_execution("🛑 Dig execution force stopped")
        self._end_execution()

    def test_step(self, step_number: int, screen_data: Dict[str, Any]) -> bool:
        """Test individual step execution"""
        try:
            old_step = self.current_step
            old_progress = self.dig_in_progress

            self.current_step = step_number
            self.dig_in_progress = True
            self.step_attempts = 0

            self._log_execution(f"🧪 Testing step {step_number}")
            result = self._execute_current_step(screen_data)

            # Restore original state
            self.current_step = old_step
            self.dig_in_progress = old_progress

            return result

        except Exception as e:
            self.logger.error(f"❌ Error testing step {step_number}: {e}")
            return False

    # Base module compatibility methods
    def execute(self, screen_data: Dict[str, Any]) -> bool:
        """Bridge method for base module compatibility"""
        return self.custom_execute(screen_data)

    def get_cooldown(self) -> float:
        """Get cooldown time from configuration"""
        return self.config_data.get('general_settings', {}).get('cooldown', 0.0)

    def get_dig_status(self) -> str:
        """Get current dig status as string"""
        if self.dig_in_progress:
            step_names = {
                1: "Opening Chat",
                2: "Finding Treasure",
                3: "Verifying Navigation",
                4: "Deploying Squad",
                5: "Timer Management",
                6: "Exiting Menu"
            }
            step_name = step_names.get(self.current_step, f"Step {self.current_step}")
            return f"Active - {step_name} (Attempt {self.step_attempts + 1})"
        else:
            return "Inactive"
