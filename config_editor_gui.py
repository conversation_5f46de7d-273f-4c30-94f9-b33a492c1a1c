"""
Configuration Editor GUI for Last War automation modules
Allows easy management of templates, coordinates, and scan regions
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import json
import os
import time
from typing import Dict, Any, Optional, Tuple
import pyautogui
from PIL import Image, ImageTk
import cv2
import numpy as np

from module_config_manager import ModuleConfigManager


class ConfigEditorGUI:
    """GUI for editing module configurations"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Last War Module Configuration Editor")
        self.root.geometry("1200x800")
        
        # Initialize config manager
        self.config_manager = ModuleConfigManager()
        
        # Current selections
        self.current_module = None
        self.current_screenshot = None
        
        # Create GUI
        self._create_widgets()
        self._load_modules()
        
        # Screenshot capture mode
        self.capture_mode = None  # 'coordinate', 'region', 'template'
        self.capture_callback = None
        self.save_timer = None  # For debounced saving
    
    def _create_widgets(self):
        """Create all GUI widgets"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Module Configuration Editor", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Left panel - Module selection
        self._create_module_panel(main_frame)
        
        # Right panel - Configuration details
        self._create_config_panel(main_frame)
        
        # Bottom panel - Tools
        self._create_tools_panel(main_frame)
    
    def _create_module_panel(self, parent):
        """Create module selection panel"""
        module_frame = ttk.LabelFrame(parent, text="Modules", padding="10")
        module_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        module_frame.columnconfigure(0, weight=1)
        module_frame.rowconfigure(1, weight=1)
        
        # Module listbox
        self.module_listbox = tk.Listbox(module_frame, height=15)
        self.module_listbox.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.module_listbox.bind('<<ListboxSelect>>', self._on_module_select)
        
        # Module buttons
        button_frame = ttk.Frame(module_frame)
        button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Button(button_frame, text="Add Module", 
                  command=self._add_module).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(button_frame, text="Delete Module", 
                  command=self._delete_module).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(button_frame, text="Export", 
                  command=self._export_module).grid(row=0, column=2)
    
    def _create_config_panel(self, parent):
        """Create configuration details panel"""
        config_frame = ttk.LabelFrame(parent, text="Configuration", padding="10")
        config_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        config_frame.columnconfigure(0, weight=1)
        config_frame.rowconfigure(0, weight=1)
        
        # Notebook for different config types
        self.config_notebook = ttk.Notebook(config_frame)
        self.config_notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Templates tab
        self._create_templates_tab()
        
        # Text Patterns tab
        self._create_text_patterns_tab()
        
        # Click Coordinates tab
        self._create_coordinates_tab()
        
        # Scan Regions tab
        self._create_regions_tab()
        
        # Actions tab
        self._create_actions_tab()

        # Settings tab
        self._create_settings_tab()
    
    def _create_templates_tab(self):
        """Create templates configuration tab"""
        templates_frame = ttk.Frame(self.config_notebook, padding="10")
        self.config_notebook.add(templates_frame, text="Templates")
        
        templates_frame.columnconfigure(0, weight=1)
        templates_frame.rowconfigure(1, weight=1)
        
        # Templates list
        list_frame = ttk.Frame(templates_frame)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # Treeview for templates
        columns = ('Name', 'Threshold', 'Required', 'Exists')
        self.templates_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.templates_tree.heading(col, text=col)
            self.templates_tree.column(col, width=100)
        
        self.templates_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbar
        templates_scroll = ttk.Scrollbar(list_frame, orient="vertical", command=self.templates_tree.yview)
        templates_scroll.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.templates_tree.configure(yscrollcommand=templates_scroll.set)
        
        # Template buttons
        template_buttons = ttk.Frame(templates_frame)
        template_buttons.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Button(template_buttons, text="Add Template",
                  command=self._add_template).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(template_buttons, text="Edit Template",
                  command=self._edit_template).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(template_buttons, text="Capture Template",
                  command=self._capture_template).grid(row=0, column=2, padx=(0, 5))
        ttk.Button(template_buttons, text="Delete Template",
                  command=self._delete_template).grid(row=0, column=3, padx=(0, 5))
        ttk.Button(template_buttons, text="Test Template",
                  command=self._test_template).grid(row=0, column=4)
    
    def _create_text_patterns_tab(self):
        """Create text patterns configuration tab"""
        text_frame = ttk.Frame(self.config_notebook, padding="10")
        self.config_notebook.add(text_frame, text="Text Patterns")
        
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(1, weight=1)
        
        # Text patterns list
        columns = ('Text', 'Region', 'Required')
        self.text_tree = ttk.Treeview(text_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.text_tree.heading(col, text=col)
            self.text_tree.column(col, width=150)
        
        self.text_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Text pattern buttons
        text_buttons = ttk.Frame(text_frame)
        text_buttons.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Button(text_buttons, text="Add Text Pattern",
                  command=self._add_text_pattern).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(text_buttons, text="Edit Text Pattern",
                  command=self._edit_text_pattern).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(text_buttons, text="Delete Text Pattern",
                  command=self._delete_text_pattern).grid(row=0, column=2, padx=(0, 5))
        ttk.Button(text_buttons, text="Test OCR",
                  command=self._test_ocr).grid(row=0, column=3)
    
    def _create_coordinates_tab(self):
        """Create click coordinates configuration tab"""
        coord_frame = ttk.Frame(self.config_notebook, padding="10")
        self.config_notebook.add(coord_frame, text="Click Coordinates")
        
        coord_frame.columnconfigure(0, weight=1)
        coord_frame.rowconfigure(1, weight=1)
        
        # Create frame for coordinates with inline editing
        coord_list_frame = ttk.Frame(coord_frame)
        coord_list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        coord_list_frame.columnconfigure(0, weight=1)
        coord_list_frame.rowconfigure(0, weight=1)

        # Coordinates list with inline editing
        columns = ('Enabled', 'Name', 'X', 'Y', 'Delay', 'Repeat', 'ESC', 'Description')
        self.coord_tree = ttk.Treeview(coord_list_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.coord_tree.heading(col, text=col)
            if col == 'Enabled':
                self.coord_tree.column(col, width=70)
            elif col == 'Name':
                self.coord_tree.column(col, width=110)
            elif col == 'X' or col == 'Y':
                self.coord_tree.column(col, width=70)
            elif col == 'Delay':
                self.coord_tree.column(col, width=70)
            elif col == 'Repeat':
                self.coord_tree.column(col, width=70)
            elif col == 'ESC':
                self.coord_tree.column(col, width=50)
            else:  # Description
                self.coord_tree.column(col, width=160)

        self.coord_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbar for coordinates
        coord_scrollbar = ttk.Scrollbar(coord_list_frame, orient="vertical", command=self.coord_tree.yview)
        coord_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.coord_tree.configure(yscrollcommand=coord_scrollbar.set)

        # Inline delay editing frame
        delay_edit_frame = ttk.LabelFrame(coord_list_frame, text="Quick Delay Edit", padding="5")
        delay_edit_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Label(delay_edit_frame, text="Selected:").grid(row=0, column=0, padx=(0, 5))
        self.selected_coord_label = ttk.Label(delay_edit_frame, text="None", foreground="gray")
        self.selected_coord_label.grid(row=0, column=1, padx=(0, 10))

        # Enabled checkbox
        self.enabled_var = tk.BooleanVar(value=True)
        self.enabled_checkbox = ttk.Checkbutton(delay_edit_frame, text="Enabled", variable=self.enabled_var)
        self.enabled_checkbox.grid(row=0, column=2, padx=(0, 10))

        ttk.Label(delay_edit_frame, text="Delay:").grid(row=0, column=3, padx=(0, 5))
        self.delay_var = tk.StringVar(value="2.0")
        self.delay_spinbox = ttk.Spinbox(delay_edit_frame, from_=0.01, to=10.0, increment=0.01,
                                        textvariable=self.delay_var, width=8,
                                        command=self._on_delay_change)
        self.delay_spinbox.grid(row=0, column=4, padx=(0, 10))
        self.delay_spinbox.bind('<KeyRelease>', self._on_delay_change)

        ttk.Label(delay_edit_frame, text="Repeat:").grid(row=0, column=5, padx=(0, 5))
        self.repeat_var = tk.StringVar(value="1")
        self.repeat_spinbox = ttk.Spinbox(delay_edit_frame, from_=1, to=20, increment=1,
                                         textvariable=self.repeat_var, width=6,
                                         command=self._on_repeat_change)
        self.repeat_spinbox.grid(row=0, column=6, padx=(0, 10))
        self.repeat_spinbox.bind('<KeyRelease>', self._on_repeat_change)

        ttk.Button(delay_edit_frame, text="Apply", command=self._apply_changes).grid(row=0, column=7, padx=(0, 5))

        # Quick enable all button
        ttk.Button(delay_edit_frame, text="Enable All", command=self._enable_all_steps).grid(row=0, column=8, padx=(5, 0))

        # Bind selection event
        self.coord_tree.bind('<<TreeviewSelect>>', self._on_coord_select)
        
        # Coordinate buttons
        coord_buttons = ttk.Frame(coord_frame)
        coord_buttons.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Button(coord_buttons, text="Add Coordinate",
                  command=self._add_coordinate).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(coord_buttons, text="Edit Coordinate",
                  command=self._edit_coordinate).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(coord_buttons, text="Capture Coordinate",
                  command=self._capture_coordinate).grid(row=0, column=2, padx=(0, 5))
        ttk.Button(coord_buttons, text="Delete Coordinate",
                  command=self._delete_coordinate).grid(row=0, column=3, padx=(0, 5))
        ttk.Button(coord_buttons, text="Test Click",
                  command=self._test_click).grid(row=0, column=4, padx=(0, 10))

        # Live reload button
        ttk.Button(coord_buttons, text="🔄 Reload Module",
                  command=self._reload_module_live).grid(row=0, column=5, padx=(0, 5))

        # Save all button
        ttk.Button(coord_buttons, text="💾 Save All",
                  command=self._save_all_changes).grid(row=0, column=6)
    
    def _create_regions_tab(self):
        """Create scan regions configuration tab"""
        region_frame = ttk.Frame(self.config_notebook, padding="10")
        self.config_notebook.add(region_frame, text="Scan Regions")
        
        region_frame.columnconfigure(0, weight=1)
        region_frame.rowconfigure(1, weight=1)
        
        # Regions list
        columns = ('Name', 'X', 'Y', 'Width', 'Height')
        self.region_tree = ttk.Treeview(region_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.region_tree.heading(col, text=col)
            self.region_tree.column(col, width=100)
        
        self.region_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Region buttons
        region_buttons = ttk.Frame(region_frame)
        region_buttons.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Button(region_buttons, text="Add Region",
                  command=self._add_region).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(region_buttons, text="Edit Region",
                  command=self._edit_region).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(region_buttons, text="Capture Region",
                  command=self._capture_region).grid(row=0, column=2, padx=(0, 5))
        ttk.Button(region_buttons, text="Delete Region",
                  command=self._delete_region).grid(row=0, column=3, padx=(0, 5))
        ttk.Button(region_buttons, text="Preview Region",
                  command=self._preview_region).grid(row=0, column=4)
    
    def _create_actions_tab(self):
        """Create actions configuration tab"""
        actions_frame = ttk.Frame(self.config_notebook, padding="10")
        self.config_notebook.add(actions_frame, text="Actions")
        
        actions_frame.columnconfigure(0, weight=1)
        actions_frame.rowconfigure(1, weight=1)
        
        # Actions list
        columns = ('Type', 'Target', 'Parameters')
        self.actions_tree = ttk.Treeview(actions_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.actions_tree.heading(col, text=col)
            self.actions_tree.column(col, width=150)
        
        self.actions_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Action buttons
        action_buttons = ttk.Frame(actions_frame)
        action_buttons.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Button(action_buttons, text="Add Action", 
                  command=self._add_action).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(action_buttons, text="Edit Action", 
                  command=self._edit_action).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(action_buttons, text="Delete Action", 
                  command=self._delete_action).grid(row=0, column=2, padx=(0, 5))
        ttk.Button(action_buttons, text="Move Up", 
                  command=self._move_action_up).grid(row=0, column=3, padx=(0, 5))
        ttk.Button(action_buttons, text="Move Down",
                  command=self._move_action_down).grid(row=0, column=4)

    def _create_settings_tab(self):
        """Create settings configuration tab"""
        settings_frame = ttk.Frame(self.config_notebook, padding="10")
        self.config_notebook.add(settings_frame, text="Settings")

        settings_frame.columnconfigure(1, weight=1)

        # Module Settings Section
        module_settings_frame = ttk.LabelFrame(settings_frame, text="Module Settings", padding="10")
        module_settings_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        module_settings_frame.columnconfigure(1, weight=1)

        # Enabled checkbox
        ttk.Label(module_settings_frame, text="Enabled:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.enabled_var = tk.BooleanVar()
        ttk.Checkbutton(module_settings_frame, variable=self.enabled_var,
                       command=self._update_enabled).grid(row=0, column=1, sticky=tk.W)

        # Priority
        ttk.Label(module_settings_frame, text="Priority:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.priority_var = tk.StringVar()
        self.priority_var.trace('w', lambda *args: self._update_priority())
        priority_spinbox = ttk.Spinbox(module_settings_frame, from_=1, to=10, width=10,
                                      textvariable=self.priority_var, command=self._update_priority)
        priority_spinbox.grid(row=1, column=1, sticky=tk.W, pady=(5, 0))

        # Cooldown
        ttk.Label(module_settings_frame, text="Cooldown (seconds):").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.cooldown_var = tk.StringVar()
        self.cooldown_var.trace('w', lambda *args: self._update_cooldown())
        cooldown_spinbox = ttk.Spinbox(module_settings_frame, from_=1, to=3600, width=10,
                                      textvariable=self.cooldown_var, command=self._update_cooldown)
        cooldown_spinbox.grid(row=2, column=1, sticky=tk.W, pady=(5, 0))

        # Detection Settings Section
        detection_frame = ttk.LabelFrame(settings_frame, text="Detection Settings", padding="10")
        detection_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        detection_frame.columnconfigure(1, weight=1)

        # Scan interval
        ttk.Label(detection_frame, text="Scan Interval (seconds):").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.scan_interval_var = tk.StringVar()
        self.scan_interval_var.trace('w', lambda *args: self._update_scan_interval())
        scan_interval_spinbox = ttk.Spinbox(detection_frame, from_=0.5, to=60, increment=0.5, width=10,
                                           textvariable=self.scan_interval_var, command=self._update_scan_interval)
        scan_interval_spinbox.grid(row=0, column=1, sticky=tk.W)

        # Template threshold
        ttk.Label(detection_frame, text="Default Template Threshold:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.threshold_var = tk.StringVar()
        self.threshold_var.trace('w', lambda *args: self._update_threshold())
        threshold_spinbox = ttk.Spinbox(detection_frame, from_=0.1, to=1.0, increment=0.05, width=10,
                                       textvariable=self.threshold_var, command=self._update_threshold)
        threshold_spinbox.grid(row=1, column=1, sticky=tk.W, pady=(5, 0))

        # Advanced Settings Section
        advanced_frame = ttk.LabelFrame(settings_frame, text="Advanced Settings", padding="10")
        advanced_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        advanced_frame.columnconfigure(1, weight=1)

        # Max attempts
        ttk.Label(advanced_frame, text="Max Attempts:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.max_attempts_var = tk.StringVar()
        max_attempts_spinbox = ttk.Spinbox(advanced_frame, from_=1, to=20, width=10,
                                          textvariable=self.max_attempts_var, command=self._update_max_attempts)
        max_attempts_spinbox.grid(row=0, column=1, sticky=tk.W)

        # Wait time between actions
        ttk.Label(advanced_frame, text="Wait Time (seconds):").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.wait_time_var = tk.StringVar()
        wait_time_spinbox = ttk.Spinbox(advanced_frame, from_=0.1, to=10, increment=0.1, width=10,
                                       textvariable=self.wait_time_var, command=self._update_wait_time)
        wait_time_spinbox.grid(row=1, column=1, sticky=tk.W, pady=(5, 0))

        # Settings buttons
        settings_buttons = ttk.Frame(settings_frame)
        settings_buttons.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Button(settings_buttons, text="Reset to Defaults",
                  command=self._reset_settings).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(settings_buttons, text="Apply Settings",
                  command=self._apply_settings).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(settings_buttons, text="Export Settings",
                  command=self._export_settings).grid(row=0, column=2)

    def _create_tools_panel(self, parent):
        """Create tools panel"""
        tools_frame = ttk.LabelFrame(parent, text="Tools", padding="10")
        tools_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Button(tools_frame, text="🎯 Setup Templates",
                  command=self._setup_templates).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(tools_frame, text="Take Screenshot",
                  command=self._take_screenshot).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(tools_frame, text="Import Config",
                  command=self._import_config).grid(row=0, column=2, padx=(0, 10))
        ttk.Button(tools_frame, text="Export All",
                  command=self._export_all).grid(row=0, column=3, padx=(0, 10))
        ttk.Button(tools_frame, text="Validate Config",
                  command=self._validate_config).grid(row=0, column=4, padx=(0, 10))
        ttk.Button(tools_frame, text="Save All",
                  command=self._save_all).grid(row=0, column=5, padx=(0, 10))
        
        # Status label
        self.status_label = ttk.Label(tools_frame, text="Ready")
        self.status_label.grid(row=0, column=6, padx=(20, 0))
    
    def _load_modules(self):
        """Load modules into the listbox"""
        self.module_listbox.delete(0, tk.END)
        modules = self.config_manager.list_modules()
        for module in modules:
            self.module_listbox.insert(tk.END, module)
    
    def _on_module_select(self, event):
        """Handle module selection"""
        selection = self.module_listbox.curselection()
        if selection:
            self.current_module = self.module_listbox.get(selection[0])
            self._load_module_config()

    def _select_module(self, module_name):
        """Programmatically select a module (for command line usage)"""
        try:
            # Find the module in the listbox
            for i in range(self.module_listbox.size()):
                if self.module_listbox.get(i) == module_name:
                    # Select the module
                    self.module_listbox.selection_clear(0, tk.END)
                    self.module_listbox.selection_set(i)
                    self.module_listbox.see(i)

                    # Load the module config
                    self.current_module = module_name
                    self._load_module_config()
                    return True

            return False
        except Exception as e:
            print(f"Error selecting module {module_name}: {str(e)}")
            return False
    
    def _load_module_config(self):
        """Load configuration for selected module"""
        if not self.current_module:
            return
        
        # Load templates
        self._load_templates()
        
        # Load text patterns
        self._load_text_patterns()
        
        # Load coordinates
        self._load_coordinates()
        
        # Load regions
        self._load_regions()
        
        # Load actions
        self._load_actions()

        # Load settings
        self._load_module_settings()

        self.status_label.config(text=f"Loaded config for {self.current_module}")
    
    def _load_templates(self):
        """Load templates into tree view"""
        # Clear existing items
        for item in self.templates_tree.get_children():
            self.templates_tree.delete(item)
        
        templates = self.config_manager.get_templates(self.current_module)
        for template in templates:
            # Check if template file exists
            template_path = os.path.join('templates', f"{template['name']}.png")
            exists = "Yes" if os.path.exists(template_path) else "No"
            
            self.templates_tree.insert('', 'end', values=(
                template['name'],
                template.get('threshold', 0.8),
                "Yes" if template.get('required', False) else "No",
                exists
            ))
    
    def _load_text_patterns(self):
        """Load text patterns into tree view"""
        for item in self.text_tree.get_children():
            self.text_tree.delete(item)
        
        patterns = self.config_manager.get_text_patterns(self.current_module)
        for pattern in patterns:
            self.text_tree.insert('', 'end', values=(
                pattern['text'],
                pattern.get('region', 'full_screen'),
                "Yes" if pattern.get('required', False) else "No"
            ))
    
    def _load_coordinates(self):
        """Load coordinates into tree view"""
        for item in self.coord_tree.get_children():
            self.coord_tree.delete(item)
        
        coordinates = self.config_manager.get_click_coordinates(self.current_module)
        for coord in coordinates:
            enabled_text = "✓" if coord.get('enabled', True) else "✗"
            esc_text = "ESC" if coord.get('use_esc_key', False) else "Click"
            self.coord_tree.insert('', 'end', values=(
                enabled_text,
                coord['name'],
                coord['x'],
                coord['y'],
                coord.get('delay', 2.0),  # Default delay if not specified
                coord.get('repeat', 1),   # Default repeat if not specified
                esc_text,
                coord.get('description', '')
            ))
    
    def _load_regions(self):
        """Load regions into tree view"""
        for item in self.region_tree.get_children():
            self.region_tree.delete(item)
        
        regions = self.config_manager.get_scan_regions(self.current_module)
        for region in regions:
            self.region_tree.insert('', 'end', values=(
                region['name'],
                region['x'],
                region['y'],
                region['width'],
                region['height']
            ))
    
    def _load_actions(self):
        """Load actions into tree view"""
        for item in self.actions_tree.get_children():
            self.actions_tree.delete(item)
        
        actions = self.config_manager.get_actions(self.current_module)
        for action in actions:
            params = ', '.join([f"{k}={v}" for k, v in action.items() if k not in ['type', 'target']])
            self.actions_tree.insert('', 'end', values=(
                action.get('type', ''),
                action.get('target', ''),
                params
            ))

    def _load_module_settings(self):
        """Load module settings into the settings tab"""
        if not self.current_module:
            return

        config = self.config_manager.get_module_config(self.current_module)
        if not config:
            return

        # Load module settings
        self.enabled_var.set(config.get('enabled', True))
        self.priority_var.set(str(config.get('priority', 2)))
        self.cooldown_var.set(str(config.get('cooldown', 30)))

        # Load detection settings from global config
        import configparser
        global_config = configparser.ConfigParser()
        global_config.read('config.ini')

        scan_interval = global_config.getfloat('GENERAL', 'scan_interval', fallback=2.0)
        self.scan_interval_var.set(str(scan_interval))

        # Load default threshold from templates
        default_threshold = 0.8
        templates = config.get('templates', [])
        if templates:
            default_threshold = templates[0].get('threshold', 0.8)
        self.threshold_var.set(str(default_threshold))

        # Load advanced settings from actions
        default_max_attempts = 5
        default_wait_time = 1.0
        actions = config.get('actions', [])
        if actions:
            default_max_attempts = actions[0].get('max_attempts', 5)
            default_wait_time = actions[0].get('wait', 1.0)

        self.max_attempts_var.set(str(default_max_attempts))
        self.wait_time_var.set(str(default_wait_time))

    # Template methods
    def _add_template(self):
        """Add a new template"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        name = simpledialog.askstring("Template Name", "Enter template name:")
        if not name:
            return

        threshold = simpledialog.askfloat("Threshold", "Enter matching threshold (0.0-1.0):",
                                        initialvalue=0.8, minvalue=0.0, maxvalue=1.0)
        if threshold is None:
            return

        required = messagebox.askyesno("Required", "Is this template required?")

        self.config_manager.add_template(self.current_module, name, threshold, required)
        self._load_templates()

    def _edit_template(self):
        """Edit selected template"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        selection = self.templates_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to edit")
            return

        # Get current template data
        item = self.templates_tree.item(selection[0])
        current_name = item['values'][0]
        current_threshold = float(item['values'][1])
        current_required = item['values'][2] == 'True'

        # Create edit dialog
        edit_window = tk.Toplevel(self.root)
        edit_window.title("Edit Template")
        edit_window.geometry("400x300")
        edit_window.transient(self.root)
        edit_window.grab_set()

        # Template name
        ttk.Label(edit_window, text="Template Name:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        name_var = tk.StringVar(value=current_name)
        ttk.Entry(edit_window, textvariable=name_var, width=30).grid(row=0, column=1, padx=10, pady=5)

        # Threshold
        ttk.Label(edit_window, text="Threshold (0.0-1.0):").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        threshold_var = tk.StringVar(value=str(current_threshold))
        ttk.Spinbox(edit_window, from_=0.0, to=1.0, increment=0.05, textvariable=threshold_var, width=28).grid(row=1, column=1, padx=10, pady=5)

        # Required
        ttk.Label(edit_window, text="Required:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        required_var = tk.BooleanVar(value=current_required)
        ttk.Checkbutton(edit_window, variable=required_var).grid(row=2, column=1, sticky=tk.W, padx=10, pady=5)

        # Buttons
        button_frame = ttk.Frame(edit_window)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)

        def save_changes():
            try:
                new_name = name_var.get().strip()
                new_threshold = float(threshold_var.get())
                new_required = required_var.get()

                if not new_name:
                    messagebox.showerror("Error", "Template name cannot be empty")
                    return

                if not (0.0 <= new_threshold <= 1.0):
                    messagebox.showerror("Error", "Threshold must be between 0.0 and 1.0")
                    return

                # Update template
                config = self.config_manager.get_module_config(self.current_module)
                if config and 'templates' in config:
                    for template in config['templates']:
                        if template['name'] == current_name:
                            template['name'] = new_name
                            template['threshold'] = new_threshold
                            template['required'] = new_required
                            break

                    self.config_manager.update_module_config(self.current_module, config)
                    self._debounced_save()
                    self._load_templates()
                    edit_window.destroy()
                    messagebox.showinfo("Success", "Template updated successfully!")

            except ValueError:
                messagebox.showerror("Error", "Invalid threshold value")

        def recapture_template():
            """Recapture the template image"""
            try:
                edit_window.withdraw()  # Hide edit window temporarily
                self.root.iconify()
                time.sleep(0.5)

                # Capture new template region using simple method
                region_coords = self._simple_region_capture()
                self.root.deiconify()

                if region_coords:
                    x, y, width, height = region_coords

                    # Take screenshot of the region
                    screenshot = pyautogui.screenshot(region=(x, y, width, height))

                    # Save the new template image
                    template_path = os.path.join("templates", f"{current_name}.png")
                    screenshot.save(template_path)

                    # Show success message
                    edit_window.deiconify()  # Show edit window again
                    messagebox.showinfo("Success", f"Template '{current_name}' image updated successfully!")

                else:
                    edit_window.deiconify()  # Show edit window again
                    messagebox.showinfo("Cancelled", "Template capture cancelled")

            except Exception as e:
                edit_window.deiconify()  # Make sure edit window is visible
                messagebox.showerror("Error", f"Failed to recapture template: {str(e)}")

        ttk.Button(button_frame, text="Save", command=save_changes).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="Recapture Template", command=recapture_template).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="Cancel", command=edit_window.destroy).grid(row=0, column=2, padx=5)

    def _capture_template(self):
        """Capture a template from screen"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        template_name = simpledialog.askstring("Template Name", "Enter name for template to capture:")
        if not template_name:
            return

        messagebox.showinfo("Capture Template",
                           "1. Position mouse at TOP-LEFT corner, press SPACEBAR\n"
                           "2. Position mouse at BOTTOM-RIGHT corner, press SPACEBAR\n"
                           "Press ESC to cancel")

        self.root.iconify()
        time.sleep(0.5)

        try:
            # Capture region using simple method
            region_coords = self._simple_region_capture()

            if region_coords:
                x, y, width, height = region_coords

                # Take screenshot of the selected region
                screenshot = pyautogui.screenshot(region=(x, y, width, height))

                # Save template
                template_path = os.path.join('templates', f"{template_name}.png")
                os.makedirs('templates', exist_ok=True)
                screenshot.save(template_path)

                # Get configuration
                threshold = simpledialog.askfloat("Threshold", "Enter matching threshold (0.0-1.0):",
                                                initialvalue=0.8, minvalue=0.0, maxvalue=1.0)
                required = messagebox.askyesno("Required", "Is this template required for module execution?")

                # Add to configuration
                self.config_manager.add_template(self.current_module, template_name, threshold or 0.8, required)
                self._load_templates()

                messagebox.showinfo("Success", f"Template '{template_name}' captured and saved!")
            else:
                messagebox.showinfo("Cancelled", "Template capture cancelled")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to capture template: {str(e)}")
        finally:
            self.root.deiconify()

    def _delete_template(self):
        """Delete selected template"""
        selection = self.templates_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to delete")
            return

        item = self.templates_tree.item(selection[0])
        template_name = item['values'][0]

        # Delete without confirmation popup
        module_config = self.config_manager.get_module_config(self.current_module)
        templates = module_config.get('templates', [])
        templates = [t for t in templates if t['name'] != template_name]
        module_config['templates'] = templates
        self.config_manager.update_module_config(self.current_module, module_config)
        self.config_manager.save_config()  # Save to file

        # Delete template file
        template_path = os.path.join('templates', f"{template_name}.png")
        if os.path.exists(template_path):
            os.remove(template_path)

        self._load_templates()
        print(f"🗑️ Deleted template: {template_name}")
        messagebox.showinfo("Success", f"Deleted template: {template_name}")

    def _test_template(self):
        """Test selected template"""
        selection = self.templates_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to test")
            return

        item = self.templates_tree.item(selection[0])
        template_name = item['values'][0]

        # Take screenshot and test template matching
        try:
            screenshot = pyautogui.screenshot()
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            template_path = os.path.join('templates', f"{template_name}.png")
            if not os.path.exists(template_path):
                messagebox.showerror("Error", f"Template file not found: {template_path}")
                return

            template = cv2.imread(template_path)
            result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            messagebox.showinfo("Template Test",
                              f"Template: {template_name}\n"
                              f"Best match confidence: {max_val:.3f}\n"
                              f"Location: {max_loc}")
        except Exception as e:
            messagebox.showerror("Error", f"Template test failed: {str(e)}")

    # Text pattern methods
    def _add_text_pattern(self):
        """Add a new text pattern"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        text = simpledialog.askstring("Text Pattern", "Enter text to search for:")
        if not text:
            return

        regions = [r['name'] for r in self.config_manager.get_scan_regions(self.current_module)]
        if not regions:
            regions = ['full_screen']

        # Simple region selection - in real implementation you'd use a dropdown
        region = simpledialog.askstring("Region", f"Enter region name (available: {', '.join(regions)}):",
                                       initialvalue=regions[0])
        if not region:
            return

        required = messagebox.askyesno("Required", "Is this text pattern required?")

        self.config_manager.add_text_pattern(self.current_module, text, region, required)
        self._load_text_patterns()

    def _edit_text_pattern(self):
        """Edit selected text pattern"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        selection = self.text_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a text pattern to edit")
            return

        # Get current text pattern data
        item = self.text_tree.item(selection[0])
        current_text = item['values'][0]
        current_region = item['values'][1]
        current_required = item['values'][2] == 'True'

        # Create edit dialog
        edit_window = tk.Toplevel(self.root)
        edit_window.title("Edit Text Pattern")
        edit_window.geometry("500x400")
        edit_window.transient(self.root)
        edit_window.grab_set()

        # Text pattern
        ttk.Label(edit_window, text="Text Pattern:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        text_var = tk.StringVar(value=current_text)
        ttk.Entry(edit_window, textvariable=text_var, width=40).grid(row=0, column=1, padx=10, pady=5)

        # Region selection
        ttk.Label(edit_window, text="Region:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)

        # Get available regions
        regions = [r['name'] for r in self.config_manager.get_scan_regions(self.current_module)]
        if not regions:
            regions = ['full_screen']

        region_var = tk.StringVar(value=current_region)
        region_combo = ttk.Combobox(edit_window, textvariable=region_var, values=regions, width=37)
        region_combo.grid(row=1, column=1, padx=10, pady=5)

        # Required checkbox
        ttk.Label(edit_window, text="Required:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        required_var = tk.BooleanVar(value=current_required)
        ttk.Checkbutton(edit_window, variable=required_var).grid(row=2, column=1, sticky=tk.W, padx=10, pady=5)

        # Region preview/edit section
        region_frame = ttk.LabelFrame(edit_window, text="Region Configuration", padding="10")
        region_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=10, pady=10)

        ttk.Label(region_frame, text="Selected region for text detection:").grid(row=0, column=0, columnspan=2, sticky=tk.W)

        region_info_var = tk.StringVar()
        region_info_label = ttk.Label(region_frame, textvariable=region_info_var, foreground="blue")
        region_info_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))

        def update_region_info():
            """Update region information display"""
            selected_region = region_var.get()
            region_data = self.config_manager.get_region_by_name(self.current_module, selected_region)
            if region_data:
                x, y, w, h = region_data
                region_info_var.set(f"Region: ({x}, {y}) {w}x{h}")
            else:
                region_info_var.set("Region not found or not configured")

        # Update region info when selection changes
        region_combo.bind('<<ComboboxSelected>>', lambda e: update_region_info())
        update_region_info()  # Initial update

        def edit_selected_region():
            """Edit the selected region - simple recapture"""
            selected_region = region_var.get()
            if selected_region and selected_region != 'full_screen':
                try:
                    edit_window.withdraw()  # Hide edit window temporarily
                    self.root.iconify()
                    time.sleep(0.5)

                    # Capture new region using simple method
                    region_coords = self._simple_region_capture()
                    self.root.deiconify()

                    if region_coords:
                        new_x, new_y, new_width, new_height = region_coords

                        # Update the region in configuration
                        config = self.config_manager.get_module_config(self.current_module)
                        if config and 'scan_regions' in config:
                            for region in config['scan_regions']:
                                if region['name'] == selected_region:
                                    region['x'] = new_x
                                    region['y'] = new_y
                                    region['width'] = new_width
                                    region['height'] = new_height
                                    break

                            self.config_manager.update_module_config(self.current_module, config)
                            self._debounced_save()

                            # Update the region info display
                            update_region_info()

                            edit_window.deiconify()  # Show edit window again
                            messagebox.showinfo("Success", f"Region '{selected_region}' updated: ({new_x}, {new_y}) {new_width}x{new_height}")
                        else:
                            edit_window.deiconify()
                            messagebox.showerror("Error", "Failed to update region configuration")
                    else:
                        edit_window.deiconify()  # Show edit window again
                        messagebox.showinfo("Cancelled", "Region capture cancelled")

                except Exception as e:
                    edit_window.deiconify()  # Make sure edit window is visible
                    messagebox.showerror("Error", f"Failed to recapture region: {str(e)}")
            else:
                messagebox.showinfo("Info", "Cannot edit 'full_screen' region")

        ttk.Button(region_frame, text="Recapture Region",
                  command=edit_selected_region).grid(row=2, column=0, pady=(10, 0))
        ttk.Button(region_frame, text="Preview Region",
                  command=lambda: self._preview_specific_region(region_var.get())).grid(row=2, column=1, padx=(10, 0), pady=(10, 0))

        # Buttons
        button_frame = ttk.Frame(edit_window)
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)

        def save_text_pattern_changes():
            try:
                new_text = text_var.get().strip()
                new_region = region_var.get().strip()
                new_required = required_var.get()

                if not new_text:
                    messagebox.showerror("Error", "Text pattern cannot be empty")
                    return

                if not new_region:
                    messagebox.showerror("Error", "Region cannot be empty")
                    return

                # Update text pattern
                config = self.config_manager.get_module_config(self.current_module)
                if config and 'text_patterns' in config:
                    for pattern in config['text_patterns']:
                        if (pattern['text'] == current_text and
                            pattern['region'] == current_region and
                            pattern['required'] == current_required):
                            pattern['text'] = new_text
                            pattern['region'] = new_region
                            pattern['required'] = new_required
                            break

                    self.config_manager.update_module_config(self.current_module, config)
                    self._debounced_save()
                    self._load_text_patterns()
                    edit_window.destroy()
                    messagebox.showinfo("Success", "Text pattern updated successfully!")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to update text pattern: {str(e)}")

        ttk.Button(button_frame, text="Save", command=save_text_pattern_changes).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="Cancel", command=edit_window.destroy).grid(row=0, column=1, padx=5)

    def _delete_text_pattern(self):
        """Delete selected text pattern"""
        selection = self.text_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a text pattern to delete")
            return

        item = self.text_tree.item(selection[0])
        text = item['values'][0]

        if messagebox.askyesno("Confirm", f"Delete text pattern '{text}'?"):
            module_config = self.config_manager.get_module_config(self.current_module)
            patterns = module_config.get('text_patterns', [])
            patterns = [p for p in patterns if p['text'] != text]
            module_config['text_patterns'] = patterns
            self.config_manager.update_module_config(self.current_module, module_config)
            self._load_text_patterns()

    def _test_ocr(self):
        """Test OCR on current screen"""
        try:
            import pytesseract
            screenshot = pyautogui.screenshot()
            text = pytesseract.image_to_string(screenshot)

            # Show OCR result in a new window
            ocr_window = tk.Toplevel(self.root)
            ocr_window.title("OCR Result")
            ocr_window.geometry("600x400")

            text_widget = tk.Text(ocr_window, wrap=tk.WORD)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert(tk.END, text)

        except Exception as e:
            messagebox.showerror("Error", f"OCR test failed: {str(e)}")

    # Coordinate methods
    def _add_coordinate(self):
        """Add a new click coordinate"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        name = simpledialog.askstring("Coordinate Name", "Enter coordinate name:")
        if not name:
            return

        x = simpledialog.askinteger("X Coordinate", "Enter X coordinate:", minvalue=0)
        if x is None:
            return

        y = simpledialog.askinteger("Y Coordinate", "Enter Y coordinate:", minvalue=0)
        if y is None:
            return

        delay = simpledialog.askfloat("Delay", "Enter delay in seconds:", initialvalue=2.0, minvalue=0.01, maxvalue=10.0)
        if delay is None:
            delay = 2.0

        repeat = simpledialog.askinteger("Repeat", "Enter repeat count:", initialvalue=1, minvalue=1, maxvalue=20)
        if repeat is None:
            repeat = 1

        description = simpledialog.askstring("Description", "Enter description (optional):") or ""

        # Ask if user wants to use ESC key instead of clicking
        use_esc = messagebox.askyesno("ESC Key Option",
                                     f"Use ESC key instead of clicking at ({x}, {y})?\n\n"
                                     "Choose 'Yes' to press ESC key (good for closing menus)\n"
                                     "Choose 'No' to perform normal click")

        # Add delay and repeat to coordinate data
        config = self.config_manager.get_module_config(self.current_module)
        if not config:
            config = {"click_coordinates": []}
        if "click_coordinates" not in config:
            config["click_coordinates"] = []

        config["click_coordinates"].append({
            "name": name,
            "x": x,
            "y": y,
            "delay": delay,
            "repeat": repeat,
            "description": description,
            "use_esc_key": use_esc
        })

        self.config_manager.update_module_config(self.current_module, config)
        self._load_coordinates()

    def _edit_coordinate(self):
        """Edit selected coordinate"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        selection = self.coord_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a coordinate to edit")
            return

        # Get current coordinate data
        # Columns are: ('Enabled', 'Name', 'X', 'Y', 'Delay', 'Repeat', 'ESC', 'Description')
        item = self.coord_tree.item(selection[0])
        current_name = item['values'][1]  # Name is at index 1
        current_x = int(item['values'][2])  # X is at index 2
        current_y = int(item['values'][3])  # Y is at index 3
        current_delay = float(item['values'][4]) if len(item['values']) > 4 else 2.0  # Delay is at index 4
        current_repeat = int(item['values'][5]) if len(item['values']) > 5 else 1  # Repeat is at index 5
        current_esc_text = item['values'][6] if len(item['values']) > 6 else "Click"  # ESC is at index 6
        current_use_esc = current_esc_text == "ESC"
        current_description = item['values'][7] if len(item['values']) > 7 else ""  # Description is at index 7

        # Create edit dialog
        edit_window = tk.Toplevel(self.root)
        edit_window.title("Edit Coordinate")
        edit_window.geometry("450x400")
        edit_window.transient(self.root)
        edit_window.grab_set()

        # Coordinate name
        ttk.Label(edit_window, text="Coordinate Name:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        name_var = tk.StringVar(value=current_name)
        ttk.Entry(edit_window, textvariable=name_var, width=30).grid(row=0, column=1, padx=10, pady=5)

        # X coordinate
        ttk.Label(edit_window, text="X Coordinate:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        x_var = tk.StringVar(value=str(current_x))
        ttk.Spinbox(edit_window, from_=0, to=9999, textvariable=x_var, width=28).grid(row=1, column=1, padx=10, pady=5)

        # Y coordinate
        ttk.Label(edit_window, text="Y Coordinate:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        y_var = tk.StringVar(value=str(current_y))
        ttk.Spinbox(edit_window, from_=0, to=9999, textvariable=y_var, width=28).grid(row=2, column=1, padx=10, pady=5)

        # Delay
        ttk.Label(edit_window, text="Delay (seconds):").grid(row=3, column=0, sticky=tk.W, padx=10, pady=5)
        delay_var = tk.StringVar(value=str(current_delay))
        ttk.Spinbox(edit_window, from_=0.01, to=10.0, increment=0.01, textvariable=delay_var, width=28).grid(row=3, column=1, padx=10, pady=5)

        # Repeat
        ttk.Label(edit_window, text="Repeat count:").grid(row=4, column=0, sticky=tk.W, padx=10, pady=5)
        repeat_var = tk.StringVar(value=str(current_repeat))
        ttk.Spinbox(edit_window, from_=1, to=20, increment=1, textvariable=repeat_var, width=28).grid(row=4, column=1, padx=10, pady=5)

        # ESC Key Option
        ttk.Label(edit_window, text="Action Type:").grid(row=5, column=0, sticky=tk.W, padx=10, pady=5)
        esc_var = tk.BooleanVar(value=current_use_esc)
        esc_frame = ttk.Frame(edit_window)
        esc_frame.grid(row=5, column=1, sticky=tk.W, padx=10, pady=5)
        ttk.Checkbutton(esc_frame, text="Use ESC key instead of clicking", variable=esc_var).pack(anchor=tk.W)
        ttk.Label(esc_frame, text="(Good for closing menus/dialogs)", font=('TkDefaultFont', 8)).pack(anchor=tk.W)

        # Description
        ttk.Label(edit_window, text="Description:").grid(row=6, column=0, sticky=tk.W, padx=10, pady=5)
        desc_var = tk.StringVar(value=current_description)
        ttk.Entry(edit_window, textvariable=desc_var, width=30).grid(row=6, column=1, padx=10, pady=5)

        # Buttons
        button_frame = ttk.Frame(edit_window)
        button_frame.grid(row=7, column=0, columnspan=2, pady=20)

        def save_coordinate_changes():
            try:
                new_name = name_var.get().strip()
                new_x = int(x_var.get())
                new_y = int(y_var.get())
                new_delay = float(delay_var.get())
                new_repeat = int(repeat_var.get())
                new_use_esc = esc_var.get()
                new_description = desc_var.get().strip()

                if not new_name:
                    messagebox.showerror("Error", "Coordinate name cannot be empty")
                    return

                # Update coordinate
                config = self.config_manager.get_module_config(self.current_module)
                if config and 'click_coordinates' in config:
                    for coord in config['click_coordinates']:
                        if coord['name'] == current_name:
                            coord['name'] = new_name
                            coord['x'] = new_x
                            coord['y'] = new_y
                            coord['delay'] = new_delay
                            coord['repeat'] = new_repeat
                            coord['use_esc_key'] = new_use_esc
                            coord['description'] = new_description
                            break

                    self.config_manager.update_module_config(self.current_module, config)
                    self._debounced_save()
                    self._load_coordinates()
                    edit_window.destroy()
                    messagebox.showinfo("Success", "Coordinate updated successfully!")

            except ValueError:
                messagebox.showerror("Error", "Invalid coordinate values")

        def recapture_coordinate():
            """Recapture the coordinate position"""
            try:
                edit_window.withdraw()  # Hide edit window temporarily
                self.root.iconify()
                time.sleep(0.5)

                # Capture new coordinate using simple method
                new_position = self._simple_coordinate_capture()
                self.root.deiconify()

                if new_position:
                    new_x, new_y = new_position
                    x_var.set(str(new_x))
                    y_var.set(str(new_y))

                    edit_window.deiconify()  # Show edit window again
                    messagebox.showinfo("Success", f"Coordinate captured at ({new_x}, {new_y})!")
                else:
                    edit_window.deiconify()  # Show edit window again
                    messagebox.showinfo("Cancelled", "Coordinate capture cancelled")

            except Exception as e:
                edit_window.deiconify()  # Make sure edit window is visible
                messagebox.showerror("Error", f"Failed to recapture coordinate: {str(e)}")

        ttk.Button(button_frame, text="Save", command=save_coordinate_changes).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="Recapture Position", command=recapture_coordinate).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="Cancel", command=edit_window.destroy).grid(row=0, column=2, padx=5)

    def _capture_coordinate(self):
        """Capture a coordinate by pressing spacebar"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        name = simpledialog.askstring("Coordinate Name", "Enter name for this coordinate:")
        if not name:
            return

        # Simple message
        messagebox.showinfo("Capture Coordinate",
                           "Position your mouse where you want to capture, then press SPACEBAR.\n"
                           "Press ESC to cancel.")

        self.root.iconify()  # Minimize window
        time.sleep(0.5)

        try:
            # Simple spacebar capture
            click_pos = self._simple_spacebar_capture()

            if click_pos:
                x, y = click_pos

                delay = simpledialog.askfloat("Delay", f"Enter delay in seconds for coordinate ({x}, {y}):",
                                            initialvalue=2.0, minvalue=0.01, maxvalue=10.0)
                if delay is None:
                    delay = 2.0

                repeat = simpledialog.askinteger("Repeat", f"Enter repeat count for coordinate ({x}, {y}):",
                                               initialvalue=1, minvalue=1, maxvalue=20)
                if repeat is None:
                    repeat = 1

                description = simpledialog.askstring("Description",
                                                   f"Enter description for coordinate ({x}, {y}) (optional):") or ""

                # Add coordinate with delay and repeat
                config = self.config_manager.get_module_config(self.current_module)
                if not config:
                    config = {"click_coordinates": []}
                if "click_coordinates" not in config:
                    config["click_coordinates"] = []

                config["click_coordinates"].append({
                    "name": name,
                    "x": x,
                    "y": y,
                    "delay": delay,
                    "repeat": repeat,
                    "description": description
                })

                self.config_manager.update_module_config(self.current_module, config)
                self._load_coordinates()

                messagebox.showinfo("Success", f"Coordinate '{name}' captured at ({x}, {y}) with {delay}s delay and {repeat}× repeat!")
            else:
                messagebox.showinfo("Cancelled", "Coordinate capture cancelled")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to capture coordinate: {str(e)}")
        finally:
            self.root.deiconify()

    def _delete_coordinate(self):
        """Delete selected coordinate"""
        selection = self.coord_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a coordinate to delete")
            return

        item = self.coord_tree.item(selection[0])
        coord_name = item['values'][0]

        # Delete without confirmation popup
        module_config = self.config_manager.get_module_config(self.current_module)
        coordinates = module_config.get('click_coordinates', [])
        coordinates = [c for c in coordinates if c['name'] != coord_name]
        module_config['click_coordinates'] = coordinates
        self.config_manager.update_module_config(self.current_module, module_config)
        self.config_manager.save_config()  # Save to file
        self._load_coordinates()
        print(f"🗑️ Deleted coordinate: {coord_name}")
        messagebox.showinfo("Success", f"Deleted coordinate: {coord_name}")

    def _test_click(self):
        """Test selected coordinate by clicking it (no popup)"""
        selection = self.coord_tree.selection()
        if not selection:
            return

        item = self.coord_tree.item(selection[0])
        values = item['values']

        # Handle both old and new format
        if len(values) >= 6:  # New format with enabled column
            coord_name = values[1]
            x, y = int(values[2]), int(values[3])
            repeat = int(values[5]) if len(values) > 5 else 1
        else:  # Old format
            coord_name = values[0]
            x, y = int(values[1]), int(values[2])
            repeat = 1

        try:
            # Disable PyAutoGUI failsafe for testing
            original_pause = pyautogui.PAUSE
            pyautogui.PAUSE = 0

            print(f"🧪 Testing {coord_name}: clicking at ({x}, {y}) {repeat} times")

            # Perform clicks with repeat
            for i in range(repeat):
                pyautogui.click(x, y)
                if repeat > 1 and i < repeat - 1:
                    time.sleep(0.1)  # Small delay between repeats

            # Restore original pause
            pyautogui.PAUSE = original_pause

            print(f"✅ Test click completed for {coord_name}")

        except Exception as e:
            print(f"❌ Test click failed for {coord_name}: {str(e)}")
            pyautogui.PAUSE = original_pause

    # Region methods
    def _add_region(self):
        """Add a new scan region"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        name = simpledialog.askstring("Region Name", "Enter region name:")
        if not name:
            return

        x = simpledialog.askinteger("X Position", "Enter X position:", minvalue=0)
        if x is None:
            return

        y = simpledialog.askinteger("Y Position", "Enter Y position:", minvalue=0)
        if y is None:
            return

        width = simpledialog.askinteger("Width", "Enter width:", minvalue=1)
        if width is None:
            return

        height = simpledialog.askinteger("Height", "Enter height:", minvalue=1)
        if height is None:
            return

        self.config_manager.add_scan_region(self.current_module, name, x, y, width, height)
        self._load_regions()

    def _edit_region(self):
        """Edit selected region"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        selection = self.region_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a region to edit")
            return

        # Get current region data
        item = self.region_tree.item(selection[0])
        current_name = item['values'][0]
        current_x = int(item['values'][1])
        current_y = int(item['values'][2])
        current_width = int(item['values'][3])
        current_height = int(item['values'][4])

        # Create edit dialog
        edit_window = tk.Toplevel(self.root)
        edit_window.title("Edit Region")
        edit_window.geometry("450x400")
        edit_window.transient(self.root)
        edit_window.grab_set()

        # Region name
        ttk.Label(edit_window, text="Region Name:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        name_var = tk.StringVar(value=current_name)
        ttk.Entry(edit_window, textvariable=name_var, width=30).grid(row=0, column=1, padx=10, pady=5)

        # X position
        ttk.Label(edit_window, text="X Position:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        x_var = tk.StringVar(value=str(current_x))
        ttk.Spinbox(edit_window, from_=0, to=9999, textvariable=x_var, width=28).grid(row=1, column=1, padx=10, pady=5)

        # Y position
        ttk.Label(edit_window, text="Y Position:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        y_var = tk.StringVar(value=str(current_y))
        ttk.Spinbox(edit_window, from_=0, to=9999, textvariable=y_var, width=28).grid(row=2, column=1, padx=10, pady=5)

        # Width
        ttk.Label(edit_window, text="Width:").grid(row=3, column=0, sticky=tk.W, padx=10, pady=5)
        width_var = tk.StringVar(value=str(current_width))
        ttk.Spinbox(edit_window, from_=1, to=9999, textvariable=width_var, width=28).grid(row=3, column=1, padx=10, pady=5)

        # Height
        ttk.Label(edit_window, text="Height:").grid(row=4, column=0, sticky=tk.W, padx=10, pady=5)
        height_var = tk.StringVar(value=str(current_height))
        ttk.Spinbox(edit_window, from_=1, to=9999, textvariable=height_var, width=28).grid(row=4, column=1, padx=10, pady=5)

        # Buttons
        button_frame = ttk.Frame(edit_window)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)

        def save_region_changes():
            try:
                new_name = name_var.get().strip()
                new_x = int(x_var.get())
                new_y = int(y_var.get())
                new_width = int(width_var.get())
                new_height = int(height_var.get())

                if not new_name:
                    messagebox.showerror("Error", "Region name cannot be empty")
                    return

                if new_width <= 0 or new_height <= 0:
                    messagebox.showerror("Error", "Width and height must be positive")
                    return

                # Update region
                config = self.config_manager.get_module_config(self.current_module)
                if config and 'scan_regions' in config:
                    for region in config['scan_regions']:
                        if region['name'] == current_name:
                            region['name'] = new_name
                            region['x'] = new_x
                            region['y'] = new_y
                            region['width'] = new_width
                            region['height'] = new_height
                            break

                    self.config_manager.update_module_config(self.current_module, config)
                    self._debounced_save()
                    self._load_regions()
                    edit_window.destroy()
                    messagebox.showinfo("Success", "Region updated successfully!")

            except ValueError:
                messagebox.showerror("Error", "Invalid region values")

        def recapture_region():
            """Recapture the region area"""
            try:
                edit_window.withdraw()  # Hide edit window temporarily
                self.root.iconify()
                time.sleep(0.5)

                # Capture new region using simple method
                region_coords = self._simple_region_capture()
                self.root.deiconify()

                if region_coords:
                    new_x, new_y, new_width, new_height = region_coords
                    x_var.set(str(new_x))
                    y_var.set(str(new_y))
                    width_var.set(str(new_width))
                    height_var.set(str(new_height))

                    edit_window.deiconify()  # Show edit window again
                    messagebox.showinfo("Success", f"Region captured: ({new_x}, {new_y}) {new_width}x{new_height}!")
                else:
                    edit_window.deiconify()  # Show edit window again
                    messagebox.showinfo("Cancelled", "Region capture cancelled")

            except Exception as e:
                edit_window.deiconify()  # Make sure edit window is visible
                messagebox.showerror("Error", f"Failed to recapture region: {str(e)}")

        ttk.Button(button_frame, text="Save", command=save_region_changes).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="Recapture Area", command=recapture_region).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="Cancel", command=edit_window.destroy).grid(row=0, column=2, padx=5)

    def _capture_region(self):
        """Capture a region by selecting two points with spacebar"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        name = simpledialog.askstring("Region Name", "Enter name for this region:")
        if not name:
            return

        messagebox.showinfo("Capture Region",
                           "1. Position mouse at TOP-LEFT corner, press SPACEBAR\n"
                           "2. Position mouse at BOTTOM-RIGHT corner, press SPACEBAR\n"
                           "Press ESC to cancel")

        self.root.iconify()
        time.sleep(0.5)

        try:
            # Capture two points with spacebar
            region_coords = self._simple_region_capture()

            if region_coords:
                x, y, width, height = region_coords

                self.config_manager.add_scan_region(self.current_module, name, x, y, width, height)
                self._load_regions()

                messagebox.showinfo("Success", f"Region '{name}' captured: ({x}, {y}) {width}x{height}")
            else:
                messagebox.showinfo("Cancelled", "Region capture cancelled")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to capture region: {str(e)}")
        finally:
            self.root.deiconify()

    def _delete_region(self):
        """Delete selected region"""
        selection = self.region_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a region to delete")
            return

        item = self.region_tree.item(selection[0])
        region_name = item['values'][0]

        if messagebox.askyesno("Confirm", f"Delete region '{region_name}'?"):
            module_config = self.config_manager.get_module_config(self.current_module)
            regions = module_config.get('scan_regions', [])
            regions = [r for r in regions if r['name'] != region_name]
            module_config['scan_regions'] = regions
            self.config_manager.update_module_config(self.current_module, module_config)
            self._load_regions()

    def _preview_region(self):
        """Preview selected region on screen"""
        selection = self.region_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a region to preview")
            return

        item = self.region_tree.item(selection[0])
        x, y, width, height = int(item['values'][1]), int(item['values'][2]), int(item['values'][3]), int(item['values'][4])

        try:
            # Take screenshot of the region
            screenshot = pyautogui.screenshot(region=(x, y, width, height))

            # Show in new window
            preview_window = tk.Toplevel(self.root)
            preview_window.title(f"Region Preview: {item['values'][0]}")

            # Convert PIL to PhotoImage
            photo = ImageTk.PhotoImage(screenshot)
            label = tk.Label(preview_window, image=photo)
            label.image = photo  # Keep a reference
            label.pack(padx=10, pady=10)

            info_label = tk.Label(preview_window, text=f"Region: {x}, {y}, {width}x{height}")
            info_label.pack(pady=(0, 10))

        except Exception as e:
            messagebox.showerror("Error", f"Preview failed: {str(e)}")

    def _preview_specific_region(self, region_name: str):
        """Preview a specific region by name"""
        if not self.current_module or not region_name:
            return

        try:
            # Get region data
            region_data = self.config_manager.get_region_by_name(self.current_module, region_name)
            if not region_data:
                messagebox.showwarning("Warning", f"Region '{region_name}' not found")
                return

            x, y, width, height = region_data

            # Take screenshot of the region
            screenshot = pyautogui.screenshot(region=(x, y, width, height))

            # Show in new window
            preview_window = tk.Toplevel(self.root)
            preview_window.title(f"Region Preview: {region_name}")

            # Convert PIL to PhotoImage
            photo = ImageTk.PhotoImage(screenshot)
            label = tk.Label(preview_window, image=photo)
            label.image = photo  # Keep a reference
            label.pack(padx=10, pady=10)

            info_label = tk.Label(preview_window, text=f"Region: ({x}, {y}) {width}x{height}")
            info_label.pack(pady=(0, 10))

        except Exception as e:
            messagebox.showerror("Error", f"Preview failed: {str(e)}")

    # Action methods
    def _add_action(self):
        """Add a new action"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        # Create action dialog
        self._show_action_dialog()

    def _edit_action(self):
        """Edit selected action"""
        selection = self.actions_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an action to edit")
            return

        # Get current action data
        item = self.actions_tree.item(selection[0])
        action_type = item['values'][0]
        target = item['values'][1]

        # Show edit dialog with current values
        self._show_action_dialog(action_type, target)

    def _delete_action(self):
        """Delete selected action"""
        selection = self.actions_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an action to delete")
            return

        if messagebox.askyesno("Confirm", "Delete selected action?"):
            # Get index of selected item
            index = self.actions_tree.index(selection[0])

            module_config = self.config_manager.get_module_config(self.current_module)
            actions = module_config.get('actions', [])
            if 0 <= index < len(actions):
                actions.pop(index)
                module_config['actions'] = actions
                self.config_manager.update_module_config(self.current_module, module_config)
                self._load_actions()

    def _move_action_up(self):
        """Move selected action up in the list"""
        selection = self.actions_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an action to move")
            return

        index = self.actions_tree.index(selection[0])
        if index > 0:
            module_config = self.config_manager.get_module_config(self.current_module)
            actions = module_config.get('actions', [])
            actions[index], actions[index-1] = actions[index-1], actions[index]
            module_config['actions'] = actions
            self.config_manager.update_module_config(self.current_module, module_config)
            self._load_actions()

    def _move_action_down(self):
        """Move selected action down in the list"""
        selection = self.actions_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an action to move")
            return

        index = self.actions_tree.index(selection[0])
        module_config = self.config_manager.get_module_config(self.current_module)
        actions = module_config.get('actions', [])

        if index < len(actions) - 1:
            actions[index], actions[index+1] = actions[index+1], actions[index]
            module_config['actions'] = actions
            self.config_manager.update_module_config(self.current_module, module_config)
            self._load_actions()

    def _show_action_dialog(self, current_type="", current_target=""):
        """Show dialog for adding/editing actions"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Action Configuration")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # Action type
        ttk.Label(dialog, text="Action Type:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        type_var = tk.StringVar(value=current_type)
        type_combo = ttk.Combobox(dialog, textvariable=type_var, values=[
            "navigate", "click", "wait", "scan", "template_match", "text_search",
            "claim_rewards", "complete_tasks", "gather_resource", "close"
        ])
        type_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=10, pady=5)

        # Target
        ttk.Label(dialog, text="Target:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        target_var = tk.StringVar(value=current_target)
        target_entry = ttk.Entry(dialog, textvariable=target_var)
        target_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=10, pady=5)

        # Parameters frame
        params_frame = ttk.LabelFrame(dialog, text="Parameters", padding="5")
        params_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=5)

        # Common parameters
        ttk.Label(params_frame, text="Wait Time:").grid(row=0, column=0, sticky=tk.W)
        wait_var = tk.StringVar()
        ttk.Entry(params_frame, textvariable=wait_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(5, 0))

        ttk.Label(params_frame, text="Max Attempts:").grid(row=1, column=0, sticky=tk.W)
        attempts_var = tk.StringVar()
        ttk.Entry(params_frame, textvariable=attempts_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(5, 0))

        ttk.Label(params_frame, text="Confirm:").grid(row=2, column=0, sticky=tk.W)
        confirm_var = tk.BooleanVar()
        ttk.Checkbutton(params_frame, variable=confirm_var).grid(row=2, column=1, sticky=tk.W, padx=(5, 0))

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.grid(row=3, column=0, columnspan=2, pady=10)

        def save_action():
            action = {"type": type_var.get(), "target": target_var.get()}

            if wait_var.get():
                action["wait"] = float(wait_var.get())
            if attempts_var.get():
                action["max_attempts"] = int(attempts_var.get())
            if confirm_var.get():
                action["confirm"] = True

            # Add to module config
            module_config = self.config_manager.get_module_config(self.current_module)
            if "actions" not in module_config:
                module_config["actions"] = []

            module_config["actions"].append(action)
            self.config_manager.update_module_config(self.current_module, module_config)
            self._load_actions()
            dialog.destroy()

        ttk.Button(button_frame, text="Save", command=save_action).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(button_frame, text="Cancel", command=dialog.destroy).grid(row=0, column=1)

        dialog.columnconfigure(1, weight=1)
        params_frame.columnconfigure(1, weight=1)

    # Helper methods for screen capture
    def _simple_spacebar_capture(self) -> Optional[Tuple[int, int]]:
        """Simple spacebar capture without any windows"""
        try:
            import keyboard

            # Wait for spacebar or escape
            while True:
                if keyboard.is_pressed('space'):
                    # Capture current mouse position
                    pos = pyautogui.position()
                    time.sleep(0.2)  # Debounce
                    return (pos.x, pos.y)
                elif keyboard.is_pressed('esc'):
                    return None
                time.sleep(0.05)  # Small delay to prevent high CPU usage

        except ImportError:
            # Fallback without keyboard library
            return self._simple_fallback_capture()
        except Exception as e:
            print(f"Error in spacebar capture: {str(e)}")
            return None

    def _simple_fallback_capture(self) -> Optional[Tuple[int, int]]:
        """Fallback capture method without keyboard library"""
        # Create minimal dialog
        dialog = tk.Toplevel()
        dialog.title("Capture")
        dialog.geometry("200x100")
        dialog.attributes('-topmost', True)

        result = None

        def capture():
            nonlocal result
            result = pyautogui.position()
            dialog.destroy()

        def cancel():
            dialog.destroy()

        ttk.Button(dialog, text="Capture Now", command=capture).pack(pady=10)
        ttk.Button(dialog, text="Cancel", command=cancel).pack(pady=5)

        dialog.eval('tk::PlaceWindow . center')
        dialog.grab_set()
        dialog.wait_window()

        return result

    def _simple_coordinate_capture(self) -> Optional[Tuple[int, int]]:
        """Simple coordinate capture with one spacebar press"""
        import time  # Ensure time is available in this scope

        try:
            import keyboard

            # Use the safer polling method instead of hooks
            print("Position mouse and press SPACEBAR to capture coordinate...")
            print("Press ESC to cancel")

            # Wait for any currently pressed keys to be released
            time.sleep(0.5)
            while keyboard.is_pressed('space') or keyboard.is_pressed('esc'):
                time.sleep(0.1)

            # Additional delay to ensure clean state
            time.sleep(0.2)

            while True:
                if keyboard.is_pressed('space'):
                    pos = pyautogui.position()
                    print(f"Coordinate captured: {pos}")

                    # Wait for key release to prevent multiple captures
                    while keyboard.is_pressed('space'):
                        time.sleep(0.05)

                    return (pos.x, pos.y)
                elif keyboard.is_pressed('esc'):
                    print("Coordinate capture cancelled")

                    # Wait for key release
                    while keyboard.is_pressed('esc'):
                        time.sleep(0.05)

                    return None
                time.sleep(0.05)  # Small delay to prevent high CPU usage

        except ImportError:
            # Fallback: ask user to click
            try:
                print("Keyboard library not available, using fallback method...")
                time.sleep(2)  # Give user more time to position mouse
                pos = pyautogui.position()
                return (pos.x, pos.y)
            except Exception:
                return None

        except Exception as e:
            print(f"Error in coordinate capture: {str(e)}")
            return None

    def _simple_region_capture(self) -> Optional[Tuple[int, int, int, int]]:
        """Simple region capture with two spacebar presses"""
        import time  # Ensure time is available in this scope

        try:
            import keyboard

            points = []

            # Wait for any currently pressed keys to be released
            time.sleep(0.5)
            while keyboard.is_pressed('space') or keyboard.is_pressed('esc'):
                time.sleep(0.1)

            # Additional delay to ensure clean state
            time.sleep(0.2)

            # Capture first point
            print("Position mouse at TOP-LEFT corner and press SPACEBAR...")
            while True:
                if keyboard.is_pressed('space'):
                    pos = pyautogui.position()
                    points.append((pos.x, pos.y))
                    print(f"First point captured: {pos}")

                    # Wait for key release to prevent multiple captures
                    while keyboard.is_pressed('space'):
                        time.sleep(0.05)

                    break
                elif keyboard.is_pressed('esc'):
                    return None
                time.sleep(0.05)

            # Capture second point
            print("Position mouse at BOTTOM-RIGHT corner and press SPACEBAR...")
            while True:
                if keyboard.is_pressed('space'):
                    pos = pyautogui.position()
                    points.append((pos.x, pos.y))
                    print(f"Second point captured: {pos}")

                    # Wait for key release to prevent multiple captures
                    while keyboard.is_pressed('space'):
                        time.sleep(0.05)

                    break
                elif keyboard.is_pressed('esc'):
                    return None
                time.sleep(0.05)

            # Calculate region
            x1, y1 = points[0]
            x2, y2 = points[1]

            x = min(x1, x2)
            y = min(y1, y2)
            width = abs(x2 - x1)
            height = abs(y2 - y1)

            return (x, y, width, height)

        except ImportError:
            # Fallback without keyboard library
            return self._simple_region_fallback()
        except Exception as e:
            print(f"Error in region capture: {str(e)}")
            return None

    def _simple_region_fallback(self) -> Optional[Tuple[int, int, int, int]]:
        """Fallback region capture without keyboard library"""
        points = []

        # First point
        dialog1 = tk.Toplevel()
        dialog1.title("Point 1")
        dialog1.geometry("250x100")
        dialog1.attributes('-topmost', True)

        result1 = None

        def capture1():
            nonlocal result1
            result1 = pyautogui.position()
            dialog1.destroy()

        ttk.Label(dialog1, text="Position at TOP-LEFT corner").pack(pady=10)
        ttk.Button(dialog1, text="Capture Point 1", command=capture1).pack(pady=5)

        dialog1.eval('tk::PlaceWindow . center')
        dialog1.grab_set()
        dialog1.wait_window()

        if not result1:
            return None

        points.append(result1)

        # Second point
        dialog2 = tk.Toplevel()
        dialog2.title("Point 2")
        dialog2.geometry("250x100")
        dialog2.attributes('-topmost', True)

        result2 = None

        def capture2():
            nonlocal result2
            result2 = pyautogui.position()
            dialog2.destroy()

        ttk.Label(dialog2, text="Position at BOTTOM-RIGHT corner").pack(pady=10)
        ttk.Button(dialog2, text="Capture Point 2", command=capture2).pack(pady=5)

        dialog2.eval('tk::PlaceWindow . center')
        dialog2.grab_set()
        dialog2.wait_window()

        if not result2:
            return None

        points.append(result2)

        # Calculate region
        x1, y1 = points[0]
        x2, y2 = points[1]

        x = min(x1, x2)
        y = min(y1, y2)
        width = abs(x2 - x1)
        height = abs(y2 - y1)

        return (x, y, width, height)

    def _capture_mouse_click(self) -> Optional[Tuple[int, int]]:
        """Capture a single mouse click position"""
        try:
            # Try to use mouse/keyboard libraries if available
            import mouse
            import keyboard
            return self._capture_click_with_libraries()
        except ImportError:
            # Fallback to simple method
            return self._capture_click_simple()

    def _capture_click_with_libraries(self) -> Optional[Tuple[int, int]]:
        """Capture click using mouse/keyboard libraries"""
        import mouse
        import keyboard

        click_position = None

        def on_click():
            nonlocal click_position
            click_position = mouse.get_position()

        try:
            # Show instruction overlay
            instruction_window = self._create_instruction_overlay("Click on the desired location\nPress ESC to cancel")

            # Wait for click
            mouse.wait(button='left', target_types=('down',))
            click_position = mouse.get_position()

            instruction_window.destroy()
            return click_position

        except Exception as e:
            print(f"Error capturing click with libraries: {str(e)}")
            return None

    def _capture_click_simple(self) -> Optional[Tuple[int, int]]:
        """Simple click capture using keyboard trigger"""
        # Create countdown dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Capture Click Position")
        dialog.geometry("400x200")
        dialog.attributes('-topmost', True)
        dialog.attributes('-alpha', 0.9)
        dialog.configure(bg='darkblue')

        # Center dialog
        dialog.eval('tk::PlaceWindow . center')

        # Create labels
        title_label = tk.Label(dialog, text="Click Position Capture",
                              fg='white', bg='darkblue',
                              font=('Arial', 14, 'bold'))
        title_label.pack(pady=10)

        instruction_label = tk.Label(dialog,
                                   text="Position your mouse at the target location\nThen press SPACEBAR to capture",
                                   fg='white', bg='darkblue',
                                   font=('Arial', 11),
                                   justify=tk.CENTER)
        instruction_label.pack(pady=10)

        countdown_label = tk.Label(dialog, text="",
                                 fg='yellow', bg='darkblue',
                                 font=('Arial', 16, 'bold'))
        countdown_label.pack(pady=10)

        position_label = tk.Label(dialog, text="Current position: (0, 0)",
                                fg='lightgreen', bg='darkblue',
                                font=('Arial', 10))
        position_label.pack(pady=5)

        # Control buttons
        button_frame = tk.Frame(dialog, bg='darkblue')
        button_frame.pack(pady=10)

        result = None
        capture_enabled = True

        def update_position():
            """Update current mouse position display"""
            if dialog.winfo_exists():
                try:
                    pos = pyautogui.position()
                    position_label.config(text=f"Current position: ({pos.x}, {pos.y})")
                    dialog.after(100, update_position)  # Update every 100ms
                except:
                    pass

        def capture_position():
            nonlocal result, capture_enabled
            if capture_enabled:
                result = pyautogui.position()
                countdown_label.config(text="✓ CAPTURED!", fg='lightgreen')
                instruction_label.config(text=f"Position captured: ({result.x}, {result.y})")
                capture_enabled = False
                dialog.after(1500, dialog.destroy)  # Auto-close after 1.5 seconds

        def cancel_capture():
            nonlocal result
            result = None
            dialog.destroy()

        def on_key_press(event):
            """Handle keyboard input"""
            if event.keysym == 'space':
                capture_position()
            elif event.keysym == 'Escape':
                cancel_capture()

        # Bind keyboard events
        dialog.bind('<KeyPress>', on_key_press)
        dialog.focus_set()

        # Create buttons
        ttk.Button(button_frame, text="Capture (SPACE)",
                  command=capture_position).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel (ESC)",
                  command=cancel_capture).pack(side=tk.LEFT, padx=5)

        # Start position updates
        update_position()

        # Wait for dialog to close
        dialog.grab_set()
        dialog.wait_window()

        return result

    def _capture_single_coordinate(self) -> Optional[Tuple[int, int]]:
        """Capture a single coordinate position"""
        try:
            # Try to use mouse/keyboard libraries if available
            import mouse
            import keyboard

            print("Position mouse and press SPACEBAR to capture coordinate...")
            print("Press ESC to cancel")

            captured_pos = None

            def on_key_event(event):
                nonlocal captured_pos
                if event.name == 'space' and event.event_type == keyboard.KEY_DOWN:
                    captured_pos = mouse.get_position()
                    return False  # Stop listening
                elif event.name == 'esc' and event.event_type == keyboard.KEY_DOWN:
                    return False  # Stop listening (cancelled)

            # Listen for spacebar or escape
            keyboard.hook(on_key_event)
            keyboard.wait()  # Wait for key press

            return captured_pos

        except ImportError:
            # Fallback method using tkinter
            print("Using fallback coordinate capture method...")

            # Create a simple dialog for manual input
            result = simpledialog.askstring(
                "Coordinate Capture",
                "Mouse/keyboard libraries not available.\n"
                "Please enter coordinates manually (format: x,y):"
            )

            if result:
                try:
                    x, y = map(int, result.split(','))
                    return (x, y)
                except ValueError:
                    messagebox.showerror("Error", "Invalid coordinate format. Use: x,y")

            return None

        except Exception as e:
            print(f"Error in coordinate capture: {str(e)}")
            return None

    def _capture_screen_region(self) -> Optional[Tuple[int, int, int, int]]:
        """Capture a screen region with two clicks (top-left, bottom-right)"""
        try:
            # Try to use mouse/keyboard libraries if available
            import mouse
            import keyboard
            return self._capture_region_with_libraries()
        except ImportError:
            # Fallback to simple method
            return self._capture_region_simple()

    def _capture_region_with_libraries(self) -> Optional[Tuple[int, int, int, int]]:
        """Capture region using mouse/keyboard libraries"""
        import mouse

        clicks = []

        try:
            # Show instruction overlay
            instruction_window = self._create_instruction_overlay(
                "Click TOP-LEFT corner, then BOTTOM-RIGHT corner\nPress ESC to cancel"
            )

            # Get first click
            mouse.wait(button='left', target_types=('down',))
            clicks.append(mouse.get_position())

            # Update instruction
            instruction_window.destroy()
            instruction_window = self._create_instruction_overlay(
                f"First corner: {clicks[0]}\nNow click BOTTOM-RIGHT corner"
            )

            # Get second click
            mouse.wait(button='left', target_types=('down',))
            clicks.append(mouse.get_position())

            instruction_window.destroy()

            if len(clicks) == 2:
                # Calculate region from two points
                x1, y1 = clicks[0]
                x2, y2 = clicks[1]

                # Ensure top-left and bottom-right are correct
                x = min(x1, x2)
                y = min(y1, y2)
                width = abs(x2 - x1)
                height = abs(y2 - y1)

                return (x, y, width, height)

            return None

        except Exception as e:
            print(f"Error capturing region with libraries: {str(e)}")
            return None

    def _capture_region_simple(self) -> Optional[Tuple[int, int, int, int]]:
        """Simple region capture using keyboard triggers"""
        # Create dialog for two-point selection
        dialog = tk.Toplevel(self.root)
        dialog.title("Capture Screen Region")
        dialog.geometry("450x250")
        dialog.attributes('-topmost', True)
        dialog.attributes('-alpha', 0.9)
        dialog.configure(bg='darkgreen')

        # Center dialog
        dialog.eval('tk::PlaceWindow . center')

        # Create labels
        title_label = tk.Label(dialog, text="Screen Region Capture",
                              fg='white', bg='darkgreen',
                              font=('Arial', 14, 'bold'))
        title_label.pack(pady=10)

        instruction_label = tk.Label(dialog,
                                   text="Step 1: Position mouse at TOP-LEFT corner\nThen press SPACEBAR",
                                   fg='white', bg='darkgreen',
                                   font=('Arial', 11),
                                   justify=tk.CENTER)
        instruction_label.pack(pady=10)

        position_label = tk.Label(dialog, text="Current position: (0, 0)",
                                fg='lightgreen', bg='darkgreen',
                                font=('Arial', 10))
        position_label.pack(pady=5)

        status_label = tk.Label(dialog, text="Waiting for first point...",
                              fg='yellow', bg='darkgreen',
                              font=('Arial', 12, 'bold'))
        status_label.pack(pady=5)

        points_label = tk.Label(dialog, text="Point 1: Not captured\nPoint 2: Not captured",
                              fg='lightblue', bg='darkgreen',
                              font=('Arial', 10),
                              justify=tk.LEFT)
        points_label.pack(pady=5)

        # Control buttons
        button_frame = tk.Frame(dialog, bg='darkgreen')
        button_frame.pack(pady=10)

        points = []
        result = None

        def update_position():
            """Update current mouse position display"""
            if dialog.winfo_exists():
                try:
                    pos = pyautogui.position()
                    position_label.config(text=f"Current position: ({pos.x}, {pos.y})")
                    dialog.after(100, update_position)  # Update every 100ms
                except:
                    pass

        def capture_point():
            """Capture current mouse position"""
            pos = pyautogui.position()
            points.append(pos)

            if len(points) == 1:
                # First point captured
                points_label.config(text=f"Point 1: {pos} ✓\nPoint 2: Not captured")
                status_label.config(text="✓ First point captured!")
                instruction_label.config(text="Step 2: Position mouse at BOTTOM-RIGHT corner\nThen press SPACEBAR again")

            elif len(points) == 2:
                # Second point captured
                points_label.config(text=f"Point 1: {points[0]} ✓\nPoint 2: {pos} ✓")
                status_label.config(text="✓ Region captured!")
                instruction_label.config(text="Both points captured successfully!")

                # Calculate region
                x1, y1 = points[0]
                x2, y2 = points[1]

                x = min(x1, x2)
                y = min(y1, y2)
                width = abs(x2 - x1)
                height = abs(y2 - y1)

                nonlocal result
                result = (x, y, width, height)

                region_info = tk.Label(dialog, text=f"Region: ({x}, {y}) {width}x{height}",
                                     fg='lightgreen', bg='darkgreen',
                                     font=('Arial', 11, 'bold'))
                region_info.pack(pady=5)

                dialog.after(2000, dialog.destroy)  # Auto-close after 2 seconds

        def cancel_capture():
            nonlocal result
            result = None
            dialog.destroy()

        def on_key_press(event):
            """Handle keyboard input"""
            if event.keysym == 'space' and len(points) < 2:
                capture_point()
            elif event.keysym == 'Escape':
                cancel_capture()

        # Bind keyboard events
        dialog.bind('<KeyPress>', on_key_press)
        dialog.focus_set()

        # Create buttons
        ttk.Button(button_frame, text="Capture Point (SPACE)",
                  command=capture_point).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel (ESC)",
                  command=cancel_capture).pack(side=tk.LEFT, padx=5)

        # Start position updates
        update_position()

        # Wait for dialog to close
        dialog.grab_set()
        dialog.wait_window()

        return result

    def _create_instruction_overlay(self, text: str):
        """Create a semi-transparent instruction overlay"""
        overlay = tk.Toplevel()
        overlay.title("Capture Instructions")
        overlay.geometry("400x150")
        overlay.attributes('-topmost', True)
        overlay.attributes('-alpha', 0.9)
        overlay.configure(bg='black')

        # Center the overlay
        overlay.eval('tk::PlaceWindow . center')

        # Instruction text
        label = tk.Label(overlay, text=text,
                        fg='white', bg='black',
                        font=('Arial', 12, 'bold'),
                        justify=tk.CENTER)
        label.pack(expand=True, fill=tk.BOTH, padx=20, pady=20)

        # Make sure it's visible
        overlay.lift()
        overlay.focus_force()
        overlay.update()

        return overlay

    # Module management methods
    def _add_module(self):
        """Add a new module"""
        name = simpledialog.askstring("Module Name", "Enter module name:")
        if not name:
            return

        if name in self.config_manager.list_modules():
            messagebox.showwarning("Warning", f"Module '{name}' already exists")
            return

        # Create basic module config
        basic_config = {
            "enabled": True,
            "priority": 5,
            "cooldown": 60,
            "templates": [],
            "text_patterns": [],
            "click_coordinates": [],
            "scan_regions": [],
            "actions": []
        }

        self.config_manager.update_module_config(name, basic_config)
        self._load_modules()

        # Select the new module
        modules = self.config_manager.list_modules()
        if name in modules:
            index = modules.index(name)
            self.module_listbox.selection_set(index)
            self.current_module = name
            self._load_module_config()

    def _delete_module(self):
        """Delete selected module"""
        selection = self.module_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a module to delete")
            return

        module_name = self.module_listbox.get(selection[0])

        if messagebox.askyesno("Confirm", f"Delete module '{module_name}' and all its configuration?"):
            # Remove from config
            if module_name in self.config_manager.config:
                del self.config_manager.config[module_name]
                self.config_manager.save_config()

            self._load_modules()
            self.current_module = None

            # Clear all trees
            for tree in [self.templates_tree, self.text_tree, self.coord_tree, self.region_tree, self.actions_tree]:
                for item in tree.get_children():
                    tree.delete(item)

    def _export_module(self):
        """Export selected module configuration"""
        selection = self.module_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a module to export")
            return

        module_name = self.module_listbox.get(selection[0])
        filename = filedialog.asksaveasfilename(
            title="Export Module Configuration",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialvalue=f"{module_name}_config.json"
        )

        if filename:
            self.config_manager.export_module_config(module_name, filename)
            messagebox.showinfo("Success", f"Module '{module_name}' exported to {filename}")

    # Tool methods
    def _take_screenshot(self):
        """Take a screenshot for reference"""
        choice = messagebox.askyesnocancel("Screenshot Type",
                                          "Take full screen screenshot?\n\n"
                                          "Yes = Full screen\n"
                                          "No = Select region\n"
                                          "Cancel = Cancel")

        if choice is None:  # Cancel
            return

        try:
            if choice:  # Full screen
                screenshot = pyautogui.screenshot()
            else:  # Select region
                messagebox.showinfo("Select Region",
                                   "Click OK, then select the region to screenshot.\n"
                                   "Click TOP-LEFT corner, then BOTTOM-RIGHT corner.")

                self.root.iconify()
                time.sleep(0.5)

                region_coords = self._capture_screen_region()
                self.root.deiconify()

                if not region_coords:
                    messagebox.showinfo("Cancelled", "Screenshot cancelled")
                    return

                x, y, width, height = region_coords
                screenshot = pyautogui.screenshot(region=(x, y, width, height))

            # Save screenshot
            filename = filedialog.asksaveasfilename(
                title="Save Screenshot",
                defaultextension=".png",
                filetypes=[("PNG files", "*.png"), ("All files", "*.*")],
                initialvalue=f"screenshot_{int(time.time())}.png"
            )

            if filename:
                screenshot.save(filename)
                messagebox.showinfo("Success", f"Screenshot saved to {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to take screenshot: {str(e)}")
            if hasattr(self, 'root'):
                self.root.deiconify()

    def _setup_templates(self):
        """Launch the template setup tool"""
        try:
            import subprocess
            import sys

            # Launch the template setup tool
            subprocess.Popen([sys.executable, "setup_debug_templates.py"])
            self.status_label.config(text="Template setup tool launched")

            messagebox.showinfo("Template Setup",
                "Template setup tool launched!\n\n"
                "This tool will help you capture:\n"
                "• events_button - Main screen detection\n"
                "• base_button - Base view detection\n"
                "• world_button - World map detection\n\n"
                "Position mouse over UI elements and press SPACE to capture.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch template setup: {str(e)}")
            self.status_label.config(text="Template setup failed")

    def _import_config(self):
        """Import module configuration from file"""
        filename = filedialog.askopenfilename(
            title="Import Module Configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                self.config_manager.import_module_config(filename)
                self._load_modules()
                messagebox.showinfo("Success", f"Configuration imported from {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to import configuration: {str(e)}")

    def _export_all(self):
        """Export all module configurations"""
        filename = filedialog.asksaveasfilename(
            title="Export All Configurations",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialvalue="all_modules_config.json"
        )

        if filename:
            try:
                with open(filename, 'w') as f:
                    json.dump(self.config_manager.config, f, indent=2)
                messagebox.showinfo("Success", f"All configurations exported to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export configurations: {str(e)}")

    def _validate_config(self):
        """Validate all module configurations"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module to validate")
            return

        issues = self.config_manager.validate_module_config(self.current_module)

        if not issues:
            messagebox.showinfo("Validation", f"Module '{self.current_module}' configuration is valid!")
        else:
            issue_text = "\n".join([f"• {issue}" for issue in issues])
            messagebox.showwarning("Validation Issues", f"Issues found in '{self.current_module}':\n\n{issue_text}")

    def _save_all(self):
        """Save all configurations"""
        try:
            self.config_manager.save_config()
            messagebox.showinfo("Success", "All configurations saved successfully!")
            self.status_label.config(text="All configurations saved")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configurations: {str(e)}")

    def run(self):
        """Start the configuration editor"""
        self.root.mainloop()


    # Settings tab methods
    def _debounced_save(self):
        """Save configuration after a short delay to prevent excessive saves"""
        if self.save_timer:
            self.root.after_cancel(self.save_timer)

        self.save_timer = self.root.after(1000, self._perform_save)  # Save after 1 second

    def _perform_save(self):
        """Actually perform the save operation"""
        try:
            self.config_manager.save_config()
            self.save_timer = None
            self.status_label.config(text=f"Settings saved for {self.current_module}")
            # Clear the status after 3 seconds
            self.root.after(3000, lambda: self.status_label.config(text="Ready"))
        except Exception as e:
            self.status_label.config(text=f"Save error: {str(e)}")

    def _update_enabled(self):
        """Update module enabled status"""
        if self.current_module:
            config = self.config_manager.get_module_config(self.current_module)
            if config:
                config['enabled'] = self.enabled_var.get()
                self.config_manager.update_module_config(self.current_module, config)
                self._debounced_save()  # Save with delay
                self.status_label.config(text=f"Updated enabled status for {self.current_module}")

    def _update_priority(self):
        """Update module priority"""
        if self.current_module:
            try:
                priority = int(self.priority_var.get())
                config = self.config_manager.get_module_config(self.current_module)
                if config:
                    config['priority'] = priority
                    self.config_manager.update_module_config(self.current_module, config)
                    self._debounced_save()  # Save with delay
                    self.status_label.config(text=f"Updated priority to {priority} for {self.current_module}")
            except ValueError:
                self.status_label.config(text="Invalid priority value")

    def _update_cooldown(self):
        """Update module cooldown"""
        if self.current_module:
            try:
                cooldown = float(self.cooldown_var.get())
                config = self.config_manager.get_module_config(self.current_module)
                if config:
                    config['cooldown'] = cooldown
                    self.config_manager.update_module_config(self.current_module, config)
                    self._debounced_save()  # Save with delay
                    self.status_label.config(text=f"Updated cooldown to {cooldown}s for {self.current_module}")
            except ValueError:
                self.status_label.config(text="Invalid cooldown value")

    def _update_scan_interval(self):
        """Update scan interval"""
        try:
            interval = float(self.scan_interval_var.get())
            # Update global config
            import configparser
            config = configparser.ConfigParser()
            config.read('config.ini')

            if not config.has_section('GENERAL'):
                config.add_section('GENERAL')

            config.set('GENERAL', 'scan_interval', str(interval))

            with open('config.ini', 'w') as f:
                config.write(f)

            self.status_label.config(text=f"Updated scan interval to {interval}s")
        except ValueError:
            self.status_label.config(text="Invalid scan interval value")
        except Exception as e:
            self.status_label.config(text=f"Error updating scan interval: {str(e)}")

    def _update_threshold(self):
        """Update default template threshold"""
        if self.current_module:
            try:
                threshold = float(self.threshold_var.get())
                if not (0.0 <= threshold <= 1.0):
                    self.status_label.config(text="Threshold must be between 0.0 and 1.0")
                    return

                config = self.config_manager.get_module_config(self.current_module)
                if config and 'templates' in config:
                    # Update all templates without specific threshold
                    updated_count = 0
                    for template in config['templates']:
                        if 'threshold' not in template or template['threshold'] == 0.8:
                            template['threshold'] = threshold
                            updated_count += 1

                    self.config_manager.update_module_config(self.current_module, config)
                    self._debounced_save()  # Save with delay
                    self._load_templates()
                    self.status_label.config(text=f"Updated threshold to {threshold} for {updated_count} templates")
            except ValueError:
                self.status_label.config(text="Invalid threshold value")

    def _update_max_attempts(self):
        """Update max attempts for actions"""
        if self.current_module:
            try:
                max_attempts = int(self.max_attempts_var.get())
                config = self.config_manager.get_module_config(self.current_module)
                if config and 'actions' in config:
                    # Update all actions without specific max_attempts
                    for action in config['actions']:
                        if 'max_attempts' not in action:
                            action['max_attempts'] = max_attempts
                    self.config_manager.update_module_config(self.current_module, config)
                    self._load_actions()
            except ValueError:
                pass

    def _update_wait_time(self):
        """Update wait time between actions"""
        if self.current_module:
            try:
                wait_time = float(self.wait_time_var.get())
                config = self.config_manager.get_module_config(self.current_module)
                if config and 'actions' in config:
                    # Update all actions without specific wait time
                    for action in config['actions']:
                        if 'wait' not in action:
                            action['wait'] = wait_time
                    self.config_manager.update_module_config(self.current_module, config)
                    self._load_actions()
            except ValueError:
                pass

    def _reset_settings(self):
        """Reset settings to defaults"""
        if self.current_module:
            if messagebox.askyesno("Reset Settings",
                                 f"Reset all settings for {self.current_module} to defaults?"):
                config = self.config_manager.get_module_config(self.current_module)
                if config:
                    # Reset to defaults
                    config['enabled'] = True
                    config['priority'] = 2
                    config['cooldown'] = 30

                    # Reset template thresholds
                    if 'templates' in config:
                        for template in config['templates']:
                            template['threshold'] = 0.8

                    # Reset action settings
                    if 'actions' in config:
                        for action in config['actions']:
                            action.pop('max_attempts', None)
                            action.pop('wait', None)

                    self.config_manager.update_module_config(self.current_module, config)
                    self._load_module_settings()
                    self._load_templates()
                    self._load_actions()

    def _apply_settings(self):
        """Apply all current settings"""
        if self.current_module:
            try:
                # Cancel any pending save
                if self.save_timer:
                    self.root.after_cancel(self.save_timer)
                    self.save_timer = None

                # Force immediate save
                self.config_manager.save_config()
                self.status_label.config(text=f"Settings applied and saved for {self.current_module}")
                messagebox.showinfo("Settings Applied",
                                  f"Settings for {self.current_module} have been applied and saved successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to apply settings: {str(e)}")

    def _export_settings(self):
        """Export current settings to file"""
        if self.current_module:
            config = self.config_manager.get_module_config(self.current_module)
            if config:
                filename = filedialog.asksaveasfilename(
                    title="Export Settings",
                    defaultextension=".json",
                    filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                    initialvalue=f"{self.current_module}_settings.json"
                )

                if filename:
                    import json
                    try:
                        with open(filename, 'w') as f:
                            json.dump(config, f, indent=2)
                        messagebox.showinfo("Export Successful",
                                          f"Settings exported to {filename}")
                    except Exception as e:
                        messagebox.showerror("Export Failed", f"Failed to export settings: {str(e)}")

    def _on_coord_select(self, event):
        """Handle coordinate selection for inline delay, repeat, and enabled editing"""
        selection = self.coord_tree.selection()
        if selection:
            item = self.coord_tree.item(selection[0])
            values = item['values']

            if len(values) >= 6:  # New format with enabled column
                enabled_text = values[0]
                coord_name = values[1]
                current_delay = values[4]
                current_repeat = values[5] if len(values) > 5 else 1

                self.selected_coord_label.config(text=coord_name, foreground="black")
                self.enabled_var.set(enabled_text == "✓")
                self.delay_var.set(str(current_delay))
                self.repeat_var.set(str(current_repeat))

                print(f"DEBUG: Selected {coord_name}, enabled={enabled_text == '✓'}, delay={current_delay}, repeat={current_repeat}")
            else:
                # Fallback for old format
                self.selected_coord_label.config(text="Invalid format", foreground="red")
                self.enabled_var.set(True)
                self.delay_var.set("2.0")
                self.repeat_var.set("1")
        else:
            self.selected_coord_label.config(text="None", foreground="gray")
            self.enabled_var.set(True)
            self.delay_var.set("2.0")
            self.repeat_var.set("1")

    def _on_delay_change(self, event=None):
        """Handle delay change in spinbox"""
        # This is called when user types or uses arrows
        pass  # We'll apply on button click for better control

    def _on_repeat_change(self, event=None):
        """Handle repeat change in spinbox"""
        # This is called when user types or uses arrows
        pass  # We'll apply on button click for better control

    def _apply_changes(self):
        """Apply the enabled, delay and repeat changes to selected coordinate"""
        selection = self.coord_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a coordinate first")
            return

        try:
            new_enabled = self.enabled_var.get()
            new_delay = float(self.delay_var.get())
            if new_delay < 0.01 or new_delay > 10.0:
                messagebox.showerror("Error", "Delay must be between 0.01 and 10.0 seconds")
                return

            new_repeat = int(self.repeat_var.get())
            if new_repeat < 1 or new_repeat > 20:
                messagebox.showerror("Error", "Repeat must be between 1 and 20")
                return

            # Get current coordinate data
            item = self.coord_tree.item(selection[0])
            coord_name = item['values'][1]  # Name is now in column 1

            print(f"DEBUG: Applying changes to {coord_name}: enabled={new_enabled}, delay={new_delay}, repeat={new_repeat}")

            # Update in config
            config = self.config_manager.get_module_config(self.current_module)
            if config and 'click_coordinates' in config:
                for coord in config['click_coordinates']:
                    if coord['name'] == coord_name:
                        old_enabled = coord.get('enabled', True)
                        old_delay = coord.get('delay', 2.0)
                        old_repeat = coord.get('repeat', 1)
                        coord['enabled'] = new_enabled
                        coord['delay'] = new_delay
                        coord['repeat'] = new_repeat

                        # Update the tree view
                        enabled_text = "✓" if new_enabled else "✗"
                        self.coord_tree.item(selection[0], values=(
                            enabled_text,
                            coord['name'],
                            coord['x'],
                            coord['y'],
                            new_delay,
                            new_repeat,
                            coord.get('description', '')
                        ))

                        # Save config
                        self.config_manager.update_module_config(self.current_module, config)
                        self._debounced_save()

                        # Show feedback
                        status_text = "enabled" if new_enabled else "disabled"
                        self.selected_coord_label.config(text=f"{coord_name} ✓", foreground="green")
                        self.root.after(2000, lambda: self.selected_coord_label.config(text=coord_name, foreground="black"))

                        print(f"Updated {coord_name}: {status_text}, {old_delay}s → {new_delay}s, {old_repeat}× → {new_repeat}×")
                        break

        except ValueError:
            messagebox.showerror("Error", "Please enter valid delay and repeat values")

    def _enable_all_steps(self):
        """Enable all steps in the current module"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        try:
            config = self.config_manager.get_module_config(self.current_module)
            if not config or 'click_coordinates' not in config:
                messagebox.showwarning("Warning", "No coordinates found for this module")
                return

            # Enable all coordinates
            enabled_count = 0
            for coord in config['click_coordinates']:
                if not coord.get('enabled', True):
                    coord['enabled'] = True
                    enabled_count += 1

            if enabled_count > 0:
                # Save config
                self.config_manager.update_module_config(self.current_module, config)
                self._debounced_save()

                # Reload the coordinate list
                self._load_coordinates()

                messagebox.showinfo("Success", f"Enabled {enabled_count} disabled steps!")
                self.logger.info(f"✅ Enabled {enabled_count} steps in {self.current_module}")
            else:
                messagebox.showinfo("Info", "All steps are already enabled!")

        except Exception as e:
            self.logger.error(f"Error enabling all steps: {str(e)}")
            messagebox.showerror("Error", f"Failed to enable all steps: {str(e)}")

    def _reload_module_live(self):
        """Reload the module configuration live without restart"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        try:
            # Save current config first
            self._save_all_changes()

            # Try to reload the module if it's daily_tasks
            if self.current_module == "daily_tasks":
                # Import the main controller to reload the module
                try:
                    import importlib
                    import sys

                    # Reload the daily tasks module
                    if 'modules.daily_tasks' in sys.modules:
                        importlib.reload(sys.modules['modules.daily_tasks'])

                    messagebox.showinfo("Success", f"Module '{self.current_module}' reloaded!\nNew delays will take effect immediately.")

                except Exception as e:
                    messagebox.showwarning("Partial Success",
                                         f"Config saved but module reload failed: {str(e)}\n"
                                         f"Restart the application to apply changes.")
            else:
                messagebox.showinfo("Config Saved",
                                  f"Configuration saved for '{self.current_module}'.\n"
                                  f"Restart the application to apply changes.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to reload module: {str(e)}")

    def _save_all_changes(self):
        """Save all configuration changes"""
        try:
            self.config_manager.save_config()
            print(f"All changes saved for module: {self.current_module}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save changes: {str(e)}")

    def run(self):
        """Run the configuration editor"""
        self.root.mainloop()


def main():
    """Main entry point for configuration editor"""
    import sys

    try:
        app = ConfigEditorGUI()

        # Handle command line arguments for direct module opening
        if len(sys.argv) > 1:
            module_name = sys.argv[1]
            # Remove any --module prefix if present
            if module_name.startswith('--module'):
                if len(sys.argv) > 2:
                    module_name = sys.argv[2]
                else:
                    module_name = None

            if module_name and module_name in app.config_manager.list_modules():
                # Select the module directly
                app._select_module(module_name)
                print(f"Opened configuration for module: {module_name}")
            else:
                print(f"Module '{module_name}' not found. Available modules: {app.config_manager.list_modules()}")

        app.run()
    except Exception as e:
        print(f"Error starting configuration editor: {str(e)}")


if __name__ == "__main__":
    main()
