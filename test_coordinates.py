#!/usr/bin/env python3
"""Test coordinate loading directly"""

import os
import json

def test_coordinate_loading():
    # Test loading Good_setting_map.json
    good_setting_path = os.path.join("templates", "Good_setting_map.json")
    
    print(f"Looking for: {good_setting_path}")
    print(f"File exists: {os.path.exists(good_setting_path)}")
    
    if os.path.exists(good_setting_path):
        with open(good_setting_path, 'r') as f:
            good_coordinates = json.load(f)
        
        print("Good_setting_map coordinates:")
        for piece_name, coords in good_coordinates.items():
            print(f"  {piece_name}: ({coords[0]}, {coords[1]})")
    
    # Test creating a simple map_piece_regions structure
    map_piece_regions = {
        'M1': {'big_map_pos': (1000, 1000)},
        'M2': {'big_map_pos': (2000, 2000)},
    }
    
    print("\nBefore update:")
    for piece, data in map_piece_regions.items():
        print(f"  {piece}: {data['big_map_pos']}")
    
    # Simulate the update
    if os.path.exists(good_setting_path):
        with open(good_setting_path, 'r') as f:
            good_coordinates = json.load(f)
        
        for piece_name, coords in good_coordinates.items():
            if piece_name in map_piece_regions:
                map_piece_regions[piece_name]['big_map_pos'] = (coords[0], coords[1])
                print(f"Updated {piece_name} to ({coords[0]}, {coords[1]})")
    
    print("\nAfter update:")
    for piece, data in map_piece_regions.items():
        print(f"  {piece}: {data['big_map_pos']}")

if __name__ == "__main__":
    test_coordinate_loading()
