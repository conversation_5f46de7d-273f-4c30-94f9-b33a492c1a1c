#!/usr/bin/env python3
"""
Test script for Enhanced ESC Recovery System

This script tests the new multi-method detection system for ESC recovery
including template matching, color detection, and OCR detection with
comprehensive debugging and visualization.
"""

import os
import sys
import time
import logging
from typing import Dict, Any

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from screen_scanner import ScreenScanner
from centralized_template_scanner import CentralizedTemplateScanner

def setup_logging() -> logging.Logger:
    """Setup logging for test script."""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_enhanced_esc_recovery.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('ESC_RECOVERY_TEST')

def test_screen_scanner_enhanced_detection(logger: logging.Logger) -> bool:
    """Test the enhanced detection capabilities of the screen scanner."""
    logger.info("=== Testing Screen Scanner Enhanced Detection ===")
    
    try:
        # Initialize screen scanner
        scanner = ScreenScanner()
        
        # Test templates to detect
        test_templates = ['events_button', 'quit_game_dialog']
        
        logger.info(f"Testing enhanced detection for templates: {test_templates}")
        
        # Test with enhanced detection enabled
        logger.info("Testing with enhanced detection ENABLED...")
        enhanced_results = scanner.scan_screen_cache_optimized(
            required_templates=test_templates,
            enhanced_detection=True,
            debug_screenshots=True
        )
        
        # Log results
        logger.info("Enhanced Detection Results:")
        templates_detected = enhanced_results.get('templates_detected', {})
        detection_details = enhanced_results.get('detection_details', {})
        
        for template_name in test_templates:
            detected = templates_detected.get(template_name, False)
            details = detection_details.get(template_name, {})
            method = details.get('method', 'unknown')
            confidence = details.get('confidence', 0.0)
            
            logger.info(f"  {template_name}: {detected} (method: {method}, confidence: {confidence:.3f})")
            
            # Log debug info if available
            debug_info = details.get('debug_info', {})
            if debug_info:
                logger.info(f"    Debug files: {debug_info}")
        
        # Test with standard detection for comparison
        logger.info("Testing with standard detection for comparison...")
        standard_results = scanner.scan_screen_cache_optimized(
            required_templates=test_templates,
            enhanced_detection=False,
            debug_screenshots=False
        )
        
        logger.info("Standard Detection Results:")
        standard_templates = standard_results.get('templates_detected', {})
        for template_name in test_templates:
            detected = standard_templates.get(template_name, False)
            logger.info(f"  {template_name}: {detected}")
        
        # Compare results
        logger.info("Comparison Summary:")
        for template_name in test_templates:
            enhanced_detected = templates_detected.get(template_name, False)
            standard_detected = standard_templates.get(template_name, False)
            
            if enhanced_detected != standard_detected:
                logger.warning(f"  {template_name}: Enhanced={enhanced_detected}, Standard={standard_detected} (DIFFERENT)")
            else:
                logger.info(f"  {template_name}: Both methods agree ({enhanced_detected})")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing screen scanner enhanced detection: {str(e)}")
        return False

def test_centralized_scanner_esc_recovery(logger: logging.Logger) -> bool:
    """Test the centralized scanner ESC recovery with enhanced detection."""
    logger.info("=== Testing Centralized Scanner ESC Recovery ===")
    
    try:
        # Initialize centralized scanner
        scanner = CentralizedTemplateScanner()
        
        # Force failed scan count to trigger ESC recovery
        logger.info("Simulating failed scans to trigger ESC recovery...")
        scanner.failed_scan_count = 10  # This should trigger ESC recovery
        
        # Test ESC recovery (but don't actually perform it to avoid disrupting the system)
        logger.info("Testing ESC recovery detection logic...")
        
        # Test the screen state detection that ESC recovery uses
        screen_data = scanner.screen_scanner.scan_screen_cache_optimized(
            required_templates=['events_button', 'quit_game_dialog'],
            enhanced_detection=True,
            debug_screenshots=True
        )
        
        templates_detected = screen_data.get('templates_detected', {})
        detection_details = screen_data.get('detection_details', {})
        
        logger.info("ESC Recovery Detection Test Results:")
        for template_name in ['events_button', 'quit_game_dialog']:
            detected = templates_detected.get(template_name, False)
            details = detection_details.get(template_name, {})
            method = details.get('method', 'unknown')
            confidence = details.get('confidence', 0.0)
            
            logger.info(f"  {template_name}: {detected} (method: {method}, confidence: {confidence:.3f})")
            
            # Check what ESC recovery would do
            if template_name == 'events_button' and detected:
                logger.info("    → ESC Recovery would SUCCEED (main menu detected)")
            elif template_name == 'quit_game_dialog' and detected:
                logger.info("    → ESC Recovery would dismiss quit dialog")
            elif not detected:
                logger.info("    → ESC Recovery would continue pressing ESC")
        
        # Log debug information
        debug_info = screen_data.get('debug_info', {})
        if 'screenshot_path' in debug_info:
            logger.info(f"Debug screenshot saved: {debug_info['screenshot_path']}")
        
        for template_name, details in detection_details.items():
            template_debug_info = details.get('debug_info', {})
            if template_debug_info:
                logger.info(f"Debug files for {template_name}: {template_debug_info}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing centralized scanner ESC recovery: {str(e)}")
        return False

def test_configuration_loading(logger: logging.Logger) -> bool:
    """Test that enhanced detection configuration loads correctly."""
    logger.info("=== Testing Configuration Loading ===")
    
    try:
        scanner = ScreenScanner()
        
        # Test configuration loading for ESC recovery templates
        test_templates = ['events_button', 'quit_game_dialog']
        
        for template_name in test_templates:
            logger.info(f"Testing configuration for {template_name}...")
            
            config = scanner._get_enhanced_detection_config(template_name)
            
            if config:
                logger.info(f"  Configuration loaded successfully")
                
                # Check color detection config
                color_config = config.get('color_detection', {})
                if color_config.get('enabled', False):
                    color_ranges = color_config.get('color_ranges', [])
                    logger.info(f"    Color detection: ENABLED ({len(color_ranges)} color ranges)")
                else:
                    logger.info(f"    Color detection: DISABLED")
                
                # Check OCR detection config
                ocr_config = config.get('ocr_detection', {})
                if ocr_config.get('enabled', False):
                    text_patterns = ocr_config.get('text_patterns', [])
                    logger.info(f"    OCR detection: ENABLED ({len(text_patterns)} text patterns)")
                else:
                    logger.info(f"    OCR detection: DISABLED")
            else:
                logger.warning(f"  No configuration found for {template_name}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing configuration loading: {str(e)}")
        return False

def main():
    """Main test function."""
    logger = setup_logging()
    logger.info("Starting Enhanced ESC Recovery System Tests")
    
    # Create debug directories
    os.makedirs("debug_screenshots", exist_ok=True)
    
    # Run tests
    tests = [
        ("Configuration Loading", test_configuration_loading),
        ("Screen Scanner Enhanced Detection", test_screen_scanner_enhanced_detection),
        ("Centralized Scanner ESC Recovery", test_centralized_scanner_esc_recovery)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running Test: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func(logger)
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {str(e)}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASSED" if result else "FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Enhanced ESC Recovery system is ready.")
        return 0
    else:
        logger.error("⚠️ Some tests failed. Please review the logs and fix issues.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
