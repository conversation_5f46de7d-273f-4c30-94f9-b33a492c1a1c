"""
Test imports only - no GUI
"""

def test_imports():
    """Test all imports without creating GUI"""
    print("Testing imports...")
    
    try:
        print("1. Testing basic imports...")
        import json
        import os
        import logging
        from datetime import datetime
        print("   ✅ Basic imports OK")
        
        print("2. Testing dig_module_config.json...")
        if os.path.exists("dig_module_config.json"):
            with open("dig_module_config.json", 'r') as f:
                config = json.load(f)
            print(f"   ✅ Config file loaded: {len(config)} sections")
        else:
            print("   ❌ dig_module_config.json not found")
        
        print("3. Testing dig_control_panel imports...")
        try:
            # Test individual imports from dig_control_panel
            import tkinter as tk
            from tkinter import ttk, messagebox, filedialog
            print("   ✅ tkinter imports OK")
            
            import time
            import threading
            from typing import Dict, Any, Optional, List
            print("   ✅ utility imports OK")
            
        except Exception as e:
            print(f"   ❌ Import error: {e}")
            return False
        
        print("4. Testing dig_module_refactored...")
        try:
            # Check if the refactored module exists
            if os.path.exists("dig_module_refactored.py"):
                print("   ✅ dig_module_refactored.py exists")
            else:
                print("   ❌ dig_module_refactored.py not found")
        except Exception as e:
            print(f"   ❌ Error checking dig module: {e}")
        
        print("5. Testing screen_scanner...")
        try:
            if os.path.exists("screen_scanner.py"):
                print("   ✅ screen_scanner.py exists")
            else:
                print("   ❌ screen_scanner.py not found")
        except Exception as e:
            print(f"   ❌ Error checking screen scanner: {e}")
        
        print("6. Testing config_helper...")
        try:
            if os.path.exists("config_helper.py"):
                print("   ✅ config_helper.py exists")
            else:
                print("   ❌ config_helper.py not found")
        except Exception as e:
            print(f"   ❌ Error checking config helper: {e}")
        
        print("\n✅ All import tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_imports()
