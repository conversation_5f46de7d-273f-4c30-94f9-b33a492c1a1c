#!/usr/bin/env python3
"""
Test script to verify OCR accuracy fixes and compare Config Helper vs Map Trade OCR results
"""

import os
import json
import time
import cv2
import numpy as np
import pyautogui
from typing import Dict, Optional, Tuple

def test_fast_map_ocr_directly():
    """Test Fast Map OCR system directly"""
    print("🧪 TESTING FAST MAP OCR DIRECTLY")
    print("-" * 40)
    
    try:
        from fast_map_ocr import get_fast_map_ocr
        fast_ocr = get_fast_map_ocr()
        
        # Enable debug mode like Config Helper
        fast_ocr.set_debug_mode(True)
        fast_ocr.set_validation(True)
        
        print("✅ Fast Map OCR system loaded successfully")
        print(f"   Debug mode: {fast_ocr.config['debug_mode']}")
        print(f"   Validation: {fast_ocr.config['validation_enabled']}")
        
        return fast_ocr
        
    except Exception as e:
        print(f"❌ Failed to load Fast Map OCR: {e}")
        return None

def test_map_trade_module_ocr():
    """Test Map Trade module OCR initialization"""
    print("\n🧪 TESTING MAP TRADE MODULE OCR")
    print("-" * 40)
    
    try:
        from modules.map_trade import MapTradeModule
        map_trade = MapTradeModule()
        
        print("✅ Map Trade module loaded successfully")
        
        # Check Fast OCR availability
        fast_ocr_available = getattr(map_trade, '_fast_ocr_available', False)
        print(f"   Fast OCR available: {fast_ocr_available}")
        
        if fast_ocr_available:
            config = map_trade.fast_map_ocr.config
            print(f"   Debug mode: {config['debug_mode']}")
            print(f"   Validation: {config['validation_enabled']}")
        
        return map_trade
        
    except Exception as e:
        print(f"❌ Failed to load Map Trade module: {e}")
        return None

def capture_test_image(coordinate: Tuple[int, int], size: int = 35) -> np.ndarray:
    """Capture a test image from the specified coordinate"""
    try:
        x, y = coordinate
        capture_x = x - size // 2
        capture_y = y - size // 2
        
        screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        print(f"   📸 Captured {size}x{size} image at ({x}, {y})")
        print(f"   Image shape: {image.shape}, brightness: {np.mean(image):.1f}")
        
        return image
        
    except Exception as e:
        print(f"   ❌ Failed to capture image: {e}")
        return np.zeros((size, size, 3), dtype=np.uint8)

def compare_ocr_methods():
    """Compare OCR methods using the same test image"""
    print("\n🔍 COMPARING OCR METHODS")
    print("-" * 40)
    
    # Load Good_setting_map coordinates
    good_setting_path = os.path.join("templates", "Good_setting_map.json")
    if not os.path.exists(good_setting_path):
        print("❌ Good_setting_map.json not found!")
        return
    
    with open(good_setting_path, 'r') as f:
        good_coordinates = json.load(f)
    
    # Initialize OCR systems
    fast_ocr = test_fast_map_ocr_directly()
    map_trade = test_map_trade_module_ocr()
    
    if not fast_ocr or not map_trade:
        print("❌ Failed to initialize OCR systems")
        return
    
    # Test with M1 coordinate
    test_piece = "M1"
    test_coord = good_coordinates[test_piece]
    
    print(f"\n🎯 Testing with {test_piece} at {test_coord}")
    
    # Capture test image
    test_image = capture_test_image(test_coord, 35)
    
    if test_image.shape[0] == 0:
        print("❌ Failed to capture test image")
        return
    
    # Save test image for inspection
    cv2.imwrite("test_ocr_comparison_image.png", test_image)
    print("   💾 Saved test image: test_ocr_comparison_image.png")
    
    # Test 1: Direct Fast Map OCR
    print("\n   🧠 Testing Direct Fast Map OCR...")
    try:
        start_time = time.time()
        direct_result = fast_ocr.recognize_map_piece(test_image, f"{test_piece}_DIRECT")
        direct_time = time.time() - start_time
        print(f"   Direct Fast OCR: {direct_result} (took {direct_time:.3f}s)")
    except Exception as e:
        print(f"   Direct Fast OCR failed: {e}")
        direct_result = None
    
    # Test 2: Map Trade module OCR method
    print("\n   🧠 Testing Map Trade Module OCR...")
    try:
        map_trade._current_piece_id = f"{test_piece}_MODULE"
        start_time = time.time()
        module_result = map_trade._extract_number_from_image(test_image)
        module_time = time.time() - start_time
        print(f"   Map Trade OCR: {module_result} (took {module_time:.3f}s)")
    except Exception as e:
        print(f"   Map Trade OCR failed: {e}")
        module_result = None
    finally:
        map_trade._current_piece_id = None
    
    # Test 3: Traditional OCR only
    print("\n   🧠 Testing Traditional OCR Only...")
    try:
        start_time = time.time()
        traditional_result = map_trade._extract_number_traditional_ocr(test_image)
        traditional_time = time.time() - start_time
        print(f"   Traditional OCR: {traditional_result} (took {traditional_time:.3f}s)")
    except Exception as e:
        print(f"   Traditional OCR failed: {e}")
        traditional_result = None
    
    # Compare results
    print("\n📊 COMPARISON RESULTS")
    print("-" * 40)
    print(f"Direct Fast OCR:    {direct_result}")
    print(f"Map Trade Module:    {module_result}")
    print(f"Traditional Only:    {traditional_result}")
    
    if direct_result == module_result:
        print("✅ SUCCESS: Direct Fast OCR and Map Trade module results MATCH!")
        print("   The OCR accuracy discrepancy should now be resolved.")
    else:
        print("⚠️ WARNING: Results still differ between methods")
        print("   Further investigation may be needed.")
    
    if direct_result is not None and traditional_result is not None:
        if direct_result == traditional_result:
            print("ℹ️ INFO: Fast OCR and Traditional OCR gave same result")
        else:
            print("ℹ️ INFO: Fast OCR and Traditional OCR gave different results")
            print("   This suggests Fast OCR provides better accuracy")

def main():
    print("🚀 OCR ACCURACY FIX VERIFICATION")
    print("Testing fixes for Config Helper vs Map Trade OCR discrepancy")
    print("=" * 60)
    
    # Run comparison test
    compare_ocr_methods()
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)
    print("1. Check the comparison results above")
    print("2. If Direct Fast OCR and Map Trade Module results match:")
    print("   ✅ The OCR accuracy fix is working correctly")
    print("3. If they still differ:")
    print("   ⚠️ Additional debugging may be needed")
    print("4. Test the Map Trade module with live automation to confirm")
    print("5. Compare live results with Config Helper testing")

if __name__ == "__main__":
    main()
