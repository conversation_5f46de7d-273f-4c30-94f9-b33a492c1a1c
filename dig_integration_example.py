"""
Dig Module Integration Example
Demonstrates how to integrate the refactored dig module with the main system
"""

import json
import time
from typing import Dict, Any

# Example of how to integrate the refactored dig module
class DigIntegrationExample:
    """Example integration of the refactored dig module"""
    
    def __init__(self):
        # Import the refactored dig module
        from dig_module_refactored import DigModule
        
        # Initialize the dig module
        self.dig_module = DigModule()
        
        # Mock main controller for demonstration
        self.main_controller = self._create_mock_controller()
        
        print("🚀 Dig Module Integration Example")
        print("=" * 50)
        print(f"✅ Dig module loaded: {self.dig_module.name}")
        print(f"✅ Configuration loaded: {self.dig_module.config_file}")
        print(f"✅ Module enabled: {self.dig_module.enabled}")
        print(f"✅ Current step: {self.dig_module.current_step}")
        print()
    
    def _create_mock_controller(self):
        """Create a mock main controller for testing"""
        class MockController:
            def __init__(self):
                self.screen_scanner = MockScreenScanner()
                self.paused = False
        
        class MockScreenScanner:
            def scan_screen_cache_optimized(self, **kwargs):
                # Mock screen data with sample templates
                return {
                    'templates_found': {
                        'dig_icon': [[100, 200, 50, 30, 0.8]],
                        'test_flight_treasure': [[500, 600, 100, 20, 0.9]],
                        'Base': [[800, 100, 60, 40, 0.85]]
                    },
                    'templates_detected': {
                        'dig_icon': True,
                        'test_flight_treasure': True,
                        'Base': True
                    }
                }
        
        return MockController()
    
    def demonstrate_configuration_loading(self):
        """Demonstrate configuration loading and reloading"""
        print("📋 Configuration Management Demo")
        print("-" * 30)
        
        # Show current configuration
        print("Current configuration keys:")
        for key in self.dig_module.config_data.keys():
            print(f"  • {key}")
        
        # Show trigger templates
        trigger_templates = self.dig_module.config_data.get('trigger_templates', {})
        print(f"\nTrigger templates configured: {len(trigger_templates)}")
        for template_name, config in trigger_templates.items():
            enabled = "✅" if config.get('enabled', True) else "❌"
            threshold = config.get('threshold', 0.7)
            execution_path = config.get('execution_path', 'unknown')
            print(f"  {enabled} {template_name} (threshold: {threshold}, path: {execution_path})")
        
        # Show execution steps
        execution_steps = self.dig_module.config_data.get('execution_steps', {})
        print(f"\nExecution steps configured: {len(execution_steps)}")
        for step_key, step_config in execution_steps.items():
            enabled = "✅" if step_config.get('enabled', True) else "❌"
            step_name = step_config.get('name', step_key)
            timeout = step_config.get('timeout', 10)
            print(f"  {enabled} {step_name} (timeout: {timeout}s)")
        
        print()
    
    def demonstrate_execution_flow(self):
        """Demonstrate the execution flow"""
        print("🔄 Execution Flow Demo")
        print("-" * 25)
        
        # Mock screen data
        screen_data = self.main_controller.screen_scanner.scan_screen_cache_optimized()
        
        # Check if dig can execute
        can_execute = self.dig_module.custom_can_execute(screen_data)
        print(f"Can execute: {can_execute}")
        
        if can_execute:
            print(f"Starting step: {self.dig_module.current_step}")
            print("Execution log:")
            
            # Show recent log entries
            log_entries = self.dig_module.get_execution_log()
            for entry in log_entries[-5:]:  # Show last 5 entries
                print(f"  {entry}")
        
        print()
    
    def demonstrate_status_monitoring(self):
        """Demonstrate status monitoring"""
        print("📊 Status Monitoring Demo")
        print("-" * 28)
        
        # Get current status
        status = self.dig_module.get_current_status()
        
        print("Current Status:")
        for key, value in status.items():
            if key == 'last_log_entries':
                print(f"  {key}: {len(value)} entries")
            else:
                print(f"  {key}: {value}")
        
        # Get dig status string
        dig_status = self.dig_module.get_dig_status()
        print(f"\nDig Status: {dig_status}")
        
        print()
    
    def demonstrate_step_testing(self):
        """Demonstrate individual step testing"""
        print("🧪 Step Testing Demo")
        print("-" * 22)
        
        # Mock screen data
        screen_data = self.main_controller.screen_scanner.scan_screen_cache_optimized()
        
        # Test individual steps
        for step_num in range(1, 7):
            step_names = {
                1: "Open Chat",
                2: "Find Treasure", 
                3: "Verify Navigation",
                4: "Deploy Squad",
                5: "Timer Management",
                6: "Exit Menu"
            }
            
            step_name = step_names.get(step_num, f"Step {step_num}")
            print(f"Testing {step_name} (Step {step_num})...")
            
            # Note: In real implementation, this would actually test the step
            # For demo purposes, we just show the concept
            print(f"  ✅ Step {step_num} test completed")
        
        print()
    
    def demonstrate_configuration_modification(self):
        """Demonstrate runtime configuration modification"""
        print("⚙️ Configuration Modification Demo")
        print("-" * 38)
        
        # Show how to modify configuration at runtime
        original_enabled = self.dig_module.enabled
        print(f"Original enabled state: {original_enabled}")
        
        # Modify general settings
        self.dig_module.config_data['general_settings']['enabled'] = not original_enabled
        print(f"Modified enabled state: {not original_enabled}")
        
        # Reload configuration to apply changes
        self.dig_module.reload_configuration(self.dig_module.config_data)
        print(f"After reload enabled state: {self.dig_module.enabled}")
        
        # Restore original state
        self.dig_module.config_data['general_settings']['enabled'] = original_enabled
        self.dig_module.reload_configuration(self.dig_module.config_data)
        print(f"Restored enabled state: {self.dig_module.enabled}")
        
        print()
    
    def run_full_demonstration(self):
        """Run the complete demonstration"""
        print("🎯 COMPREHENSIVE DIG MODULE DEMONSTRATION")
        print("=" * 60)
        print()
        
        # Run all demonstrations
        self.demonstrate_configuration_loading()
        self.demonstrate_execution_flow()
        self.demonstrate_status_monitoring()
        self.demonstrate_step_testing()
        self.demonstrate_configuration_modification()
        
        print("✅ Demonstration completed successfully!")
        print()
        print("Key Benefits of the Refactored System:")
        print("• 🔧 NO hardcoded values - everything configurable")
        print("• 📊 Real-time status monitoring and logging")
        print("• 🧪 Individual step testing capabilities")
        print("• ⚙️ Runtime configuration modification")
        print("• 🎯 Complete visibility into execution flow")
        print("• 🔄 Hot-reload configuration without restart")
        print("• 📋 Comprehensive control panel interface")
        print()

def main():
    """Main demonstration function"""
    try:
        # Create and run the integration example
        demo = DigIntegrationExample()
        demo.run_full_demonstration()
        
        print("🚀 Integration example completed successfully!")
        print()
        print("Next Steps:")
        print("1. Replace the existing dig.py with dig_module_refactored.py")
        print("2. Ensure dig_module_config.json is in the root directory")
        print("3. Open Config Helper and click 'Dig Module Control Panel'")
        print("4. Configure all settings through the GUI interface")
        print("5. Test individual steps and monitor real-time execution")
        
    except Exception as e:
        print(f"❌ Error running demonstration: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
