#!/usr/bin/env python3
"""
Test script to verify the scan region capture functionality
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_scan_region_gui():
    """Test the scan region capture functionality"""
    try:
        # Import required classes
        from centralized_scanner_gui import CentralizedScannerGUI
        from main_controller import MainController
        import tkinter as tk

        print("✅ Successfully imported required classes")

        # Create a root window
        root = tk.Tk()
        root.withdraw()  # Hide the root window

        # Create a main controller instance
        main_controller = MainController()
        print("✅ Successfully created MainController")

        # Create the GUI
        gui = CentralizedScannerGUI(root, main_controller)
        print("✅ Successfully created CentralizedScannerGUI")
        
        # Show a test message
        messagebox.showinfo("Test", 
                          "✅ Scan Region Capture Test\n\n"
                          "The GUI has been successfully initialized!\n"
                          "You can now:\n"
                          "1. Go to the 'Template Mappings' tab\n"
                          "2. Select a template (e.g., dig_icon)\n"
                          "3. Try the '📷 Capture Region' button\n"
                          "4. Test the scan region functionality\n\n"
                          "Click OK to continue...")
        
        # Open the GUI window
        gui.open()

        # Start the main loop
        root.mainloop()
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("🧪 Testing Scan Region Capture Functionality...")
    print("=" * 50)
    
    success = test_scan_region_gui()
    
    if success:
        print("✅ Test completed successfully!")
    else:
        print("❌ Test failed!")
        sys.exit(1)
