{"template_mappings": [{"template_name": "dig_icon", "priority": 0, "module": "dig", "action": "custom_execute", "description": "dig_icon -> dig.custom_execute (Highest Priority)", "enabled": false, "threshold": 0.8, "cooldown": 0.0}, {"template_name": "dig_up_dropdown", "priority": 0, "module": "dig", "action": "custom_execute", "description": "dig_up_dropdown -> dig.custom_execute (Highest Priority)", "enabled": false, "threshold": 0.8, "cooldown": 0.0}, {"template_name": "test_flight_treasure", "priority": 0, "module": "dig", "action": "custom_execute", "description": "test_flight_treasure -> dig.custom_execute (Highest Priority)", "enabled": false, "threshold": 0.8, "cooldown": 0.0}, {"template_name": "dig_up_treasure", "priority": 0, "module": "dig", "action": "custom_execute", "description": "dig_up_treasure -> dig.custom_execute (Highest Priority)", "enabled": false, "threshold": 0.8, "cooldown": 0.0}, {"template_name": "Help_template", "priority": 1, "module": "help_click", "action": "action_1_HelpButton", "description": "Help_template -> help_click.1_HelpButton", "enabled": true, "threshold": 0.8, "cooldown": 0.0}, {"template_name": "help_world", "priority": 1, "module": "help_click", "action": "action_1_HelpButton", "description": "help_world -> help_click.action_1_HelpButton", "enabled": true, "threshold": 0.8, "cooldown": 0.0}, {"template_name": "Help_chat", "priority": 2, "module": "help_click", "action": "action_2_HelpChat", "description": "Help chat button - lower priority", "enabled": true, "threshold": 0.7, "cooldown": 0.0}, {"template_name": "squad_0_4", "priority": 4, "module": "zombie_invasion", "action": "deploy_squad", "description": "squad_0_4 -> zombie_invasion.deploy_squad", "enabled": false, "threshold": 0.9, "cooldown": 0.0}, {"template_name": "squad_1_4", "priority": 5, "module": "zombie_invasion", "action": "deploy_squad", "description": "squad_1_4 -> zombie_invasion.deploy_squad", "enabled": false, "threshold": 0.9, "cooldown": 30.0}, {"template_name": "squad_2_4", "priority": 6, "module": "zombie_invasion", "action": "deploy_squad", "description": "squad_2_4 -> zombie_invasion.deploy_squad", "enabled": false, "threshold": 0.9, "cooldown": 30.0}, {"template_name": "squad_3_4", "priority": 7, "module": "zombie_invasion", "action": "deploy_squad", "description": "squad_3_4 -> zombie_invasion.deploy_squad", "enabled": false, "threshold": 0.9, "cooldown": 0.0}, {"template_name": "squad_4_4", "priority": 8, "module": "zombie_invasion", "action": "deploy_squad", "description": "squad_4_4 -> zombie_invasion.deploy_squad", "enabled": false, "threshold": 0.9, "cooldown": 0.0}, {"template_name": "map_button", "priority": 9, "module": "map_trade", "action": "start_trading", "description": "map_button -> map_trade.start_trading", "enabled": true, "threshold": 0.75, "cooldown": 300.0}, {"template_name": "alliance_button", "priority": 12, "module": "alliance_donation", "action": "start_sequence", "description": "alliance_button -> alliance_donation.start_sequence", "enabled": false, "threshold": 0.8, "cooldown": 3600.0}], "exclusion_rules": [{"name": "help_priority_rule", "description": "If Help_Template is detected, ignore Help_chat", "primary_template": "Help_Template", "excluded_templates": ["Help_chat"], "enabled": true}, {"name": "truck_priority_rule", "description": "Golden trucks have priority over purple trucks", "primary_template": "Golden_Truck", "excluded_templates": ["Purple_Truck"], "enabled": true}], "scanner_settings": {"scan_interval": 1.0, "max_concurrent_detections": 3, "template_cache_size": 100, "detection_timeout": 5.0, "priority_override_enabled": true, "debug_mode": false}}