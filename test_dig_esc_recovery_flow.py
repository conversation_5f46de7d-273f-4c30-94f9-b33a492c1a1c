#!/usr/bin/env python3
"""
Test script to verify dig module ESC recovery flow returns control to main scanner
"""

import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dig_esc_recovery_flow():
    """Test that dig module returns control to main scanner after ESC recovery"""
    try:
        logger.info("🧪 Testing Dig Module ESC Recovery Flow...")
        
        from modules.dig import DigModule
        
        # Create dig module
        dig_module = DigModule()
        
        # Mock the ESC recovery method
        esc_recovery_called = False
        def mock_trigger_esc_recovery():
            nonlocal esc_recovery_called
            esc_recovery_called = True
            logger.info("✅ Mock ESC recovery triggered")
        
        dig_module._trigger_esc_recovery = mock_trigger_esc_recovery
        
        # Set up the scenario: 10 failed treasure scans
        dig_module.current_step = dig_module.STEP_FIND_TREASURE
        dig_module.dig_in_progress = True
        dig_module.treasure_scan_attempts = 10  # At the limit
        
        # Mock screen data with no treasure templates
        mock_screen_data = {
            'templates_found': {}  # No templates found
        }
        
        # Test the treasure finding step with 10 failed attempts
        result = dig_module._step_find_treasure(mock_screen_data)
        
        # Verify the expected behavior
        if result == True:  # Should return True to give control back to main scanner
            logger.info("✅ Dig module correctly returns True after ESC recovery")
        else:
            logger.error(f"❌ Dig module returned {result}, expected True")
            return False
        
        if esc_recovery_called:
            logger.info("✅ ESC recovery was triggered as expected")
        else:
            logger.error("❌ ESC recovery was not triggered")
            return False
        
        if not dig_module.dig_in_progress:
            logger.info("✅ Dig state was reset correctly")
        else:
            logger.error("❌ Dig state was not reset")
            return False
        
        logger.info("✅ Dig module ESC recovery flow verified")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_scanner_template_config():
    """Test that main scanner has treasure templates configured"""
    try:
        logger.info("🧪 Testing Main Scanner Template Configuration...")
        
        # Check template scanner config
        import json
        with open('template_scanner_config.json', 'r') as f:
            config = json.load(f)
        
        template_mappings = config.get('template_mappings', [])
        
        # Look for treasure templates
        treasure_templates = ['test_flight_treasure', 'dig_up_treasure', 'dig_up_dropdown']
        found_templates = []
        
        for mapping in template_mappings:
            template_name = mapping.get('template_name', '')
            if template_name in treasure_templates:
                found_templates.append(template_name)
                module = mapping.get('module', '')
                action = mapping.get('action', '')
                enabled = mapping.get('enabled', False)
                
                logger.info(f"✅ Found template: {template_name} -> {module}.{action} (enabled: {enabled})")
                
                if module != 'dig' or action != 'custom_execute':
                    logger.error(f"❌ Template {template_name} has wrong module/action: {module}.{action}")
                    return False
                
                if not enabled:
                    logger.warning(f"⚠️ Template {template_name} is disabled")
        
        missing_templates = set(treasure_templates) - set(found_templates)
        if missing_templates:
            logger.error(f"❌ Missing templates: {missing_templates}")
            return False
        
        logger.info("✅ All treasure templates are properly configured in main scanner")
        return True
        
    except Exception as e:
        logger.error(f"❌ Template config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    logger.info("🚀 Testing Dig Module ESC Recovery Flow...")
    logger.info("=" * 60)
    
    # Test dig module ESC recovery flow
    dig_flow_ok = test_dig_esc_recovery_flow()
    
    # Test main scanner template configuration
    template_config_ok = test_main_scanner_template_config()
    
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST RESULTS:")
    logger.info(f"Dig ESC Recovery Flow: {'✅ PASSED' if dig_flow_ok else '❌ FAILED'}")
    logger.info(f"Template Configuration: {'✅ PASSED' if template_config_ok else '❌ FAILED'}")
    
    if dig_flow_ok and template_config_ok:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("💡 Expected behavior:")
        logger.info("   • Dig module scans for treasure templates 10 times")
        logger.info("   • After 10 failed scans, triggers ESC recovery")
        logger.info("   • Returns control to main scanner (returns True)")
        logger.info("   • Main scanner continues and can detect treasure templates")
        logger.info("   • If treasure templates found, dig module triggers again")
    else:
        logger.warning("\n⚠️ Some tests failed - manual investigation needed")
    
    return dig_flow_ok and template_config_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
