"""
Dig <PERSON>dule for Last War automation
Handles excavation tasks with proper timing and coordination

Updated workflow implementation:
1. Detection & Initialization - detect dig triggers, pause main scanner
2. Open Alliance Chat - click (1267,1353) then (1176,170)
3. Find Treasure Link - scan for dig_up_treasure/test_flight_treasure, click or use chat_click backup
4. Verify Chat State & Navigate - check if chat closed, look for send_to_dig templates, click (1279,646)
5. Deploy Squad - click (1279,875) send squad, (1279,1101) confirm, (1279,646) open timer
6. Timer Management & Rapid Clicking - detect timer, rapid click (1279,646) when <10s for 20s
7. Exit to Main Menu - ESC until quit_game_dialog, then ESC once more
8. Send Completion Message - open chat, type message at (1073,1327), press Enter
9. Return Control - ESC to close, resume main scanner
"""

import time
import pyautogui
import pyperclip
from typing import Dict, Any
from enhanced_base_module import EnhancedBaseModule


class DigModule(EnhancedBaseModule):
    """
    Dig module for excavation automation with detailed 9-step workflow
    Integrates with centralized template management and has highest priority
    """

    def __init__(self):
        super().__init__(name="dig", priority=-1, enabled=True)  # Highest priority

        # Load configuration from module config
        self._load_dig_config()

        # State tracking
        self.dig_in_progress = False
        self.current_step = 0

        # Step tracking - Updated workflow
        self.STEP_DETECT = 0
        self.STEP_OPEN_CHAT = 1
        self.STEP_FIND_TREASURE = 2
        self.STEP_VERIFY_CHAT_STATE = 3
        self.STEP_DEPLOY_SQUAD = 4
        self.STEP_TIMER_MANAGEMENT = 5
        self.STEP_EXIT_MENU = 6
        self.STEP_SEND_MESSAGE = 7
        self.STEP_RETURN_CONTROL = 8

    def _load_dig_config(self):
        """Load dig-specific configuration from unified config"""
        try:
            # Try unified config first, fallback to legacy
            config = self.unified_config_manager.get_module_config("dig")
            if not config:
                config = self.config_manager.get_module_config("dig")

            if config:
                # Load from configuration with updated defaults
                settings = config.get("settings", {})
                self.rapid_click_speed = settings.get("rapid_click_speed", 0.005)  # Updated default
                self.rapid_click_duration = settings.get("rapid_click_duration", 20.0)
                self.timer_threshold = settings.get("timer_threshold", 10)
                self.thank_you_message = settings.get("thank_you_message", "Thank you for the dig!")

                # Load coordinates if available
                coordinates = config.get("coordinates", {})
                self.dig_coordinates = coordinates

                # Calculate expected clicks for user info
                expected_clicks = int(self.rapid_click_duration / self.rapid_click_speed)
                self.logger.info(f"Loaded dig config: speed={self.rapid_click_speed}s, duration={self.rapid_click_duration}s, threshold={self.timer_threshold}s")
                self.logger.info(f"Expected clicks: {expected_clicks} clicks over {self.rapid_click_duration} seconds")
                self.logger.info(f"Thank you message: '{self.thank_you_message}'")
            else:
                raise Exception("No configuration found")

        except Exception as e:
            self.logger.error(f"Error loading dig config: {str(e)}")
            # Use updated defaults
            self.rapid_click_speed = 0.005  # 200 clicks per second as requested
            self.rapid_click_duration = 20.0  # 20 seconds
            self.timer_threshold = 10  # Start at 10 seconds
            self.thank_you_message = "Thank you for the dig!"
            self.dig_coordinates = {}

    def _apply_module_specific_config(self, config: Dict[str, Any]):
        """Apply dig-specific configuration from unified config"""
        settings = config.get("settings", {})
        self.rapid_click_speed = settings.get("rapid_click_speed", 0.005)  # Updated default
        self.rapid_click_duration = settings.get("rapid_click_duration", 20.0)
        self.timer_threshold = settings.get("timer_threshold", 10)
        self.thank_you_message = settings.get("thank_you_message", "Thank you for the dig!")

        # Load coordinates if available
        coordinates = config.get("coordinates", {})
        self.dig_coordinates = coordinates

        self.logger.info(f"Applied dig-specific config: speed={self.rapid_click_speed}s, duration={self.rapid_click_duration}s")

    def custom_can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Check if dig templates are detected or dig is in progress"""
        try:
            templates_found = screen_data.get('templates_found', {})

            # If dig is already in progress, continue execution
            if self.dig_in_progress:
                return True

            # Check for any dig trigger templates
            dig_triggers = ['dig_icon', 'dig_up_dropdown', 'test_flight_treasure', 'dig_up_treasure']

            for trigger in dig_triggers:
                if trigger in templates_found:
                    matches = templates_found[trigger]
                    if matches and len(matches) > 0:
                        self.logger.info(f"Dig trigger detected: {trigger} - starting dig sequence")
                        return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking dig availability: {str(e)}")
            return False

    def can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Bridge method for base module compatibility"""
        return self.custom_can_execute(screen_data)

    def custom_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Execute dig sequence based on current step"""
        try:
            if not self.dig_in_progress:
                # Start new dig sequence - Step 1: Detection & Initialization
                self.dig_in_progress = True
                self.current_step = self.STEP_OPEN_CHAT
                self.logger.info("Step 1: Dig trigger detected - starting dig sequence with highest priority")
                self.logger.info("Pausing main scanner until dig completion")

                # Pause main scanner during dig execution (highest priority)
                if hasattr(self, 'controller') and self.controller:
                    self.controller.paused = True

            # Execute current step
            return self._execute_current_step(screen_data)

        except Exception as e:
            self.logger.error(f"Error in dig execution: {str(e)}")
            self._reset_dig_state()
            return False

    def _execute_current_step(self, screen_data: Dict[str, Any]) -> bool:
        """Execute the current step in the dig sequence"""
        try:
            if self.current_step == self.STEP_OPEN_CHAT:
                return self._step_open_chat()
            elif self.current_step == self.STEP_FIND_TREASURE:
                return self._step_find_treasure(screen_data)
            elif self.current_step == self.STEP_VERIFY_CHAT_STATE:
                return self._step_verify_chat_state(screen_data)
            elif self.current_step == self.STEP_DEPLOY_SQUAD:
                return self._step_deploy_squad(screen_data)
            elif self.current_step == self.STEP_TIMER_MANAGEMENT:
                return self._step_timer_management(screen_data)
            elif self.current_step == self.STEP_EXIT_MENU:
                return self._step_exit_menu(screen_data)
            elif self.current_step == self.STEP_SEND_MESSAGE:
                return self._step_send_message()
            elif self.current_step == self.STEP_RETURN_CONTROL:
                return self._step_return_control()
            else:
                self.logger.error(f"Unknown step: {self.current_step}")
                self._reset_dig_state()
                return False

        except Exception as e:
            self.logger.error(f"Error executing step {self.current_step}: {str(e)}")
            self._reset_dig_state()
            return False

    def _step_open_chat(self) -> bool:
        """Step 2: Open Alliance Chat - click (1267,1353) then (1176,170)"""
        try:
            self.logger.info("Step 2: Opening chat and entering alliance chat")

            # Click to open chat at (1267, 1353)
            if not self._safe_click(1267, 1353, "open chat"):
                self.logger.error("Failed to open chat")
                self._emergency_recovery("Failed to open chat")
                return False

            time.sleep(1.0)

            # Click to enter alliance chat at (1176, 170)
            if not self._safe_click(1176, 170, "enter alliance chat"):
                self.logger.error("Failed to enter alliance chat")
                self._emergency_recovery("Failed to enter alliance chat")
                return False

            time.sleep(1.0)

            # Move to next step - keep dig active for centralized scanner to call again
            self.current_step = self.STEP_FIND_TREASURE
            self.logger.info("✅ Step 2 completed - moving to Step 3: Find Treasure")

            # Return True to indicate successful step completion
            return True

        except Exception as e:
            self.logger.error(f"Error opening chat: {str(e)}")
            self._emergency_recovery(f"Step 2 error: {str(e)}")
            return False

    def _step_find_treasure(self, screen_data: Dict[str, Any]) -> bool:
        """Step 3: Find Treasure Link - scan for dig_up_treasure/test_flight_treasure, click or use chat_click backup"""
        try:
            self.logger.info("Step 3: Looking for dig treasure templates")

            # Initialize scan attempt counter if not exists
            if not hasattr(self, 'treasure_scan_attempts'):
                self.treasure_scan_attempts = 0

            # Get fresh screen data by temporarily unpausing scanner
            if hasattr(self, 'controller') and self.controller:
                # Temporarily unpause to get fresh screen data
                self.controller.paused = False
                time.sleep(0.5)  # Allow one scan cycle

                # Get the latest screen data from centralized scanner
                if hasattr(self.controller, 'centralized_scanner') and self.controller.centralized_scanner:
                    fresh_screen_data = self.controller.centralized_scanner.get_current_screen_data()
                    if fresh_screen_data:
                        screen_data = fresh_screen_data
                        self.logger.info("Got fresh screen data from centralized scanner")

                # Re-pause scanner for dig execution
                self.controller.paused = True

            templates_found = screen_data.get('templates_found', {})
            self.treasure_scan_attempts += 1

            self.logger.info(f"Treasure scan attempt {self.treasure_scan_attempts}/10")

            # Try to find dig_up_treasure or test_flight_treasure
            treasure_templates = ['dig_up_treasure', 'test_flight_treasure']

            for template in treasure_templates:
                if template in templates_found:
                    matches = templates_found[template]
                    if matches and len(matches) > 0:
                        # Click on the first match
                        match = matches[0]
                        x, y = match[0] + match[2]//2, match[1] + match[3]//2
                        self.logger.info(f"✅ FOUND {template} at ({x}, {y}) - clicking!")
                        pyautogui.click(x, y)
                        time.sleep(1.0)

                        # Reset scan attempts and move to next step
                        self.treasure_scan_attempts = 0
                        self.current_step = self.STEP_VERIFY_CHAT_STATE
                        self.logger.info("✅ Treasure found - moving to Step 4: Verify Chat State")
                        return True  # Step completed successfully

            # Backup: try chat_click template if treasure templates not found
            if 'chat_click' in templates_found:
                matches = templates_found['chat_click']
                if matches and len(matches) > 0:
                    match = matches[0]
                    x, y = match[0] + match[2]//2, match[1] + match[3]//2
                    self.logger.info(f"Using chat_click backup at ({x}, {y})")
                    pyautogui.click(x, y)
                    time.sleep(1.0)
                    self.treasure_scan_attempts = 0
                    self.current_step = self.STEP_VERIFY_CHAT_STATE
                    self.logger.info("✅ Chat click backup used - moving to Step 4: Verify Chat State")
                    return True  # Step completed successfully

            # If no templates found after 10 attempts, trigger ESC recovery and return control
            if self.treasure_scan_attempts >= 10:
                self.logger.warning("❌ No treasure templates found after 10 scans - triggering ESC recovery")
                self._trigger_esc_recovery()
                self.logger.info("✅ ESC recovery completed - returning control to main scanner")
                self._reset_dig_state()
                return True  # Return control to main scanner

            # Continue scanning - return True to indicate successful scan attempt
            self.logger.info(f"No treasure templates found, continuing scan ({self.treasure_scan_attempts}/10)")
            return True  # Successful scan attempt, will be called again

        except Exception as e:
            self.logger.error(f"Error finding treasure: {str(e)}")
            return False

    def _step_verify_chat_state(self, screen_data: Dict[str, Any]) -> bool:
        """Step 4: Verify Chat State & Navigate - check if chat closed, look for send_to_dig templates, click (1279,646)"""
        try:
            self.logger.info("Step 4: Verifying chat state and looking for send_to_dig templates")
            templates_found = screen_data.get('templates_found', {})

            # Check if alliance_chat_is_on is NOT there (chat closed)
            if 'alliance_chat_is_on' not in templates_found:
                self.logger.info("Chat window closed, looking for send_to_dig templates")

                # Look for send_to_dig templates
                send_templates = ['send_to_dig_1', 'send_to_dig_2', 'send_to_dig_3',
                                'send_to_dig_flight', 'send_to_dig_flight_2']

                template_found = False
                for template in send_templates:
                    if template in templates_found:
                        matches = templates_found[template]
                        if matches and len(matches) > 0:
                            self.logger.info(f"Found {template} template - clicking (1279, 646)")
                            template_found = True
                            break

                # If any send_to_dig template found, click (1279, 646)
                if template_found:
                    pyautogui.click(1279, 646)
                    time.sleep(1.0)

                # Move to next step
                self.current_step = self.STEP_DEPLOY_SQUAD
                return True
            else:
                self.logger.info("Chat still open, waiting...")
                return True  # Stay in this step

        except Exception as e:
            self.logger.error(f"Error verifying chat state: {str(e)}")
            return False

    def _step_deploy_squad(self, screen_data: Dict[str, Any]) -> bool:
        """Step 5: Deploy Squad - click (1279,875) send squad, (1279,1101) confirm, (1279,646) open timer"""
        try:
            self.logger.info("Step 5: Deploying squad to dig location")
            templates_found = screen_data.get('templates_found', {})

            # Look for march_to_dig template first
            if 'march_to_dig' in templates_found:
                matches = templates_found['march_to_dig']
                if matches and len(matches) > 0:
                    match = matches[0]
                    x, y = match[0] + match[2]//2, match[1] + match[3]//2
                    self.logger.info(f"Clicking march_to_dig template at ({x}, {y})")
                    pyautogui.click(x, y)
                    time.sleep(1.0)

            # Look for select_tile template
            if 'select_tile' in templates_found:
                matches = templates_found['select_tile']
                if matches and len(matches) > 0:
                    match = matches[0]
                    x, y = match[0] + match[2]//2, match[1] + match[3]//2
                    self.logger.info(f"Clicking select_tile template at ({x}, {y})")
                    pyautogui.click(x, y)
                    time.sleep(1.0)

            # Step 5 sequence: Send squad, confirm deployment, open timer window
            self.logger.info("Clicking send squad at (1279, 875)")
            pyautogui.click(1279, 875)
            time.sleep(1.5)

            self.logger.info("Clicking confirm deployment at (1279, 1101)")
            pyautogui.click(1279, 1101)
            time.sleep(1.5)

            self.logger.info("Clicking to open timer window at (1279, 646)")
            pyautogui.click(1279, 646)
            time.sleep(1.0)

            # Move to next step
            self.current_step = self.STEP_TIMER_MANAGEMENT
            return True

        except Exception as e:
            self.logger.error(f"Error deploying squad: {str(e)}")
            return False

    def _step_timer_management(self, screen_data: Dict[str, Any]) -> bool:
        """Step 6: Timer Management & Rapid Clicking - detect timer, rapid click (1279,646) when <10s for 20s"""
        try:
            self.logger.info("Step 6: Managing timer and rapid clicking when ready")

            # Detect timer seconds from screen
            timer_seconds = self._detect_timer_seconds(screen_data)

            if timer_seconds is not None and timer_seconds <= self.timer_threshold:
                self.logger.info(f"Timer at {timer_seconds} seconds (threshold: {self.timer_threshold}) - starting rapid clicking")
                self.logger.info(f"Rapid clicking at (1279, 646) with {self.rapid_click_speed}s interval for {self.rapid_click_duration}s")

                # Rapid click for configured duration at the correct coordinates
                start_time = time.time()
                clicks = 0

                while time.time() - start_time < self.rapid_click_duration:
                    # Click at the correct coordinates (1279, 646)
                    pyautogui.click(1279, 646)
                    clicks += 1
                    time.sleep(self.rapid_click_speed)

                self.logger.info(f"Rapid clicking completed - {clicks} clicks in {self.rapid_click_duration} seconds")

                # Move to next step
                self.current_step = self.STEP_EXIT_MENU
                return True
            else:
                # Still waiting for timer
                if timer_seconds is not None:
                    self.logger.info(f"Timer at {timer_seconds} seconds - waiting...")
                else:
                    self.logger.info("Timer not detected - waiting...")
                return True

        except Exception as e:
            self.logger.error(f"Error in timer management: {str(e)}")
            return False

    def _step_exit_menu(self, screen_data: Dict[str, Any]) -> bool:
        """Step 7: Exit to Main Menu - ESC until quit_game_dialog, then ESC once more"""
        try:
            from mouse_override_controller import get_mouse_controller

            self.logger.info("Step 7: Exiting to main menu with ESC sequences")
            templates_found = screen_data.get('templates_found', {})

            # Disable mouse override during ESC sequence
            mouse_controller = get_mouse_controller()
            mouse_controller.disable_mouse_override("Dig module ESC sequence")

            try:
                # Keep pressing ESC until quit_game_dialog appears
                if 'quit_game_dialog' not in templates_found:
                    self.logger.info("Pressing ESC to exit dialogs")
                    pyautogui.press('escape')
                    time.sleep(0.5)
                    return True  # Stay in this step
                else:
                    # quit_game_dialog detected, press ESC once more to dismiss it
                    self.logger.info("quit_game_dialog detected, pressing ESC to dismiss")
                    pyautogui.press('escape')
                    time.sleep(1.0)

                    # Move to next step
                    self.current_step = self.STEP_SEND_MESSAGE
                    return True

            finally:
                # Re-enable mouse override with 3-second cooldown
                mouse_controller.enable_mouse_override("Dig module ESC sequence complete", cooldown=3.0)

        except Exception as e:
            self.logger.error(f"Error exiting to main menu: {str(e)}")
            return False

    def _step_send_message(self) -> bool:
        """Step 8: Send Completion Message - open chat, type message at (1073,1327), press Enter"""
        try:
            self.logger.info("Step 8: Sending completion message in alliance chat")

            # Click to open chat at (1267, 1353)
            if not self._safe_click(1267, 1353, "open chat for message"):
                self.logger.warning("Failed to open chat for message, skipping message step")
                self.current_step = self.STEP_RETURN_CONTROL
                return True  # Continue even if message fails

            time.sleep(1.0)

            # Click to enter alliance chat at (1176, 170)
            if not self._safe_click(1176, 170, "enter alliance chat for message"):
                self.logger.warning("Failed to enter alliance chat for message, skipping message step")
                self.current_step = self.STEP_RETURN_CONTROL
                return True  # Continue even if message fails

            time.sleep(1.0)

            # Click in chat input area at (1073, 1327)
            if not self._safe_click(1073, 1327, "chat input field"):
                self.logger.warning("Failed to click chat input field, skipping message step")
                self.current_step = self.STEP_RETURN_CONTROL
                return True  # Continue even if message fails

            time.sleep(0.5)

            # Type the thank you message
            if not self._safe_type(self.thank_you_message, f"completion message: '{self.thank_you_message}'"):
                self.logger.warning("Failed to type completion message, skipping message step")
                self.current_step = self.STEP_RETURN_CONTROL
                return True  # Continue even if message fails

            time.sleep(0.5)

            # Press Enter to send
            try:
                self.logger.info("Pressing Enter to send message")
                pyautogui.press('enter')
                time.sleep(1.0)
                self.logger.info("Completion message sent successfully")
            except Exception as e:
                self.logger.warning(f"Failed to send message: {str(e)}")

            # Move to next step
            self.current_step = self.STEP_RETURN_CONTROL
            return True

        except Exception as e:
            self.logger.error(f"Error in send message step: {str(e)}")
            # Don't fail the entire dig sequence for message issues
            self.current_step = self.STEP_RETURN_CONTROL
            return True

    def _step_return_control(self) -> bool:
        """Step 9: Return Control - ESC to close, resume main scanner"""
        try:
            self.logger.info("Step 9: Returning control to main scanner")

            # Press ESC to close chat and return to main screen
            self.logger.info("Pressing ESC to close chat")
            pyautogui.press('escape')
            time.sleep(1.0)

            # Reset dig state and resume main scanner
            self.logger.info("Dig sequence completed successfully - resuming main scanner")
            self._reset_dig_state()
            return True

        except Exception as e:
            self.logger.error(f"Error returning control: {str(e)}")
            self._reset_dig_state()
            return False

    def _reset_dig_state(self):
        """Reset dig state and resume main scanner"""
        self.dig_in_progress = False
        self.current_step = self.STEP_DETECT

        # Resume main scanner
        if hasattr(self, 'controller') and self.controller:
            self.controller.paused = False
            self.logger.info("Main scanner resumed")

    def _emergency_recovery(self, error_context: str = ""):
        """Emergency recovery sequence with ESC-based recovery"""
        try:
            from mouse_override_controller import get_mouse_controller

            self.logger.warning(f"Initiating emergency recovery - {error_context}")

            # Disable mouse override during recovery
            mouse_controller = get_mouse_controller()
            mouse_controller.disable_mouse_override("Dig module emergency recovery")

            try:
                # Press ESC multiple times to clear any dialogs
                for i in range(5):
                    self.logger.info(f"Emergency ESC sequence {i+1}/5")
                    pyautogui.press('escape')
                    time.sleep(0.5)

                # Additional recovery clicks to ensure we're in a stable state
                self.logger.info("Clicking safe area to clear focus")
                pyautogui.click(1280, 720)  # Center of screen
                time.sleep(1.0)

            finally:
                # Re-enable mouse override
                mouse_controller.enable_mouse_override("Dig module emergency recovery complete", cooldown=5.0)

            # Reset state
            self._reset_dig_state()
            self.logger.info("Emergency recovery completed")

        except Exception as e:
            self.logger.error(f"Emergency recovery failed: {str(e)}")
            self._reset_dig_state()

    def _trigger_esc_recovery(self):
        """Trigger centralized ESC recovery system"""
        try:
            self.logger.info("🔄 Triggering centralized ESC recovery...")

            # Temporarily unpause scanner to allow ESC recovery
            if hasattr(self, 'controller') and self.controller:
                self.controller.paused = False

                # Trigger ESC recovery through centralized scanner
                if hasattr(self.controller, 'centralized_scanner') and self.controller.centralized_scanner:
                    self.controller.centralized_scanner.trigger_esc_recovery("Dig module - no treasure found")
                    time.sleep(3.0)  # Wait for ESC recovery to complete

                # Re-pause scanner for continued dig execution
                self.controller.paused = True

            self.logger.info("✅ ESC recovery completed")

        except Exception as e:
            self.logger.error(f"Failed to trigger ESC recovery: {str(e)}")

    def _safe_click(self, x: int, y: int, description: str = "", retries: int = 3) -> bool:
        """Safely click with error handling and retries"""
        for attempt in range(retries):
            try:
                if description:
                    self.logger.info(f"Clicking {description} at ({x}, {y}) - attempt {attempt + 1}")
                else:
                    self.logger.info(f"Clicking at ({x}, {y}) - attempt {attempt + 1}")

                pyautogui.click(x, y)
                time.sleep(0.5)  # Brief pause after click
                return True

            except Exception as e:
                self.logger.warning(f"Click attempt {attempt + 1} failed: {str(e)}")
                if attempt < retries - 1:
                    time.sleep(1.0)  # Wait before retry
                else:
                    self.logger.error(f"All click attempts failed for {description}")
                    return False

        return False

    def _safe_type(self, text: str, description: str = "", retries: int = 3) -> bool:
        """Safely type text with error handling and retries"""
        for attempt in range(retries):
            try:
                if description:
                    self.logger.info(f"Typing {description} - attempt {attempt + 1}")
                else:
                    self.logger.info(f"Typing text - attempt {attempt + 1}")

                pyautogui.write(text)
                time.sleep(0.5)  # Brief pause after typing
                return True

            except Exception as e:
                self.logger.warning(f"Type attempt {attempt + 1} failed: {str(e)}")
                if attempt < retries - 1:
                    time.sleep(1.0)  # Wait before retry
                else:
                    self.logger.error(f"All type attempts failed for {description}")
                    return False

        return False

    def _check_step_timeout(self, step_start_time: float, timeout_seconds: float = 30.0) -> bool:
        """Check if current step has timed out"""
        if time.time() - step_start_time > timeout_seconds:
            self.logger.warning(f"Step timeout after {timeout_seconds} seconds")
            return True
        return False

    def _detect_timer_seconds(self, screen_data: Dict[str, Any]) -> int:
        """
        Detect timer seconds from screen using OCR and template detection
        Returns None if timer not detected, otherwise returns seconds
        """
        try:
            import cv2
            import numpy as np
            import pyautogui

            # Define timer detection region (adjust coordinates as needed)
            timer_region = (1200, 600, 200, 100)  # x, y, width, height

            # Capture the timer region
            screenshot = pyautogui.screenshot(region=timer_region)
            img = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            # Convert to grayscale for OCR
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # Apply threshold to get better OCR results
            _, thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)

            # Try to extract text using OCR
            try:
                import pytesseract
                text = pytesseract.image_to_string(thresh, config='--psm 8 -c tessedit_char_whitelist=0123456789:')

                # Parse timer text (format could be MM:SS or SS)
                text = text.strip()
                if ':' in text:
                    parts = text.split(':')
                    if len(parts) == 2:
                        try:
                            minutes = int(parts[0])
                            seconds = int(parts[1])
                            total_seconds = minutes * 60 + seconds
                            self.logger.debug(f"Timer detected: {text} ({total_seconds} seconds)")
                            return total_seconds
                        except ValueError:
                            pass
                else:
                    # Try to parse as seconds only
                    try:
                        seconds = int(text)
                        self.logger.debug(f"Timer detected: {seconds} seconds")
                        return seconds
                    except ValueError:
                        pass

            except ImportError:
                self.logger.warning("pytesseract not available for timer detection")
            except Exception as e:
                self.logger.debug(f"OCR timer detection failed: {str(e)}")

            # Fallback: Check for timer templates if available
            templates_found = screen_data.get('templates_found', {})
            if 'timer_1' in templates_found:
                # If timer template detected, assume it's close to threshold
                self.logger.debug("Timer template detected, assuming near threshold")
                return self.timer_threshold - 1

            return None

        except Exception as e:
            self.logger.debug(f"Timer detection error: {str(e)}")
            return None

    def execute(self, screen_data: Dict[str, Any]) -> bool:
        """Bridge method for base module compatibility"""
        return self.custom_execute(screen_data)

    def get_cooldown(self) -> float:
        """Get cooldown time - no cooldown for dig module"""
        return 0.0

    def get_dig_status(self) -> str:
        """Get current dig status as string"""
        if self.dig_in_progress:
            step_names = {
                self.STEP_DETECT: "Detecting",
                self.STEP_OPEN_CHAT: "Opening Chat",
                self.STEP_FIND_TREASURE: "Finding Treasure",
                self.STEP_VERIFY_CHAT_STATE: "Verifying Chat State",
                self.STEP_DEPLOY_SQUAD: "Deploying Squad",
                self.STEP_TIMER_MANAGEMENT: "Managing Timer",
                self.STEP_EXIT_MENU: "Exiting to Menu",
                self.STEP_SEND_MESSAGE: "Sending Message",
                self.STEP_RETURN_CONTROL: "Returning Control"
            }
            return f"Dig in progress - {step_names.get(self.current_step, 'Unknown Step')}"
        else:
            return "Ready"

    def get_required_templates(self) -> list:
        """Return list of templates required by this module"""
        return [
            'dig_icon', 'dig_up_dropdown', 'test_flight_treasure', 'dig_up_treasure',
            'alliance_chat_is_on', 'send_to_dig_1', 'send_to_dig_2', 'send_to_dig_3',
            'send_to_dig_flight', 'send_to_dig_flight_2', 'march_to_dig', 'select_tile',
            'quit_game_dialog'
        ]

    def set_rapid_click_speed(self, speed: float):
        """Set the rapid click speed (seconds between clicks)"""
        self.rapid_click_speed = max(0.001, min(1.0, speed))  # Clamp between 1ms and 1s
        expected_clicks = int(self.rapid_click_duration / self.rapid_click_speed)
        self.logger.info(f"Rapid click speed set to {self.rapid_click_speed} seconds ({expected_clicks} clicks over {self.rapid_click_duration}s)")

    def set_rapid_click_duration(self, duration: float):
        """Set the rapid click duration (total seconds to click)"""
        self.rapid_click_duration = max(1.0, min(60.0, duration))  # Clamp between 1s and 60s
        expected_clicks = int(self.rapid_click_duration / self.rapid_click_speed)
        self.logger.info(f"Rapid click duration set to {self.rapid_click_duration} seconds ({expected_clicks} total clicks)")

    def set_timer_threshold(self, threshold: int):
        """Set the timer threshold (start clicking when timer <= threshold)"""
        self.timer_threshold = max(1, min(30, threshold))  # Clamp between 1s and 30s
        self.logger.info(f"Timer threshold set to {self.timer_threshold} seconds")

    def set_thank_you_message(self, message: str):
        """Set the thank you message to send after dig completion"""
        self.thank_you_message = message
        self.logger.info(f"Thank you message set to: {message}")

    def get_rapid_click_info(self) -> Dict[str, Any]:
        """Get rapid click configuration information"""
        expected_clicks = int(self.rapid_click_duration / self.rapid_click_speed)
        return {
            'speed': self.rapid_click_speed,
            'duration': self.rapid_click_duration,
            'threshold': self.timer_threshold,
            'expected_clicks': expected_clicks,
            'clicks_per_second': 1.0 / self.rapid_click_speed,
            'message': self.thank_you_message
        }
