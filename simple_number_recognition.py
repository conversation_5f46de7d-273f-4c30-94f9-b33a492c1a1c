#!/usr/bin/env python3
"""
Simple Template-Based Number Recognition System
Much more reliable than complex ML for game number recognition
"""
import cv2
import numpy as np
import os
import json
import pickle
from typing import Optional, Dict, List, Tuple
import hashlib

class SimpleNumberRecognition:
    """Simple template-based number recognition that actually works"""
    
    def __init__(self):
        self.templates = {}  # number -> list of template images
        self.template_file = "number_templates.pkl"
        self.load_templates()
        
    def load_templates(self):
        """Load saved templates"""
        if os.path.exists(self.template_file):
            try:
                with open(self.template_file, 'rb') as f:
                    self.templates = pickle.load(f)
                print(f"Loaded templates for numbers: {list(self.templates.keys())}")
            except Exception as e:
                print(f"Failed to load templates: {e}")
                self.templates = {}
        else:
            self.templates = {}
    
    def save_templates(self):
        """Save templates to file"""
        try:
            with open(self.template_file, 'wb') as f:
                pickle.dump(self.templates, f)
            print(f"Saved templates for {len(self.templates)} numbers")
        except Exception as e:
            print(f"Failed to save templates: {e}")
    
    def add_template(self, image: np.ndarray, number: str) -> bool:
        """Add a template for a specific number"""
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # Clean the image (remove noise, enhance contrast)
            cleaned = self.clean_image(gray)
            
            # Create template hash to avoid duplicates
            template_hash = hashlib.md5(cleaned.tobytes()).hexdigest()
            
            # Initialize number templates if not exists
            if number not in self.templates:
                self.templates[number] = []
            
            # Check for duplicates
            existing_hashes = [t['hash'] for t in self.templates[number]]
            if template_hash not in existing_hashes:
                template_data = {
                    'image': cleaned,
                    'hash': template_hash,
                    'original_shape': image.shape
                }
                self.templates[number].append(template_data)
                self.save_templates()
                print(f"Added template for '{number}' (total: {len(self.templates[number])})")
                return True
            else:
                print(f"Duplicate template for '{number}' ignored")
                return False
                
        except Exception as e:
            print(f"Failed to add template: {e}")
            return False
    
    def clean_image(self, image: np.ndarray) -> np.ndarray:
        """Clean and enhance image for better matching"""
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(image, (3, 3), 0)
        
        # Apply adaptive threshold to get clean binary image
        binary = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # Apply morphological operations to clean up
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    def recognize_number(self, image: np.ndarray) -> Tuple[Optional[str], float]:
        """Recognize number using template matching"""
        if not self.templates:
            return None, 0.0
        
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # Clean the input image
            cleaned_input = self.clean_image(gray)
            
            best_match = None
            best_score = 0.0
            best_number = None
            
            # Try matching against all templates
            for number, templates in self.templates.items():
                for template_data in templates:
                    template = template_data['image']
                    
                    # Try multiple scales
                    for scale in [0.8, 0.9, 1.0, 1.1, 1.2]:
                        # Resize template
                        if scale != 1.0:
                            new_width = int(template.shape[1] * scale)
                            new_height = int(template.shape[0] * scale)
                            if new_width > 0 and new_height > 0:
                                scaled_template = cv2.resize(template, (new_width, new_height))
                            else:
                                continue
                        else:
                            scaled_template = template
                        
                        # Skip if template is larger than input
                        if (scaled_template.shape[0] > cleaned_input.shape[0] or 
                            scaled_template.shape[1] > cleaned_input.shape[1]):
                            continue
                        
                        # Template matching
                        result = cv2.matchTemplate(cleaned_input, scaled_template, cv2.TM_CCOEFF_NORMED)
                        _, max_val, _, _ = cv2.minMaxLoc(result)
                        
                        if max_val > best_score:
                            best_score = max_val
                            best_number = number
                            best_match = (template_data, scale)
            
            # Return result if confidence is high enough
            if best_score > 0.6:  # 60% confidence threshold
                return best_number, best_score
            else:
                return None, best_score
                
        except Exception as e:
            print(f"Recognition failed: {e}")
            return None, 0.0
    
    def get_stats(self) -> Dict:
        """Get statistics about templates"""
        stats = {
            'total_numbers': len(self.templates),
            'total_templates': sum(len(templates) for templates in self.templates.values()),
            'numbers_available': list(self.templates.keys())
        }
        
        for number, templates in self.templates.items():
            stats[f'templates_for_{number}'] = len(templates)
        
        return stats
    
    def clear_templates(self):
        """Clear all templates"""
        self.templates = {}
        if os.path.exists(self.template_file):
            os.remove(self.template_file)
        print("All templates cleared")

# Global instance
simple_recognition = None

def get_simple_recognition():
    """Get singleton instance"""
    global simple_recognition
    if simple_recognition is None:
        simple_recognition = SimpleNumberRecognition()
    return simple_recognition

if __name__ == "__main__":
    # Test the system
    recognizer = SimpleNumberRecognition()
    print("Simple Number Recognition System Ready")
    print(f"Available templates: {recognizer.get_stats()}")
