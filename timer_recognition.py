#!/usr/bin/env python3
"""
Number Recognition System for Last War Automation
Specialized for reading game numbers (maps, timers, etc.) with white numbers on various backgrounds
Uses digit templates and advanced color preprocessing
Primary focus: Map system numbers, expandable to dig module timers later
"""
import cv2
import numpy as np
import os
import json
import pickle
import pytesseract
from typing import Optional, Dict, List, Tuple, Union
import hashlib
import logging

class NumberRecognition:
    """Advanced number recognition system using digit templates and color preprocessing
    Primary use: Map system numbers, expandable to dig module timers"""
    
    def __init__(self):
        self.digit_templates = {}  # digit -> list of template images
        self.template_file = "digit_templates.pkl"
        self.config_file = "number_recognition_config.json"
        
        # Default configuration
        self.config = {
            'template_matching_threshold': 0.7,
            'ocr_psm_mode': 6,  # Single uniform block of text
            'color_preprocessing': True,
            'white_text_threshold': 200,  # Minimum brightness for white text
            'green_background_detection': True,
            'debug_images': False,
            # Double-digit specific settings
            'double_digit_enhancement': True,
            'digit_segmentation': True,
            'multi_scale_templates': True,
            'confidence_boost_double_digits': 0.1,  # Boost confidence for double-digit matches
            'ocr_double_digit_threshold': 0.4,  # Lower threshold for double-digit OCR
            'template_double_digit_threshold': 0.6,  # Lower threshold for double-digit templates
        }
        
        self.load_config()
        self.load_digit_templates()
        
        # Setup logging
        self.logger = logging.getLogger('NumberRecognition')
        
    def load_config(self):
        """Load timer recognition configuration"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                    self.config.update(loaded_config)
                print(f"Timer config loaded: {len(loaded_config)} settings")
            except Exception as e:
                print(f"Failed to load timer config: {e}")
    
    def save_config(self):
        """Save timer recognition configuration"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            print("Timer config saved")
        except Exception as e:
            print(f"Failed to save timer config: {e}")
    
    def load_digit_templates(self):
        """Load saved digit templates"""
        if os.path.exists(self.template_file):
            try:
                with open(self.template_file, 'rb') as f:
                    self.digit_templates = pickle.load(f)
                print(f"Loaded digit templates: {list(self.digit_templates.keys())}")
            except Exception as e:
                print(f"Failed to load digit templates: {e}")
                self.digit_templates = {}
        else:
            self.digit_templates = {}
    
    def save_digit_templates(self):
        """Save digit templates to file"""
        try:
            with open(self.template_file, 'wb') as f:
                pickle.dump(self.digit_templates, f)
            print(f"Saved digit templates for digits: {list(self.digit_templates.keys())}")
        except Exception as e:
            print(f"Failed to save digit templates: {e}")
    
    def preprocess_for_white_numbers(self, image: np.ndarray, enhance_double_digits: bool = True) -> np.ndarray:
        """
        Advanced preprocessing to convert image to white numbers on black background
        Enhanced for double-digit recognition with better digit separation
        """
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Enhanced preprocessing for double-digit scenarios
        if enhance_double_digits and self.config['double_digit_enhancement']:
            # Apply Gaussian blur to reduce noise while preserving digit structure
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)

            # Use adaptive threshold for better digit separation
            adaptive_thresh = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY, 11, 2
            )

            # Invert if needed (we want white text on black background)
            if np.mean(adaptive_thresh) > 127:  # More white than black, invert
                adaptive_thresh = cv2.bitwise_not(adaptive_thresh)

            # Method 1: Enhanced white text extraction
            white_mask = cv2.threshold(gray, self.config['white_text_threshold'] - 20, 255, cv2.THRESH_BINARY)[1]
        else:
            # Standard white text extraction
            white_mask = cv2.threshold(gray, self.config['white_text_threshold'], 255, cv2.THRESH_BINARY)[1]

        # Method 2: Handle green backgrounds specifically
        if self.config['green_background_detection'] and len(image.shape) == 3:
            # Convert to HSV for better color filtering
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

            # Expanded green color range for better detection
            lower_green = np.array([30, 30, 30])
            upper_green = np.array([90, 255, 255])
            green_mask = cv2.inRange(hsv, lower_green, upper_green)

            # Create inverse mask (non-green areas)
            non_green_mask = cv2.bitwise_not(green_mask)

            # Extract white text from non-green areas
            text_on_green = cv2.bitwise_and(white_mask, white_mask, mask=non_green_mask)

            # Combine with direct white extraction
            if enhance_double_digits and self.config['double_digit_enhancement']:
                combined_mask = cv2.bitwise_or(white_mask, text_on_green)
                combined_mask = cv2.bitwise_or(combined_mask, adaptive_thresh)
            else:
                combined_mask = cv2.bitwise_or(white_mask, text_on_green)
        else:
            if enhance_double_digits and self.config['double_digit_enhancement']:
                combined_mask = cv2.bitwise_or(white_mask, adaptive_thresh)
            else:
                combined_mask = white_mask

        # Enhanced cleanup for double-digit scenarios
        if enhance_double_digits and self.config['double_digit_enhancement']:
            # Use smaller kernel to preserve digit separation
            kernel_small = np.ones((1, 1), np.uint8)
            kernel_medium = np.ones((2, 2), np.uint8)

            # Close small gaps within digits
            cleaned = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel_small)
            # Remove small noise
            cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel_small)
            # Final cleanup
            cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_CLOSE, kernel_medium)
        else:
            # Standard cleanup
            kernel = np.ones((2, 2), np.uint8)
            cleaned = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)

        # Create final image: white text on black background
        result = np.zeros_like(gray)
        result[cleaned == 255] = 255

        return result

    def segment_digits(self, image: np.ndarray) -> List[Tuple[np.ndarray, Tuple[int, int, int, int]]]:
        """
        Segment multi-digit image into individual digit regions
        Returns list of (digit_image, bounding_box) tuples
        """
        if not self.config['digit_segmentation']:
            return [(image, (0, 0, image.shape[1], image.shape[0]))]

        try:
            # Preprocess the image
            processed = self.preprocess_for_white_numbers(image, enhance_double_digits=True)

            # Find contours (digit regions)
            contours, _ = cv2.findContours(processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Filter and sort contours by x-coordinate (left to right)
            digit_contours = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                area = cv2.contourArea(contour)

                # Filter by size (remove noise, keep digits)
                if area > 20 and w > 3 and h > 5:  # Minimum digit size
                    digit_contours.append((x, y, w, h, contour))

            # Sort by x-coordinate (left to right)
            digit_contours.sort(key=lambda x: x[0])

            # Extract digit regions
            digit_segments = []
            for x, y, w, h, contour in digit_contours:
                # Add small padding around digit
                padding = 2
                x_start = max(0, x - padding)
                y_start = max(0, y - padding)
                x_end = min(processed.shape[1], x + w + padding)
                y_end = min(processed.shape[0], y + h + padding)

                digit_roi = processed[y_start:y_end, x_start:x_end]
                if digit_roi.size > 0:
                    digit_segments.append((digit_roi, (x_start, y_start, x_end - x_start, y_end - y_start)))

            # If no segments found, return original image
            if not digit_segments:
                return [(processed, (0, 0, processed.shape[1], processed.shape[0]))]

            return digit_segments

        except Exception as e:
            print(f"Digit segmentation failed: {e}")
            # Fallback to original image
            processed = self.preprocess_for_white_numbers(image)
            return [(processed, (0, 0, processed.shape[1], processed.shape[0]))]

    def add_digit_template(self, image: np.ndarray, digit: str) -> bool:
        """Add a template for a specific digit (0-9)"""
        if digit not in '0123456789':
            print(f"Invalid digit: {digit}. Must be 0-9")
            return False
        
        try:
            # Preprocess the image to white on black
            processed = self.preprocess_for_white_numbers(image)
            
            # Create template hash to avoid duplicates
            template_hash = hashlib.md5(processed.tobytes()).hexdigest()
            
            # Initialize digit templates if not exists
            if digit not in self.digit_templates:
                self.digit_templates[digit] = []
            
            # Check for duplicates
            existing_hashes = [t['hash'] for t in self.digit_templates[digit]]
            if template_hash not in existing_hashes:
                template_data = {
                    'image': processed,
                    'hash': template_hash,
                    'original_shape': image.shape,
                    'digit': digit
                }
                self.digit_templates[digit].append(template_data)
                self.save_digit_templates()
                print(f"Added template for digit '{digit}' (total: {len(self.digit_templates[digit])})")
                return True
            else:
                print(f"Duplicate template for digit '{digit}' ignored")
                return False
                
        except Exception as e:
            print(f"Failed to add digit template: {e}")
            return False
    
    def recognize_with_templates(self, image: np.ndarray) -> Tuple[Optional[str], float]:
        """Enhanced template matching for double-digit recognition"""
        if not self.digit_templates:
            return None, 0.0

        try:
            # Method 1: Try digit segmentation for multi-digit numbers
            if self.config['digit_segmentation']:
                digit_segments = self.segment_digits(image)

                if len(digit_segments) > 1:  # Multi-digit detected
                    recognized_digits = []
                    total_confidence = 0.0

                    for digit_image, bbox in digit_segments:
                        best_digit, best_conf = self._match_single_digit(digit_image)
                        if best_digit and best_conf > self.config['template_double_digit_threshold']:
                            recognized_digits.append(best_digit)
                            total_confidence += best_conf
                        else:
                            # If any digit fails, fall back to whole image matching
                            break

                    if len(recognized_digits) == len(digit_segments) and recognized_digits:
                        # All digits recognized successfully
                        result_number = ''.join(recognized_digits)
                        avg_confidence = total_confidence / len(recognized_digits)

                        # Boost confidence for successful double-digit recognition
                        if len(result_number) >= 2:
                            avg_confidence = min(1.0, avg_confidence + self.config['confidence_boost_double_digits'])

                        return result_number, avg_confidence

            # Method 2: Fallback to whole image template matching
            processed_input = self.preprocess_for_white_numbers(image, enhance_double_digits=True)
            best_digit, best_confidence = self._match_single_digit(processed_input)

            return best_digit, best_confidence

        except Exception as e:
            print(f"Template recognition failed: {e}")
            return None, 0.0

    def _match_single_digit(self, digit_image: np.ndarray) -> Tuple[Optional[str], float]:
        """Match a single digit image against templates with enhanced accuracy"""
        best_matches = []

        # For each digit template, find the best match
        for digit, templates in self.digit_templates.items():
            digit_best_score = 0.0

            for template_data in templates:
                template = template_data['image']

                # Skip if template is larger than input
                if (template.shape[0] > digit_image.shape[0] or
                    template.shape[1] > digit_image.shape[1]):
                    continue

                # Enhanced multi-scale template matching
                scales = [0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3] if self.config['multi_scale_templates'] else [0.8, 0.9, 1.0, 1.1, 1.2]

                for scale in scales:
                    if scale != 1.0:
                        new_width = int(template.shape[1] * scale)
                        new_height = int(template.shape[0] * scale)
                        if new_width > 0 and new_height > 0 and new_width <= digit_image.shape[1] and new_height <= digit_image.shape[0]:
                            scaled_template = cv2.resize(template, (new_width, new_height))
                        else:
                            continue
                    else:
                        scaled_template = template

                    # Skip if scaled template is too large
                    if (scaled_template.shape[0] > digit_image.shape[0] or
                        scaled_template.shape[1] > digit_image.shape[1]):
                        continue

                    # Perform template matching with multiple methods
                    methods = [cv2.TM_CCOEFF_NORMED, cv2.TM_CCORR_NORMED]
                    for method in methods:
                        try:
                            result = cv2.matchTemplate(digit_image, scaled_template, method)
                            _, max_val, _, _ = cv2.minMaxLoc(result)

                            # Weight different methods
                            if method == cv2.TM_CCOEFF_NORMED:
                                weighted_score = max_val * 1.0  # Primary method
                            else:
                                weighted_score = max_val * 0.8  # Secondary method

                            if weighted_score > digit_best_score:
                                digit_best_score = weighted_score
                        except:
                            continue

            # Use appropriate threshold
            threshold = self.config['template_double_digit_threshold']
            if digit_best_score > threshold:
                best_matches.append((digit, digit_best_score))

        # Sort by confidence and return the best match
        if best_matches:
            best_matches.sort(key=lambda x: x[1], reverse=True)
            best_digit, best_confidence = best_matches[0]
            return best_digit, best_confidence

        return None, 0.0

    def recognize_with_ocr_psm6(self, image: np.ndarray) -> Tuple[Optional[str], float]:
        """Enhanced OCR PSM6 for double-digit recognition"""
        try:
            # Enhanced preprocessing for double-digit scenarios
            processed = self.preprocess_for_white_numbers(image, enhance_double_digits=True)

            # Try multiple OCR configurations for better double-digit recognition
            ocr_configs = [
                # Primary config: PSM 6 (single uniform block)
                f'--oem 3 --psm {self.config["ocr_psm_mode"]} -c tessedit_char_whitelist=0123456789',
                # Secondary config: PSM 8 (single word) for better digit separation
                f'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789',
                # Tertiary config: PSM 7 (single text line) as fallback
                f'--oem 3 --psm 7 -c tessedit_char_whitelist=0123456789'
            ]

            best_result = None
            best_confidence = 0.0

            for config_idx, custom_config in enumerate(ocr_configs):
                try:
                    # Perform OCR
                    ocr_result = pytesseract.image_to_data(
                        processed,
                        config=custom_config,
                        output_type=pytesseract.Output.DICT
                    )

                    # Extract text and confidence
                    detected_text = ""
                    total_confidence = 0.0
                    valid_detections = 0

                    for i in range(len(ocr_result['text'])):
                        text = ocr_result['text'][i].strip()
                        conf = int(ocr_result['conf'][i])

                        if text and conf > 0:
                            detected_text += text
                            total_confidence += conf
                            valid_detections += 1

                    if detected_text and valid_detections > 0:
                        avg_confidence = total_confidence / valid_detections / 100.0  # Convert to 0-1 range

                        # Apply confidence adjustments for double-digit scenarios
                        if len(detected_text) >= 2:
                            # Boost confidence for successful double-digit detection
                            if config_idx == 0:  # Primary config gets highest boost
                                avg_confidence = min(1.0, avg_confidence + 0.1)
                            elif config_idx == 1:  # Secondary config gets medium boost
                                avg_confidence = min(1.0, avg_confidence + 0.05)

                        # Use appropriate threshold for double-digit numbers
                        threshold = self.config['ocr_double_digit_threshold'] if len(detected_text) >= 2 else 0.5

                        if avg_confidence > best_confidence and avg_confidence > threshold:
                            best_result = detected_text
                            best_confidence = avg_confidence

                            # If we get a high-confidence result, use it immediately
                            if avg_confidence > 0.8:
                                break

                except Exception as config_error:
                    print(f"OCR config {config_idx} failed: {config_error}")
                    continue

            # Additional validation for double-digit results
            if best_result and len(best_result) >= 2:
                # Validate that all characters are digits
                if best_result.isdigit():
                    # Additional confidence boost for valid double-digit numbers
                    best_confidence = min(1.0, best_confidence + 0.05)
                    return best_result, best_confidence
                else:
                    # Filter out non-digit characters
                    filtered_result = ''.join(c for c in best_result if c.isdigit())
                    if filtered_result:
                        return filtered_result, best_confidence * 0.9  # Slight penalty for filtering

            return best_result, best_confidence if best_result else (None, 0.0)

        except Exception as e:
            print(f"OCR PSM6 recognition failed: {e}")
            return None, 0.0
    
    def recognize_number(self, image: np.ndarray) -> Dict:
        """
        Enhanced recognition method with detailed debugging for double-digit scenarios
        Returns comprehensive results for analysis and troubleshooting
        """
        results = {
            'success': False,
            'final_result': None,
            'final_confidence': 0.0,
            'method_used': None,
            'template_result': None,
            'template_confidence': 0.0,
            'ocr_result': None,
            'ocr_confidence': 0.0,
            # Enhanced debugging info
            'image_analysis': {},
            'processing_steps': [],
            'double_digit_detected': False
        }

        try:
            # Analyze input image characteristics
            results['image_analysis'] = self._analyze_image(image)
            results['processing_steps'].append(f"Image analysis: {results['image_analysis']}")

            # Detect if this might be a double-digit scenario
            if results['image_analysis']['width'] > 20 or results['image_analysis']['aspect_ratio'] > 1.5:
                results['double_digit_detected'] = True
                results['processing_steps'].append("Double-digit scenario detected")

            # Try template recognition first
            template_result, template_confidence = self.recognize_with_templates(image)
            results['template_result'] = template_result
            results['template_confidence'] = template_confidence
            results['processing_steps'].append(f"Template result: '{template_result}' (conf: {template_confidence:.3f})")

            # Try OCR PSM6 recognition
            ocr_result, ocr_confidence = self.recognize_with_ocr_psm6(image)
            results['ocr_result'] = ocr_result
            results['ocr_confidence'] = ocr_confidence
            results['processing_steps'].append(f"OCR result: '{ocr_result}' (conf: {ocr_confidence:.3f})")

            # Enhanced decision logic for double-digit scenarios
            template_threshold = self.config['template_double_digit_threshold'] if results['double_digit_detected'] else 0.6
            ocr_threshold = self.config['ocr_double_digit_threshold'] if results['double_digit_detected'] else 0.4

            # Choose the best result with enhanced logic
            if template_result and template_confidence > template_threshold:
                results['final_result'] = template_result
                results['final_confidence'] = template_confidence
                results['method_used'] = 'Template Matching'
                results['success'] = True
                results['processing_steps'].append(f"Selected template result (above threshold {template_threshold})")
            elif ocr_result and ocr_confidence > ocr_threshold:
                results['final_result'] = ocr_result
                results['final_confidence'] = ocr_confidence
                results['method_used'] = 'OCR PSM6'
                results['success'] = True
                results['processing_steps'].append(f"Selected OCR result (above threshold {ocr_threshold})")
            elif template_result and ocr_result:
                # Both methods found something, choose higher confidence
                if template_confidence >= ocr_confidence:
                    results['final_result'] = template_result
                    results['final_confidence'] = template_confidence
                    results['method_used'] = 'Template Matching (Best of Both)'
                else:
                    results['final_result'] = ocr_result
                    results['final_confidence'] = ocr_confidence
                    results['method_used'] = 'OCR PSM6 (Best of Both)'
                results['success'] = True
                results['processing_steps'].append("Selected best result from both methods")
            elif template_result:  # Fallback to template even with lower confidence
                results['final_result'] = template_result
                results['final_confidence'] = template_confidence
                results['method_used'] = 'Template Matching (Low Confidence)'
                results['success'] = True
                results['processing_steps'].append("Fallback to template result")
            elif ocr_result:  # Final fallback to OCR
                results['final_result'] = ocr_result
                results['final_confidence'] = ocr_confidence
                results['method_used'] = 'OCR PSM6 (Low Confidence)'
                results['success'] = True
                results['processing_steps'].append("Fallback to OCR result")
            else:
                results['processing_steps'].append("No valid results from either method")

            # Final validation for double-digit results
            if results['success'] and results['final_result']:
                if len(results['final_result']) >= 2:
                    results['processing_steps'].append(f"Double-digit result validated: '{results['final_result']}'")
                    # Additional confidence boost for successful double-digit recognition
                    results['final_confidence'] = min(1.0, results['final_confidence'] + 0.02)

        except Exception as e:
            results['error'] = str(e)
            results['processing_steps'].append(f"Error occurred: {str(e)}")

        return results

    def _analyze_image(self, image: np.ndarray) -> Dict[str, Any]:
        """Analyze image characteristics for debugging double-digit scenarios"""
        try:
            height, width = image.shape[:2]
            aspect_ratio = width / height if height > 0 else 0

            # Convert to grayscale for analysis
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # Analyze brightness and contrast
            mean_brightness = np.mean(gray)
            brightness_std = np.std(gray)

            # Count potential digit regions using contour analysis
            _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            potential_digits = len([c for c in contours if cv2.contourArea(c) > 10])

            # Estimate if this looks like a double-digit based on width
            likely_double_digit = width > 20 and aspect_ratio > 1.3

            return {
                'width': width,
                'height': height,
                'aspect_ratio': round(aspect_ratio, 2),
                'mean_brightness': round(mean_brightness, 1),
                'brightness_std': round(brightness_std, 1),
                'potential_digit_regions': potential_digits,
                'likely_double_digit': likely_double_digit
            }
        except Exception as e:
            return {'error': str(e)}

    def get_stats(self) -> Dict:
        """Get statistics about the number recognition system"""
        stats = {
            'total_digits': len(self.digit_templates),
            'total_templates': sum(len(templates) for templates in self.digit_templates.values()),
            'available_digits': list(self.digit_templates.keys()),
            'config': self.config.copy()
        }
        
        for digit, templates in self.digit_templates.items():
            stats[f'templates_for_{digit}'] = len(templates)
        
        return stats

# Global instance
number_recognition = None

def get_number_recognition():
    """Get singleton instance"""
    global number_recognition
    if number_recognition is None:
        number_recognition = NumberRecognition()
    return number_recognition

if __name__ == "__main__":
    # Test the system
    recognizer = NumberRecognition()
    print("Number Recognition System Ready (Map System + Future Dig Module)")
    print(f"Stats: {recognizer.get_stats()}")
