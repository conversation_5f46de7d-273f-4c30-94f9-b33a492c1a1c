#!/usr/bin/env python3
"""
Debug test to check why dig ongoing execution is not working after step 2.
This will help identify if the issue is in the dig module or centralized scanner.
"""

import sys
import os
import time
import logging
from unittest.mock import Mock, patch

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dig_ongoing_execution_debug():
    """Debug test for dig ongoing execution"""
    print("🔍 Testing Dig Ongoing Execution - Debug Mode...")
    
    try:
        # Import required modules
        from modules.dig import DigModule
        from unified_config_manager import UnifiedConfigManager
        from centralized_template_scanner import CentralizedTemplateScanner
        
        # Create unified config manager
        config_manager = UnifiedConfigManager()
        
        # Create dig module instance
        dig_module = DigModule(config_manager)
        
        # Create mock controller
        mock_controller = Mock()
        mock_controller.paused = False
        dig_module.controller = mock_controller
        
        print("✅ Dig module created successfully")
        
        # Test 1: Initial state
        print(f"\n📋 Test 1: Initial State")
        print(f"   dig_in_progress: {dig_module.dig_in_progress}")
        print(f"   current_step: {dig_module.current_step}")
        
        # Test 2: Simulate step 2 completion (what happens in your logs)
        print(f"\n📋 Test 2: Simulating Step 2 Completion")
        
        # Set the state as it would be after step 2
        dig_module.dig_in_progress = True
        dig_module.current_step = dig_module.STEP_FIND_TREASURE  # Step 3
        
        print(f"   dig_in_progress: {dig_module.dig_in_progress}")
        print(f"   current_step: {dig_module.current_step}")
        print(f"   STEP_FIND_TREASURE: {dig_module.STEP_FIND_TREASURE}")
        
        # Test 3: Check custom_can_execute
        print(f"\n📋 Test 3: Testing custom_can_execute")
        
        mock_screen_data = {
            'templates_found': {},
            'templates_detected': {}
        }
        
        can_execute = dig_module.custom_can_execute(mock_screen_data)
        print(f"   custom_can_execute result: {can_execute}")
        
        if can_execute:
            print("   ✅ custom_can_execute returns True - this should trigger ongoing execution")
        else:
            print("   ❌ custom_can_execute returns False - this is the problem!")
        
        # Test 4: Test centralized scanner ongoing execution detection
        print(f"\n📋 Test 4: Testing Centralized Scanner Ongoing Detection")
        
        # Create centralized scanner
        scanner = CentralizedTemplateScanner()
        
        # Register the dig module
        scanner.register_module('dig', dig_module)
        
        # Test the ongoing execution detection
        ongoing_result = scanner._check_ongoing_module_execution(mock_screen_data)
        
        print(f"   _check_ongoing_module_execution result: {ongoing_result}")
        
        if ongoing_result:
            print("   ✅ Ongoing execution detected - scanner should continue calling dig module")
        else:
            print("   ❌ Ongoing execution NOT detected - this is why dig stops!")
        
        # Test 5: Check individual flags
        print(f"\n📋 Test 5: Checking Individual Ongoing Flags")
        
        ongoing_flags = ['dig_in_progress', 'in_progress', 'executing', 'active']
        for flag in ongoing_flags:
            if hasattr(dig_module, flag):
                value = getattr(dig_module, flag)
                print(f"   {flag}: {value}")
            else:
                print(f"   {flag}: NOT FOUND")
        
        # Test 6: Manual execution test
        print(f"\n📋 Test 6: Testing Manual Execution")
        
        try:
            # Test if custom_execute would work
            result = dig_module.custom_execute(mock_screen_data)
            print(f"   custom_execute result: {result}")
            print(f"   After execution - current_step: {dig_module.current_step}")
            print(f"   After execution - dig_in_progress: {dig_module.dig_in_progress}")
        except Exception as e:
            print(f"   ❌ custom_execute failed: {e}")
        
        print("\n🎯 Debug Summary:")
        print("   1. Check if dig_in_progress is True after step 2")
        print("   2. Check if custom_can_execute returns True")
        print("   3. Check if centralized scanner detects ongoing execution")
        print("   4. Check if the ongoing execution method is being called")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("DIG ONGOING EXECUTION - DEBUG TEST")
    print("=" * 60)
    
    success = test_dig_ongoing_execution_debug()
    
    print("\n" + "=" * 60)
    if success:
        print("🔍 Debug test completed - check results above")
        print("\n💡 Next steps:")
        print("   1. Check the actual logs for '[ONGOING]' messages")
        print("   2. Verify dig_in_progress state after step 2")
        print("   3. Add debug logging to centralized scanner")
    else:
        print("❌ Debug test failed - check errors above")
    print("=" * 60)
