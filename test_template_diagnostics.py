"""
Test script for Template Detection Diagnostics Interface
"""

import tkinter as tk
from template_diagnostics_interface import TemplateDetectionDiagnostics

def test_diagnostics():
    """Test the template diagnostics interface"""
    print("Testing Template Detection Diagnostics Interface...")
    
    try:
        # Create root window
        root = tk.Tk()
        root.title("Template Diagnostics Test")
        
        # Create diagnostics interface
        diagnostics = TemplateDetectionDiagnostics(root)
        
        # Show the diagnostics window
        diagnostics.show_diagnostics_window()
        
        print("✅ Template Diagnostics Interface launched successfully!")
        print("🔍 Features available:")
        print("   - Template selection and loading")
        print("   - Multi-scale template matching")
        print("   - Feature-based detection (SIFT/ORB)")
        print("   - OCR analysis (Tesseract, EasyOCR, PaddleOCR)")
        print("   - Image preprocessing options")
        print("   - Live detection mode")
        print("   - Comprehensive testing")
        print("   - Performance metrics")
        print("   - Visual comparison")
        print("   - Results export")
        
        # Start the GUI
        root.mainloop()
        
    except Exception as e:
        print(f"❌ Error testing diagnostics interface: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_diagnostics()
