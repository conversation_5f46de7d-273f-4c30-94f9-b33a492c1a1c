#!/usr/bin/env python3
"""
Test script to verify red thumb template priority system is working correctly
"""
import os
import warnings

# Suppress libpng warnings
warnings.filterwarnings("ignore", message=".*libpng warning.*")
warnings.filterwarnings("ignore", message=".*sBIT.*")

def test_red_thumb_priority():
    """Test that Red_thumb.png exists and has priority over old templates"""
    
    print("🔍 Testing Red Thumb Template Priority System")
    print("=" * 50)
    
    # Check template files
    templates_dir = "templates"
    
    # Priority order templates
    red_thumb_templates = [
        ("Red_thumb.png", 0.8),           # User-created correct template (lower threshold)
        ("red_thumb_100x40.png", 0.95),  # Old template (higher threshold to avoid false positives)
        ("red_thumb_80x30.png", 0.95),
        ("red_thumb_50x25.png", 0.95),
        ("red_thumb_30x15.png", 0.95)
    ]
    
    print("📋 Template Priority Order:")
    for i, (template_name, threshold) in enumerate(red_thumb_templates, 1):
        template_path = os.path.join(templates_dir, template_name)
        exists = "✅" if os.path.exists(template_path) else "❌"
        print(f"  {i}. {template_name} (threshold: {threshold}) {exists}")
        
        if os.path.exists(template_path):
            try:
                import cv2
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    template = cv2.imread(template_path)
                if template is not None:
                    h, w = template.shape[:2]
                    print(f"     📐 Dimensions: {w}x{h}")
                else:
                    print(f"     ⚠️ Could not load template")
            except ImportError:
                print(f"     ℹ️ OpenCV not available for dimension check")
    
    print("\n🎯 Expected Behavior:")
    print("  1. Red_thumb.png should be checked FIRST")
    print("  2. If Red_thumb.png matches with confidence ≥ 0.8, it wins")
    print("  3. Old templates need confidence ≥ 0.95 to avoid false positives")
    print("  4. User coordinate (1533, 814) should be first fallback")
    
    print("\n📍 Expected Red Thumb Location:")
    print("  - Correct location: (1533, 814)")
    print("  - False positive location: (470, 473) ← Should be avoided")
    
    print("\n✅ Test completed - restart the application to see the fixes in action!")

if __name__ == "__main__":
    test_red_thumb_priority()
