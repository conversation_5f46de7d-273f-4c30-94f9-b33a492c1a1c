#!/usr/bin/env python3
"""
CRITICAL ISSUES DIAGNOSTIC TOOL
Diagnoses mouse override false positives, memory leaks, and module crashes
"""

import time
import sys
import os
import psutil
import gc
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def diagnose_mouse_override_issues():
    """Diagnose mouse override false positive issues"""
    print("🖱️ MOUSE OVERRIDE DIAGNOSTIC")
    print("=" * 50)
    
    try:
        from mouse_override_controller import get_mouse_controller
        
        controller = get_mouse_controller()
        print("✅ Mouse controller obtained")
        
        # Check current configuration
        print(f"\n📊 MOUSE OVERRIDE CONFIGURATION:")
        print(f"   mouse_detection_enabled: {controller.mouse_detection_enabled}")
        print(f"   mouse_check_interval: {controller.mouse_check_interval}s")
        print(f"   movement_tolerance: {controller.movement_tolerance} pixels")
        print(f"   movement_timeout: {controller.movement_timeout}s")
        print(f"   max_programmatic_movements: {controller.max_programmatic_movements}")
        print(f"   programmatic_movements count: {len(controller.programmatic_movements)}")
        
        # Test mouse detection status
        print(f"\n🧪 MOUSE DETECTION STATUS:")
        if controller.mouse_detection_enabled:
            print("   ⚠️ WARNING: Mouse detection is ENABLED - this may cause false positives")
            print("   💡 RECOMMENDATION: Disable mouse detection until issues are resolved")
        else:
            print("   ✅ GOOD: Mouse detection is DISABLED - no false positives expected")
        
        # Test should_pause_automation multiple times
        print(f"\n🔄 TESTING should_pause_automation() - 10 rapid calls:")
        false_positives = 0
        
        for i in range(10):
            should_pause = controller.should_pause_automation()
            print(f"   Call {i+1}: should_pause = {should_pause}")
            if should_pause:
                false_positives += 1
            time.sleep(0.1)
        
        print(f"\n📊 RESULTS:")
        print(f"   False positives: {false_positives}/10")
        if false_positives == 0:
            print("   ✅ EXCELLENT: No false positives detected")
        else:
            print("   ❌ CRITICAL: False positives detected - mouse override system needs fixing")
        
        # Memory usage of programmatic movements
        movements_memory = sys.getsizeof(controller.programmatic_movements)
        print(f"\n💾 MEMORY USAGE:")
        print(f"   Programmatic movements list: {movements_memory} bytes")
        if movements_memory > 1000:
            print("   ⚠️ WARNING: Programmatic movements using significant memory")
        
        return false_positives == 0
        
    except Exception as e:
        print(f"❌ Mouse override diagnostic failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def diagnose_memory_issues():
    """Diagnose memory leak issues"""
    print("\n💾 MEMORY DIAGNOSTIC")
    print("=" * 40)
    
    try:
        # Get current process memory
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024
        print(f"📊 INITIAL MEMORY USAGE: {initial_memory:.1f}MB")
        
        # Test memory usage with mouse controller
        print(f"\n🧪 TESTING MEMORY USAGE WITH MOUSE CONTROLLER:")
        
        from mouse_override_controller import get_mouse_controller
        controller = get_mouse_controller()
        
        # Simulate heavy usage
        print("   Simulating heavy programmatic movement registration...")
        for i in range(100):
            controller.register_programmatic_movement(i * 10, i * 10)
        
        after_movements_memory = process.memory_info().rss / 1024 / 1024
        print(f"   Memory after 100 movements: {after_movements_memory:.1f}MB")
        print(f"   Memory increase: {after_movements_memory - initial_memory:.1f}MB")
        
        # Test cleanup
        print("   Testing memory cleanup...")
        controller.cleanup_memory()
        
        after_cleanup_memory = process.memory_info().rss / 1024 / 1024
        print(f"   Memory after cleanup: {after_cleanup_memory:.1f}MB")
        print(f"   Memory freed: {after_movements_memory - after_cleanup_memory:.1f}MB")
        
        # Test main controller memory
        print(f"\n🧪 TESTING MAIN CONTROLLER MEMORY:")
        
        from main_controller import MainController
        main_ctrl = MainController()
        
        main_ctrl_memory = process.memory_info().rss / 1024 / 1024
        print(f"   Memory after MainController init: {main_ctrl_memory:.1f}MB")
        print(f"   MainController memory usage: {main_ctrl_memory - after_cleanup_memory:.1f}MB")
        
        # Check memory limits
        print(f"\n📊 MEMORY LIMITS:")
        if hasattr(main_ctrl, 'max_memory_mb'):
            print(f"   Max memory limit: {main_ctrl.max_memory_mb}MB")
            print(f"   Current usage: {main_ctrl_memory:.1f}MB")
            print(f"   Available headroom: {main_ctrl.max_memory_mb - main_ctrl_memory:.1f}MB")
            
            if main_ctrl_memory > main_ctrl.max_memory_mb * 0.8:
                print("   ⚠️ WARNING: Memory usage is high (>80% of limit)")
            else:
                print("   ✅ GOOD: Memory usage is within safe limits")
        
        # Final memory check
        final_memory = process.memory_info().rss / 1024 / 1024
        total_increase = final_memory - initial_memory
        
        print(f"\n📊 FINAL MEMORY ANALYSIS:")
        print(f"   Initial: {initial_memory:.1f}MB")
        print(f"   Final: {final_memory:.1f}MB")
        print(f"   Total increase: {total_increase:.1f}MB")
        
        if total_increase > 50:
            print("   ❌ CRITICAL: Significant memory increase detected")
            return False
        elif total_increase > 20:
            print("   ⚠️ WARNING: Moderate memory increase")
            return True
        else:
            print("   ✅ GOOD: Memory usage is stable")
            return True
        
    except Exception as e:
        print(f"❌ Memory diagnostic failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def diagnose_map_trade_module():
    """Diagnose map trade module compatibility"""
    print("\n🗺️ MAP TRADE MODULE DIAGNOSTIC")
    print("=" * 45)
    
    try:
        from modules.map_trade import MapTradeModule
        
        # Test module initialization
        print("🧪 TESTING MODULE INITIALIZATION:")
        module = MapTradeModule()
        print("   ✅ Map trade module initialized successfully")
        
        # Check if safe_click method exists
        if hasattr(module, '_safe_click'):
            print("   ✅ _safe_click method exists - mouse override integration available")
        else:
            print("   ❌ _safe_click method missing - no mouse override integration")
        
        # Test module configuration
        print(f"\n📊 MODULE CONFIGURATION:")
        print(f"   Module name: {module.name}")
        print(f"   Module enabled: {module.enabled}")
        print(f"   Module priority: {module.priority}")
        
        # Test main controller reference
        if hasattr(module, 'main_controller') and module.main_controller:
            print("   ✅ Main controller reference exists")
            
            # Test programmatic click registration
            if hasattr(module.main_controller, 'register_programmatic_click'):
                print("   ✅ Programmatic click registration available")
            else:
                print("   ❌ Programmatic click registration missing")
        else:
            print("   ⚠️ WARNING: Main controller reference missing")
        
        # Test module execution (dry run)
        print(f"\n🧪 TESTING MODULE EXECUTION (DRY RUN):")
        
        # Create fake screen data
        fake_screen_data = {
            'templates_detected': {'map_trade_button': True},
            'screenshot': None
        }
        
        # Test can_execute
        can_execute = module.can_execute(fake_screen_data)
        print(f"   can_execute() result: {can_execute}")
        
        print("   ✅ Map trade module diagnostic completed")
        return True
        
    except Exception as e:
        print(f"❌ Map trade module diagnostic failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def provide_fix_recommendations():
    """Provide specific fix recommendations based on diagnostics"""
    print("\n🔧 FIX RECOMMENDATIONS")
    print("=" * 40)
    
    print("Based on the diagnostic results, here are the recommended fixes:")
    
    print("\n1. 🖱️ MOUSE OVERRIDE FALSE POSITIVES:")
    print("   ✅ IMPLEMENTED: Mouse detection disabled by default")
    print("   ✅ IMPLEMENTED: Increased mouse check interval to 2 seconds")
    print("   ✅ IMPLEMENTED: Increased movement tolerance to 20 pixels")
    print("   ✅ IMPLEMENTED: Limited programmatic movements list size")
    print("   ✅ IMPLEMENTED: Added memory cleanup for mouse controller")
    
    print("\n2. 💾 MEMORY LEAK PREVENTION:")
    print("   ✅ IMPLEMENTED: Memory cleanup in mouse override controller")
    print("   ✅ IMPLEMENTED: Limited programmatic movements list to 10 items")
    print("   ✅ IMPLEMENTED: Reduced movement timeout to 2 seconds")
    print("   ✅ IMPLEMENTED: Added mouse controller cleanup to main controller")
    
    print("\n3. 🗺️ MAP TRADE MODULE COMPATIBILITY:")
    print("   ✅ IMPLEMENTED: Added _safe_click method to map trade module")
    print("   🔄 TODO: Replace all pyautogui.click() calls with _safe_click()")
    print("   🔄 TODO: Test module execution with new mouse override system")
    
    print("\n4. 🚨 IMMEDIATE ACTIONS NEEDED:")
    print("   1. Restart the automation system to apply changes")
    print("   2. Monitor memory usage - should stay under 500MB")
    print("   3. Check logs for '[MOUSE_OVERRIDE]' messages")
    print("   4. If false positives persist, keep mouse detection disabled")
    print("   5. Test map trade module execution manually")

def run_comprehensive_diagnostic():
    """Run all diagnostics and provide summary"""
    print("🚨 CRITICAL ISSUES COMPREHENSIVE DIAGNOSTIC")
    print("=" * 60)
    print(f"Diagnostic started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all diagnostics
    mouse_result = diagnose_mouse_override_issues()
    memory_result = diagnose_memory_issues()
    module_result = diagnose_map_trade_module()
    
    # Provide recommendations
    provide_fix_recommendations()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    if mouse_result:
        print("✅ Mouse Override: FIXED - No false positives detected")
    else:
        print("❌ Mouse Override: ISSUES DETECTED - Needs attention")
    
    if memory_result:
        print("✅ Memory Usage: STABLE - Within acceptable limits")
    else:
        print("❌ Memory Usage: CRITICAL - Memory leak detected")
    
    if module_result:
        print("✅ Map Trade Module: COMPATIBLE - Ready for testing")
    else:
        print("❌ Map Trade Module: ISSUES DETECTED - Needs fixing")
    
    print("\n" + "=" * 60)
    
    if mouse_result and memory_result and module_result:
        print("🎯 ALL CRITICAL ISSUES RESOLVED!")
        print("\n✅ SYSTEM STATUS: READY FOR PRODUCTION")
        print("\n🚀 NEXT STEPS:")
        print("1. Restart your automation system")
        print("2. Test map trade module execution")
        print("3. Monitor system for 30 minutes")
        print("4. Check memory usage stays under 500MB")
        print("5. Verify no mouse override false positives")
    else:
        print("⚠️ CRITICAL ISSUES REMAIN")
        print("\n❌ SYSTEM STATUS: NEEDS ATTENTION")
        print("\n🔧 REQUIRED ACTIONS:")
        if not mouse_result:
            print("- Fix mouse override false positives")
        if not memory_result:
            print("- Address memory leak issues")
        if not module_result:
            print("- Fix map trade module compatibility")
    
    return mouse_result and memory_result and module_result

if __name__ == "__main__":
    try:
        success = run_comprehensive_diagnostic()
        
        print(f"\nDiagnostic completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\nPress Enter to exit...")
        input()
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n\nDiagnostic interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\nDiagnostic failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
