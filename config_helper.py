#!/usr/bin/env python3
"""
Unified Configuration Helper for Last War Automation System
- Module-specific configuration editing
- Real-time template and coordinate testing
- Unified configuration management
- Live validation and testing
- Integration with main automation system
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import pyautogui
import cv2
import numpy as np
import pytesseract
import os
from datetime import datetime
import threading
import time
from PIL import Image, ImageTk
import keyboard
import json
from unified_config_manager import UnifiedConfigManager

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Create necessary folders
DEBUG_FOLDER = "debug_screenshots"
TEMPLATE_FOLDER = "templates"
COORDS_FOLDER = "coordinates"

# Ensure folders exist
for folder in [DEBUG_FOLDER, TEMPLATE_FOLDER, COORDS_FOLDER]:
    if not os.path.exists(folder):
        os.makedirs(folder)
COORDS_FOLDER = "debug_coords"

for folder in [DEBUG_FOLDER, TEMPLATE_FOLDER, COORDS_FOLDER]:
    if not os.path.exists(folder):
        os.makedirs(folder)

class ConfigHelper:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Last War Automation - Configuration Helper")

        # Store window dimensions for later use - reduced to approximately half size
        self.window_width = 700
        self.window_height = 450

        # Configure window to always stay on top and appear in foreground
        self.root.attributes('-topmost', True)

        # Set initial geometry and position
        self._setup_window_positioning()

        # Ensure window appears immediately and stays focused
        self.root.after(50, self._ensure_foreground)

        # Initialize unified configuration manager
        self.config_manager = UnifiedConfigManager()
        self.config_data = self.config_manager.load_config()

        # Current module being edited
        self.current_module = None
        self.current_template = None
        self.current_coordinate = None

        # UI state
        self.live_tracking = False
        self.testing_mode = False

        # Advanced debugging state (from original debugger)
        self.capture_size = tk.IntVar(value=25)
        self.detection_method = tk.StringVar(value="OCR")
        self.selected_map = tk.StringVar(value="M1")
        self.use_scaling = tk.BooleanVar(value=False)
        self.scale_factor = tk.DoubleVar(value=2.0)
        self.display_zoom = tk.DoubleVar(value=1.0)
        self.invert_colors = tk.BooleanVar(value=False)
        self.ocr_mode = tk.StringVar(value="Standard")
        self.morphology_enabled = tk.BooleanVar(value=False)
        self.morphology_operation = tk.StringVar(value="Erode")
        self.morphology_strength = tk.IntVar(value=1)

        # Fast Map OCR Configuration Variables (AI OCR removed for faster loading)
        self.fast_ocr_enabled = tk.BooleanVar(value=True)
        self.fast_ocr_validation = tk.BooleanVar(value=True)
        self.fast_ocr_debug = tk.BooleanVar(value=False)

        # Template creation state
        self.template_size = tk.IntVar(value=50)
        self.template_capture_mode = False
        self.live_template_preview = False
        self.template_preview_thread = None
        self.captured_image = None

        # Mouse tracking state
        self.capturing_mouse = False
        self.live_mouse_tracking = False
        self.mouse_thread = None
        self.current_mouse_x = tk.StringVar(value="0")
        self.current_mouse_y = tk.StringVar(value="0")
        self.saved_coords = tk.StringVar(value="Click 'Save Coord' to capture")

        # Results storage
        self.last_results = {}
        self.current_images = {}

        # Live scanning state
        self.live_scanning = False
        self.live_thread = None

        # Coordinate management from original debugger
        self.coordinates = {
            'M1': [1079, 833],
            'M2': [1245, 835],
            'M3': [1412, 835],
            'M4': [1577, 833],
            'M5': [1163, 994],
            'M6': [1329, 994],
            'M7': [1495, 994]
        }

        # Initialize UI
        self.setup_ui()

        # Setup keyboard listener for ENTER key
        self.setup_keyboard_listener()

        # Load saved coordinates
        self.load_saved_coordinates()

        # Focus game window if available (but don't resize on startup)
        self.focus_game_window(resize=False)

        # Initialize template combo with all available templates
        self.update_template_combo({})

        # Template preview cache for better performance
        self.template_preview_cache = {}

    def setup_ui(self):
        """Setup the main user interface"""
        # Create main control panel for specialized interfaces
        main_control_frame = ttk.Frame(self.root)
        main_control_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        # Add specialized interface buttons
        ttk.Button(main_control_frame, text="🔧 Dig Module Control Panel",
                  command=self._show_dig_control_panel).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(main_control_frame, text="🎯 Template Diagnostics",
                  command=self._show_template_diagnostics).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(main_control_frame, text="⚙️ Centralized Scanner",
                  command=self._show_centralized_scanner_config).pack(side=tk.LEFT, padx=(0, 10))

        # Create main notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=(5, 10))

        # Create tabs
        self.create_module_config_tab()
        self.create_detection_testing_tab()
        self.create_template_testing_tab()
        self.create_coordinate_capture_tab()
        self.create_template_creator_tab()
        self.create_system_settings_tab()

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Select a module to configure")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def _show_dig_control_panel(self):
        """Show the enhanced dig module control panel with full map config functionality"""
        try:
            from dig_control_panel_enhanced import show_enhanced_dig_control_panel

            # Create and show enhanced dig control panel with visual template testing,
            # coordinate capture with ENTER key, real-time monitoring, and advanced settings
            dig_panel = show_enhanced_dig_control_panel(self.root)

        except ImportError as e:
            messagebox.showerror("Error", f"Enhanced dig control panel not available: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open dig control panel: {str(e)}")

    def _show_template_diagnostics(self):
        """Show template diagnostics interface"""
        try:
            from template_diagnostics_interface import TemplateDetectionDiagnostics

            # Create and show diagnostics window
            diagnostics = TemplateDetectionDiagnostics(self.root)
            diagnostics.show()

        except ImportError as e:
            messagebox.showerror("Error", f"Template diagnostics interface not available: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open template diagnostics: {str(e)}")

    def _show_centralized_scanner_config(self):
        """Show centralized scanner configuration"""
        try:
            from centralized_scanner_gui import CentralizedScannerGUI

            # Create and show centralized scanner GUI
            scanner_gui = CentralizedScannerGUI(self.root)
            scanner_gui.show()

        except ImportError as e:
            messagebox.showerror("Error", f"Centralized scanner GUI not available: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open centralized scanner config: {str(e)}")

    def _load_legacy_modules(self):
        """Load modules from legacy module_configs.json"""
        try:
            import json
            legacy_config_path = "module_configs.json"
            if os.path.exists(legacy_config_path):
                with open(legacy_config_path, 'r') as f:
                    legacy_config = json.load(f)

                # Add legacy modules to the unified config structure
                if 'modules' not in self.config_data:
                    self.config_data['modules'] = {}

                for module_name, module_config in legacy_config.items():
                    if module_name not in self.config_data['modules']:
                        # Convert legacy format to unified format
                        unified_module = self._convert_legacy_module(module_config)
                        self.config_data['modules'][module_name] = unified_module

                        # Add to listbox without suffix - it's just another module now
                        self.module_listbox.insert(tk.END, module_name)

                        # Debug: Print what was converted
                        print(f"  ✅ Converted {module_name}: {len(unified_module.get('coordinates', {}))} coords, {len(unified_module.get('templates', {}))} templates")

                print(f"Loaded {len(legacy_config)} legacy modules from module_configs.json")

        except Exception as e:
            print(f"Error loading legacy modules: {str(e)}")

    def _convert_legacy_module(self, legacy_config):
        """Convert legacy module format to unified format"""
        unified = {
            'enabled': legacy_config.get('enabled', True),
            'priority': legacy_config.get('priority', 0),
            'cooldown': legacy_config.get('cooldown', 0.0),
            'manual_trigger': legacy_config.get('manual_trigger', False),
            'settings': {},
            'templates': {},
            'coordinates': {},
            'scan_regions': {}
        }

        # Convert templates
        for template in legacy_config.get('templates', []):
            template_name = template.get('name', 'unknown')
            unified['templates'][template_name] = {
                'threshold': template.get('threshold', 0.8),
                'required': template.get('required', False),
                'description': template.get('description', ''),
                'scanner_priority': 0,
                'scanner_enabled': True,
                'scanner_action': 'default'
            }

        # Convert coordinates with proper structure
        for coord in legacy_config.get('click_coordinates', []):
            coord_name = coord.get('name', 'unknown')
            unified['coordinates'][coord_name] = {
                'x': coord.get('x', 0),
                'y': coord.get('y', 0),
                'delay': coord.get('delay', 1.0),
                'repeat': coord.get('repeat', 1),
                'use_esc_key': coord.get('use_esc_key', False),
                'description': coord.get('description', ''),
                'enabled': coord.get('enabled', True)
            }

        # Convert scan regions if they exist
        for region in legacy_config.get('scan_regions', []):
            region_name = region.get('name', 'unknown')
            unified['scan_regions'][region_name] = {
                'x': region.get('x', 0),
                'y': region.get('y', 0),
                'width': region.get('width', 100),
                'height': region.get('height', 100),
                'description': region.get('description', '')
            }

        # Convert text patterns to scan regions if they exist
        for i, pattern in enumerate(legacy_config.get('text_patterns', [])):
            region_name = f"text_pattern_{i+1}"
            unified['scan_regions'][region_name] = {
                'x': 0,  # Default values since legacy doesn't have exact coordinates
                'y': 0,
                'width': 200,
                'height': 50,
                'description': f"Text pattern: {pattern.get('text', 'unknown')}"
            }

        # Add module-specific settings
        if 'manual_trigger' in legacy_config:
            unified['settings']['manual_trigger'] = legacy_config['manual_trigger']

        return unified

    def _clear_frame_completely(self, frame):
        """Completely clear a frame without affecting its layout"""
        try:
            # Get all children before destroying them
            children = list(frame.winfo_children())

            # Destroy each child widget recursively
            for widget in children:
                try:
                    # If widget has children, destroy them first
                    if hasattr(widget, 'winfo_children'):
                        for child in list(widget.winfo_children()):
                            try:
                                child.destroy()
                            except:
                                pass
                    widget.destroy()
                except Exception as e:
                    pass

            # Force multiple updates to ensure everything is cleaned up
            frame.update_idletasks()
            frame.update()

            # Verify all children are gone
            remaining = frame.winfo_children()
            if remaining:
                # Try one more time with more aggressive approach
                for widget in remaining:
                    try:
                        widget.pack_forget()
                        widget.grid_forget()
                        widget.place_forget()
                        widget.destroy()
                    except:
                        pass

        except Exception as e:
            pass

    def _recreate_module_frames(self):
        """Completely recreate module detail frames to prevent duplication"""
        try:
            # Remove old tabs completely
            for tab_id in self.module_notebook.tabs():
                self.module_notebook.forget(tab_id)

            # Destroy old frame references if they exist
            if hasattr(self, 'basic_settings_frame'):
                try:
                    self.basic_settings_frame.destroy()
                except:
                    pass
            if hasattr(self, 'templates_frame'):
                try:
                    self.templates_frame.destroy()
                except:
                    pass
            if hasattr(self, 'coordinates_frame'):
                try:
                    self.coordinates_frame.destroy()
                except:
                    pass
            if hasattr(self, 'scan_regions_frame'):
                try:
                    self.scan_regions_frame.destroy()
                except:
                    pass

            # Force update
            self.module_notebook.update_idletasks()

            # Recreate fresh frames
            self.basic_settings_frame = ttk.Frame(self.module_notebook)
            self.module_notebook.add(self.basic_settings_frame, text="Basic Settings")

            self.templates_frame = ttk.Frame(self.module_notebook)
            self.module_notebook.add(self.templates_frame, text="Templates")

            self.coordinates_frame = ttk.Frame(self.module_notebook)
            self.module_notebook.add(self.coordinates_frame, text="Coordinates")

            self.scan_regions_frame = ttk.Frame(self.module_notebook)
            self.module_notebook.add(self.scan_regions_frame, text="Scan Regions")



        except Exception as e:
            print(f"❌ Error recreating frames: {e}")

    def create_module_config_tab(self):
        """Create the module configuration tab"""
        self.module_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.module_frame, text="Module Configuration")

        # Left panel - Module selection
        left_panel = ttk.Frame(self.module_frame)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        ttk.Label(left_panel, text="Modules", font=('Arial', 12, 'bold')).pack(pady=(0, 10))

        # Module listbox
        self.module_listbox = tk.Listbox(left_panel, width=20, height=15)
        self.module_listbox.pack(fill=tk.Y, expand=True)
        self.module_listbox.bind('<<ListboxSelect>>', self.on_module_select)

        # Populate module list from unified config
        for module_name in self.config_data.get('modules', {}):
            self.module_listbox.insert(tk.END, module_name)

        # Also load modules from legacy module_configs.json
        self._load_legacy_modules()

        # Module control buttons
        button_frame = ttk.Frame(left_panel)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="Add Module", command=self.add_module).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="Delete Module", command=self.delete_module).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="Save Config", command=self.save_config).pack(fill=tk.X, pady=(0, 5))

        # Right panel - Module details
        self.right_panel = ttk.Frame(self.module_frame)
        self.right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Module details notebook
        self.module_notebook = ttk.Notebook(self.right_panel)
        self.module_notebook.pack(fill=tk.BOTH, expand=True)

        # Create module detail tabs (will be populated when module is selected)
        self.create_module_detail_tabs()

    def create_module_detail_tabs(self):
        """Create tabs for module details"""
        # Basic Settings tab
        self.basic_settings_frame = ttk.Frame(self.module_notebook)
        self.module_notebook.add(self.basic_settings_frame, text="Basic Settings")

        # Templates tab
        self.templates_frame = ttk.Frame(self.module_notebook)
        self.module_notebook.add(self.templates_frame, text="Templates")

        # Coordinates tab
        self.coordinates_frame = ttk.Frame(self.module_notebook)
        self.module_notebook.add(self.coordinates_frame, text="Coordinates")

        # Scan Regions tab
        self.scan_regions_frame = ttk.Frame(self.module_notebook)
        self.module_notebook.add(self.scan_regions_frame, text="Scan Regions")

    def create_detection_testing_tab(self):
        """Create the advanced detection testing tab (from original debugger)"""
        self.detection_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.detection_frame, text="Detection Testing")

        # Configure detection_frame for grid layout
        self.detection_frame.columnconfigure(0, weight=1)
        self.detection_frame.rowconfigure(0, weight=1)

        # Main frame using grid instead of pack to avoid layout conflicts
        main_frame = ttk.Frame(self.detection_frame, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights for proper layout
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.columnconfigure(2, weight=1)
        main_frame.columnconfigure(3, weight=1)
        main_frame.columnconfigure(4, weight=1)
        main_frame.rowconfigure(3, weight=1)

        # Settings frame
        settings_frame = ttk.LabelFrame(main_frame, text="Detection Settings", padding="10")
        settings_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # Capture size
        ttk.Label(settings_frame, text="Capture Size:").grid(row=0, column=0, sticky=tk.W)
        size_frame = ttk.Frame(settings_frame)
        size_frame.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        for size in [15, 20, 25, 30, 35, 40, 50]:
            ttk.Radiobutton(size_frame, text=f"{size}x{size}", variable=self.capture_size,
                           value=size).pack(side=tk.LEFT, padx=(0, 5))

        # Detection method
        ttk.Label(settings_frame, text="Method:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        method_frame = ttk.Frame(settings_frame)
        method_frame.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Radiobutton(method_frame, text="OCR", variable=self.detection_method,
                       value="OCR").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(method_frame, text="Pixel", variable=self.detection_method,
                       value="Pixel").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(method_frame, text="Both", variable=self.detection_method,
                       value="Both").pack(side=tk.LEFT)

        # Template matching method
        ttk.Label(settings_frame, text="Template Matching:").grid(row=1, column=2, sticky=tk.W, pady=(10, 0), padx=(20, 0))
        template_method_frame = ttk.Frame(settings_frame)
        template_method_frame.grid(row=1, column=3, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        self.template_matching_method = tk.StringVar(value="Multi-Scale")
        ttk.Radiobutton(template_method_frame, text="Exact Size", variable=self.template_matching_method,
                       value="Exact").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(template_method_frame, text="Multi-Scale", variable=self.template_matching_method,
                       value="Multi-Scale").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(template_method_frame, text="Feature-Based", variable=self.template_matching_method,
                       value="Features").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(template_method_frame, text="All Methods", variable=self.template_matching_method,
                       value="All").pack(side=tk.LEFT)

        # Processing options
        ttk.Label(settings_frame, text="Processing:").grid(row=2, column=0, sticky=tk.W, pady=(10, 0))
        processing_frame = ttk.Frame(settings_frame)
        processing_frame.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Checkbutton(processing_frame, text="Scale for OCR", variable=self.use_scaling).pack(side=tk.LEFT)
        ttk.Label(processing_frame, text="Factor:").pack(side=tk.LEFT, padx=(10, 5))
        ttk.Spinbox(processing_frame, from_=1.0, to=5.0, increment=0.5, width=6,
                   textvariable=self.scale_factor).pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(processing_frame, text="Display Zoom:").pack(side=tk.LEFT, padx=(10, 5))
        ttk.Spinbox(processing_frame, from_=1.0, to=10.0, increment=1.0, width=6,
                   textvariable=self.display_zoom).pack(side=tk.LEFT)

        # Color inversion option
        ttk.Label(settings_frame, text="Colors:").grid(row=3, column=0, sticky=tk.W, pady=(10, 0))
        color_frame = ttk.Frame(settings_frame)
        color_frame.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Checkbutton(color_frame, text="Invert White to Black",
                       variable=self.invert_colors).pack(side=tk.LEFT)

        # OCR fine-tuning options
        ttk.Label(settings_frame, text="OCR Mode:").grid(row=4, column=0, sticky=tk.W, pady=(10, 0))
        ocr_frame = ttk.Frame(settings_frame)
        ocr_frame.grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Radiobutton(ocr_frame, text="Standard", variable=self.ocr_mode,
                       value="Standard").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(ocr_frame, text="Thick/Bold", variable=self.ocr_mode,
                       value="Thick").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(ocr_frame, text="Thin/Light", variable=self.ocr_mode,
                       value="Thin").pack(side=tk.LEFT)

        # Morphology options for thick text
        ttk.Label(settings_frame, text="Morphology:").grid(row=5, column=0, sticky=tk.W, pady=(10, 0))
        morph_frame = ttk.Frame(settings_frame)
        morph_frame.grid(row=5, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Checkbutton(morph_frame, text="Enable",
                       variable=self.morphology_enabled).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Radiobutton(morph_frame, text="Erode", variable=self.morphology_operation,
                       value="Erode").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Radiobutton(morph_frame, text="Dilate", variable=self.morphology_operation,
                       value="Dilate").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Radiobutton(morph_frame, text="Open", variable=self.morphology_operation,
                       value="Open").pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(morph_frame, text="Strength:").pack(side=tk.LEFT, padx=(10, 5))
        ttk.Spinbox(morph_frame, from_=1, to=5, width=4,
                   textvariable=self.morphology_strength).pack(side=tk.LEFT)

        # Fast Map OCR Configuration Section (AI OCR removed for faster loading)
        ai_ocr_frame = ttk.LabelFrame(main_frame, text="🚀 OCR Configuration (Fast Loading)", padding="10")
        ai_ocr_frame.grid(row=1, column=0, columnspan=5, sticky=(tk.W, tk.E), padx=(0, 0), pady=(10, 10))

        # Simple info about AI OCR removal
        info_frame = ttk.Frame(ai_ocr_frame)
        info_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(info_frame, text="ℹ️ AI OCR initialization removed for faster Config Helper loading",
                 font=('Arial', 9), foreground='blue').pack(anchor=tk.W)
        ttk.Label(info_frame, text="   Use Fast Map OCR system below for 35x35 pixel map piece detection",
                 font=('Arial', 8), foreground='gray').pack(anchor=tk.W)

        # Fast Map OCR Section (Optimized for Speed)
        number_frame = ttk.LabelFrame(ai_ocr_frame, text="🚀 Fast Map OCR System (M1-M7 Optimized)", padding="10")
        number_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        # Fast OCR controls
        number_controls = ttk.Frame(number_frame)
        number_controls.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(number_controls, text="Test Fast OCR", command=self._test_number_recognition,
                  width=15).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(number_controls, text="Performance Stats", command=self._show_number_stats,
                  width=15).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(number_controls, text="Reset Stats", command=self._reset_ocr_stats,
                  width=12).pack(side=tk.LEFT, padx=(0, 5))

        # Fast OCR status
        self.number_status_label = ttk.Label(number_frame, text="⚡ Fast OCR Ready - Optimized for 35x35 pixel captures", foreground="green")
        self.number_status_label.pack(anchor=tk.W, pady=(0, 5))

        # Fast OCR configuration frame
        number_config_frame = ttk.Frame(number_frame)
        number_config_frame.pack(fill=tk.X, pady=(10, 0))

        # Fast OCR options
        self.number_color_preprocessing = tk.BooleanVar(value=True)
        ttk.Checkbutton(number_config_frame, text="Smart Validation (Reject OCR Errors)",
                       variable=self.number_color_preprocessing).pack(side=tk.LEFT, padx=(0, 10))

        self.number_debug_images = tk.BooleanVar(value=False)
        ttk.Checkbutton(number_config_frame, text="Debug Mode (Verbose Logging)",
                       variable=self.number_debug_images).pack(side=tk.LEFT, padx=(0, 10))

        # Performance info
        perf_info = ttk.Label(number_frame, text="7 OCR configs • Confidence scoring • 35x35 optimized • Targeting 80%+ accuracy",
                             font=('Arial', 8), foreground='gray')
        perf_info.pack(anchor=tk.W, pady=(5, 0))

        # Fast OCR variables
        self.last_captured_image = None

        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # First row of buttons
        button_row1 = ttk.Frame(control_frame)
        button_row1.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_row1, text="Focus Game", command=self.focus_game_window,
                  width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_row1, text="SCAN ALL", command=self.scan_all,
                  width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_row1, text="Live Scan", command=self.toggle_live_scan,
                  width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_row1, text="Save Results", command=self.save_results,
                  width=12).pack(side=tk.LEFT, padx=(0, 5))

        # Second row of buttons
        button_row2 = ttk.Frame(control_frame)
        button_row2.pack(fill=tk.X)

        ttk.Button(button_row2, text="Load Coords", command=self.load_coordinates_file,
                  width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_row2, text="Save Coords", command=self.save_coordinates_file,
                  width=12).pack(side=tk.LEFT)

        # Coordinates frame
        coord_frame = ttk.LabelFrame(main_frame, text="Coordinates", padding="10")
        coord_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        # Map selection
        ttk.Label(coord_frame, text="Selected:").grid(row=0, column=0, sticky=tk.W)
        map_combo = ttk.Combobox(coord_frame, textvariable=self.selected_map,
                                values=list(self.coordinates.keys()), width=10)
        map_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        map_combo.bind('<<ComboboxSelected>>', self.on_map_selected)

        # Add new coordinate
        ttk.Button(coord_frame, text="Add New", command=self.add_new_coordinate,
                  width=10).grid(row=0, column=2, padx=(10, 0))

        # Coordinate adjustment
        ttk.Label(coord_frame, text="X:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.x_var = tk.IntVar(value=self.coordinates['M1'][0])
        x_spin = ttk.Spinbox(coord_frame, from_=0, to=3000, textvariable=self.x_var, width=10)
        x_spin.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Label(coord_frame, text="Y:").grid(row=2, column=0, sticky=tk.W, pady=(5, 0))
        self.y_var = tk.IntVar(value=self.coordinates['M1'][1])
        y_spin = ttk.Spinbox(coord_frame, from_=0, to=2000, textvariable=self.y_var, width=10)
        y_spin.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=(5, 0))

        # Live coordinate updates - no button needed
        self.x_var.trace('w', self.live_update_coordinate)
        self.y_var.trace('w', self.live_update_coordinate)

        # Capture current position button
        ttk.Button(coord_frame, text="Capture Mouse (ENTER)",
                  command=self.start_mouse_capture).grid(row=3, column=0, columnspan=2, pady=(10, 0))

        # Status label for mouse capture
        self.capture_status = ttk.Label(coord_frame, text="", foreground="blue")
        self.capture_status.grid(row=4, column=0, columnspan=2, pady=(5, 0))

        # Results frame
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="10")
        results_frame.grid(row=3, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 5))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

        # Image frame
        image_frame = ttk.LabelFrame(main_frame, text="Captured Image", padding="10")
        image_frame.grid(row=3, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        image_frame.columnconfigure(0, weight=1)
        image_frame.rowconfigure(0, weight=1)

        # Results text area
        self.results_text_detection = tk.Text(results_frame, width=30, height=20, font=('Consolas', 9))
        scrollbar_detection = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text_detection.yview)
        self.results_text_detection.configure(yscrollcommand=scrollbar_detection.set)

        self.results_text_detection.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_detection.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # Image display area
        self.image_label = ttk.Label(image_frame, text="No image captured", anchor="center")
        self.image_label.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Image info
        self.image_info = ttk.Label(image_frame, text="", font=('Consolas', 8))
        self.image_info.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

        # Image buttons
        img_button_frame = ttk.Frame(image_frame)
        img_button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Button(img_button_frame, text="Save as Template",
                  command=self.save_as_template).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(img_button_frame, text="Save Debug",
                  command=self.save_debug_image).pack(side=tk.LEFT)

        # Configure grid weights for coordinate frame
        coord_frame.rowconfigure(6, weight=1)

    def create_template_testing_tab(self):
        """Create the template testing tab with visual template preview"""
        self.template_test_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.template_test_frame, text="Template Testing")

        # Template testing controls
        control_frame = ttk.Frame(self.template_test_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(control_frame, text="Template Testing", font=('Arial', 12, 'bold')).pack()

        # Template selection (top section)
        selection_frame = ttk.Frame(control_frame)
        selection_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(selection_frame, text="Select Template:").pack(anchor=tk.W)
        self.template_var = tk.StringVar()
        self.template_combo = ttk.Combobox(selection_frame, textvariable=self.template_var, state="readonly")
        self.template_combo.pack(fill=tk.X, pady=(0, 10))

        # Bind template selection change to update preview
        self.template_combo.bind('<<ComboboxSelected>>', self.on_template_selection_changed)

        # Template preview (main section - takes most of the space)
        preview_frame = ttk.LabelFrame(self.template_test_frame, text="Template Preview", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # Preview controls
        preview_controls = ttk.Frame(preview_frame)
        preview_controls.pack(fill=tk.X, pady=(0, 10))

        # Zoom controls
        ttk.Label(preview_controls, text="Zoom:").pack(side=tk.LEFT)
        self.preview_zoom = tk.DoubleVar(value=2.0)
        zoom_frame = ttk.Frame(preview_controls)
        zoom_frame.pack(side=tk.LEFT, padx=(5, 0))

        ttk.Button(zoom_frame, text="1x", width=4,
                  command=lambda: self._set_preview_zoom(1.0)).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(zoom_frame, text="2x", width=4,
                  command=lambda: self._set_preview_zoom(2.0)).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(zoom_frame, text="3x", width=4,
                  command=lambda: self._set_preview_zoom(3.0)).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(zoom_frame, text="4x", width=4,
                  command=lambda: self._set_preview_zoom(4.0)).pack(side=tk.LEFT, padx=(0, 2))

        # Fit to window button
        ttk.Button(preview_controls, text="Fit", width=6,
                  command=self._fit_preview_to_window).pack(side=tk.RIGHT)

        # Template preview label (much larger and taller)
        self.template_preview_label = tk.Label(preview_frame, text="No template selected",
                                             width=50, height=35, relief=tk.SUNKEN, bg='white')
        self.template_preview_label.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        # Add right-click context menu for preview
        self._create_preview_context_menu()

        # Template info
        self.template_info_label = tk.Label(preview_frame, text="", font=('Arial', 9), fg='gray')
        self.template_info_label.pack(pady=(5, 0))

        # Test controls
        test_controls = ttk.Frame(control_frame)
        test_controls.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(test_controls, text="Focus Game", command=self.focus_game_window).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(test_controls, text="Test Template", command=self.test_template).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(test_controls, text="Capture Screenshot", command=self.capture_screenshot).pack(side=tk.LEFT, padx=(0, 10))

        # Threshold control
        threshold_frame = ttk.Frame(test_controls)
        threshold_frame.pack(side=tk.LEFT, padx=(20, 0))

        ttk.Label(threshold_frame, text="Threshold:").pack(side=tk.LEFT)
        self.template_threshold_var = tk.DoubleVar(value=0.8)
        threshold_spinbox = ttk.Spinbox(threshold_frame, from_=0.1, to=1.0, increment=0.05,
                                       textvariable=self.template_threshold_var, width=8)
        threshold_spinbox.pack(side=tk.LEFT, padx=(5, 0))

        # Results area
        results_frame = ttk.Frame(self.template_test_frame)
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        ttk.Label(results_frame, text="Test Results", font=('Arial', 10, 'bold')).pack(anchor=tk.W)

        self.results_text = tk.Text(results_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)

        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Initialize template preview cache
        self.template_preview_cache = {}

    def _setup_window_positioning(self):
        """Setup window size and strategic positioning"""
        try:
            # Force window to update and get accurate screen dimensions
            self.root.update_idletasks()

            # Get screen dimensions
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()

            # Position window in center-right of screen to avoid completely obscuring game
            # This assumes game is typically on the left side or center-left
            pos_x = int(screen_width * 0.3)  # 30% from left edge
            pos_y = int((screen_height - self.window_height) / 2)  # Vertically centered

            # Ensure window fits on screen
            if pos_x + self.window_width > screen_width:
                pos_x = screen_width - self.window_width - 50  # 50px margin
            if pos_y + self.window_height > screen_height:
                pos_y = screen_height - self.window_height - 50  # 50px margin

            # Ensure minimum position (not off-screen)
            pos_x = max(0, pos_x)
            pos_y = max(0, pos_y)

            # Set geometry
            geometry_string = f"{self.window_width}x{self.window_height}+{pos_x}+{pos_y}"
            self.root.geometry(geometry_string)

            # Force update to apply geometry
            self.root.update_idletasks()

        except Exception as e:
            print(f"Warning: Could not setup window positioning: {e}")
            # Fallback to default geometry
            self.root.geometry(f"{self.window_width}x{self.window_height}")

    def _ensure_foreground(self):
        """Ensure the Config Helper window appears in the foreground and stays on top"""
        try:
            # Multiple attempts to bring window to foreground
            self.root.lift()
            self.root.attributes('-topmost', True)
            self.root.focus_force()

            # Brief flash to make window visible if it's hidden
            self.root.deiconify()

            # On Windows, additional methods to ensure foreground
            try:
                import ctypes

                # Get window handle
                hwnd = int(self.root.wm_frame(), 16) if hasattr(self.root, 'wm_frame') else None
                if not hwnd:
                    # Alternative method to get window handle
                    hwnd = ctypes.windll.user32.FindWindowW(None, self.root.title())

                if hwnd:
                    # Force window to foreground using Windows API
                    ctypes.windll.user32.SetForegroundWindow(hwnd)
                    ctypes.windll.user32.BringWindowToTop(hwnd)
                    ctypes.windll.user32.ShowWindow(hwnd, 9)  # SW_RESTORE

            except (ImportError, AttributeError, OSError):
                # If Windows-specific methods fail, continue with Tkinter methods
                pass

            # Schedule another check to ensure window stays visible
            self.root.after(500, self._maintain_topmost)

        except Exception as e:
            print(f"Warning: Could not ensure foreground display: {e}")

    def _maintain_topmost(self):
        """Maintain topmost status for a short period to ensure visibility"""
        try:
            # Keep topmost for initial period, then allow normal behavior
            if hasattr(self, '_topmost_counter'):
                self._topmost_counter += 1
            else:
                self._topmost_counter = 1

            # Stay topmost for first 3 seconds, then allow normal window behavior
            if self._topmost_counter < 6:  # 6 * 500ms = 3 seconds
                self.root.attributes('-topmost', True)
                self.root.after(500, self._maintain_topmost)
            else:
                # After initial period, allow user to move window behind others if needed
                # but keep it easily accessible
                self.root.attributes('-topmost', False)

        except Exception as e:
            print(f"Warning: Could not maintain topmost status: {e}")

    def create_coordinate_capture_tab(self):
        """Create the coordinate capture tab"""
        self.coord_capture_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.coord_capture_frame, text="Coordinate Capture")

        # Live tracking controls
        control_frame = ttk.Frame(self.coord_capture_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(control_frame, text="Live Mouse Tracking", font=('Arial', 12, 'bold')).pack()

        # Tracking controls
        tracking_frame = ttk.Frame(control_frame)
        tracking_frame.pack(fill=tk.X, pady=10)

        ttk.Button(tracking_frame, text="Focus Game",
                  command=self.focus_game_window).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(tracking_frame, text="Start Live Tracking",
                  command=self.start_live_mouse_tracking).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(tracking_frame, text="Stop Tracking",
                  command=self.stop_live_mouse_tracking).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(tracking_frame, text="Save Current Coord",
                  command=self.save_current_mouse_coord).pack(side=tk.LEFT)

        # Live coordinates display
        coord_display_frame = ttk.LabelFrame(control_frame, text="Live Mouse Position", padding="10")
        coord_display_frame.pack(fill=tk.X, pady=(10, 0))

        # Current position
        pos_frame = ttk.Frame(coord_display_frame)
        pos_frame.pack(fill=tk.X)

        ttk.Label(pos_frame, text="X:", font=('Arial', 12, 'bold')).pack(side=tk.LEFT)
        ttk.Label(pos_frame, textvariable=self.current_mouse_x,
                 font=('Arial', 14, 'bold'), foreground='blue').pack(side=tk.LEFT, padx=(5, 20))

        ttk.Label(pos_frame, text="Y:", font=('Arial', 12, 'bold')).pack(side=tk.LEFT)
        ttk.Label(pos_frame, textvariable=self.current_mouse_y,
                 font=('Arial', 14, 'bold'), foreground='blue').pack(side=tk.LEFT, padx=(5, 0))

        # Saved coordinates
        saved_frame = ttk.LabelFrame(self.coord_capture_frame, text="Saved Coordinates", padding="10")
        saved_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # Coordinate input/display
        input_frame = ttk.Frame(saved_frame)
        input_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(input_frame, text="Coordinates:").pack(side=tk.LEFT)
        self.coord_entry = ttk.Entry(input_frame, textvariable=self.saved_coords, width=30)
        self.coord_entry.pack(side=tk.LEFT, padx=(10, 10), fill=tk.X, expand=True)

        ttk.Button(input_frame, text="Copy",
                  command=self.copy_coordinates).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(input_frame, text="Clear",
                  command=self.clear_coordinates).pack(side=tk.LEFT)

        # Coordinate history
        history_frame = ttk.Frame(saved_frame)
        history_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(history_frame, text="Coordinate History:").pack(anchor=tk.W)

        self.coord_history = tk.Listbox(history_frame, height=15, font=('Consolas', 10))
        coord_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.coord_history.yview)
        self.coord_history.configure(yscrollcommand=coord_scrollbar.set)

        self.coord_history.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        coord_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # History buttons
        hist_button_frame = ttk.Frame(saved_frame)
        hist_button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(hist_button_frame, text="Use Selected",
                  command=self.use_selected_coord).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(hist_button_frame, text="Delete Selected",
                  command=self.delete_selected_coord).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(hist_button_frame, text="Save History",
                  command=self.save_coord_history).pack(side=tk.LEFT)

    def create_template_creator_tab(self):
        """Create the template creation tab with live preview"""
        self.template_creator_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.template_creator_frame, text="Template Creator")

        main_frame = ttk.Frame(self.template_creator_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Capture controls
        capture_frame = ttk.LabelFrame(main_frame, text="Screen Capture", padding="10")
        capture_frame.pack(fill=tk.X, pady=(0, 10))

        # Capture size options
        size_frame = ttk.Frame(capture_frame)
        size_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(size_frame, text="Capture Size:").pack(side=tk.LEFT)

        for size in [25, 50, 75, 100, 150, 200]:
            ttk.Radiobutton(size_frame, text=f"{size}x{size}", variable=self.template_size,
                           value=size).pack(side=tk.LEFT, padx=(10, 5))

        # Capture buttons
        button_frame = ttk.Frame(capture_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="Focus Game",
                  command=self.focus_game_window).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Capture at Mouse",
                  command=self.capture_at_mouse).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Capture at Coordinate",
                  command=self.capture_at_coordinate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Full Screen Capture",
                  command=self.capture_full_screen).pack(side=tk.LEFT, padx=(0, 10))

        # Template capture mode toggle
        mode_frame = ttk.Frame(capture_frame)
        mode_frame.pack(fill=tk.X, pady=(10, 0))

        self.template_mode_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(mode_frame, text="Live Capture Mode (Press ENTER at mouse position)",
                       variable=self.template_mode_var,
                       command=self.toggle_template_capture_mode).pack(side=tk.LEFT)

        self.template_mode_status = ttk.Label(mode_frame, text="", foreground="blue")
        self.template_mode_status.pack(side=tk.LEFT, padx=(10, 0))

        # Live preview toggle
        preview_mode_frame = ttk.Frame(capture_frame)
        preview_mode_frame.pack(fill=tk.X, pady=(5, 0))

        self.live_preview_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(preview_mode_frame, text="Live Preview Mode (Show what's under mouse)",
                       variable=self.live_preview_var,
                       command=self.toggle_live_template_preview).pack(side=tk.LEFT)

        self.live_preview_status = ttk.Label(preview_mode_frame, text="", foreground="green")
        self.live_preview_status.pack(side=tk.LEFT, padx=(10, 0))

        # Template preview and save
        template_frame = ttk.LabelFrame(main_frame, text="Template Preview", padding="10")
        template_frame.pack(fill=tk.BOTH, expand=True)

        # Preview area
        preview_frame = ttk.Frame(template_frame)
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        self.template_preview = ttk.Label(preview_frame, text="No template captured",
                                         anchor="center", relief="sunken")
        self.template_preview.pack(fill=tk.BOTH, expand=True)

        # Template info
        self.template_info = ttk.Label(template_frame, text="", font=('Consolas', 9))
        self.template_info.pack(fill=tk.X, pady=(0, 10))

        # Save controls
        save_frame = ttk.Frame(template_frame)
        save_frame.pack(fill=tk.X)

        ttk.Label(save_frame, text="Template Name:").pack(side=tk.LEFT)
        self.template_name = tk.StringVar(value="new_template")
        ttk.Entry(save_frame, textvariable=self.template_name, width=20).pack(side=tk.LEFT, padx=(10, 10))

        ttk.Button(save_frame, text="Save to Templates",
                  command=self.save_template_to_folder).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(save_frame, text="Test Template",
                  command=self.test_template_detection).pack(side=tk.LEFT)

    def create_system_settings_tab(self):
        """Create the system settings tab"""
        self.system_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.system_frame, text="System Settings")

        # System settings form
        settings_frame = ttk.LabelFrame(self.system_frame, text="System Configuration", padding=10)
        settings_frame.pack(fill=tk.X, padx=10, pady=10)

        # Get current system settings
        system_settings = self.config_data.get('system_settings', {})

        # Scan interval
        ttk.Label(settings_frame, text="Scan Interval (seconds):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.scan_interval_var = tk.DoubleVar(value=system_settings.get('scan_interval', 0.1))
        scan_interval_spin = ttk.Spinbox(settings_frame, from_=0.01, to=5.0, increment=0.01,
                                       textvariable=self.scan_interval_var, width=10)
        scan_interval_spin.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Templates path
        ttk.Label(settings_frame, text="Templates Path:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.templates_path_var = tk.StringVar(value=system_settings.get('templates_path', 'templates/'))
        ttk.Entry(settings_frame, textvariable=self.templates_path_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Theme
        ttk.Label(settings_frame, text="Theme:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.theme_var = tk.StringVar(value=system_settings.get('theme', 'dark'))
        theme_combo = ttk.Combobox(settings_frame, textvariable=self.theme_var, values=['light', 'dark'], state="readonly", width=10)
        theme_combo.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Save button
        ttk.Button(settings_frame, text="Save System Settings", command=self.save_system_settings).grid(row=3, column=0, columnspan=2, pady=20)

    def on_module_select(self, event):
        """Handle module selection"""
        selection = self.module_listbox.curselection()
        if selection:
            module_name = self.module_listbox.get(selection[0])
            self.current_module = module_name
            self.load_module_details(module_name)
            self.status_var.set(f"Editing module: {module_name}")

    def load_module_details(self, module_name):
        """Load module details into the detail tabs"""
        # Prevent multiple simultaneous loads and duplicate loading of same module
        if hasattr(self, '_loading_module') and self._loading_module:
            print(f"⚠️ Already loading a module, skipping load of {module_name}")
            return

        # Check if we're already showing this module
        if hasattr(self, '_current_loaded_module') and self._current_loaded_module == module_name:
            print(f"ℹ️ Module {module_name} is already loaded, skipping reload")
            return

        self._loading_module = True

        try:
            module_config = self.config_data['modules'].get(module_name, {})

            # COMPLETELY RECREATE frames instead of trying to clear them
            self._recreate_module_frames()

            # Load basic settings
            self.load_basic_settings(module_config)

            # Load templates
            self.load_templates(module_config.get('templates', {}))

            # Load coordinates
            self.load_coordinates(module_config.get('coordinates', {}))

            # Load scan regions
            self.load_scan_regions(module_config.get('scan_regions', {}))

            # Update template combo for testing
            self.update_template_combo(module_config.get('templates', {}))

            # Mark this module as currently loaded
            self._current_loaded_module = module_name

        finally:
            self._loading_module = False

    def load_basic_settings(self, module_config):
        """Load basic module settings"""
        # Clear existing content first
        for widget in self.basic_settings_frame.winfo_children():
            widget.destroy()

        settings_frame = ttk.LabelFrame(self.basic_settings_frame, text="Module Settings", padding=10)
        settings_frame.pack(fill=tk.X, padx=10, pady=10)

        # Enabled checkbox
        self.module_enabled_var = tk.BooleanVar(value=module_config.get('enabled', True))
        ttk.Checkbutton(settings_frame, text="Module Enabled", variable=self.module_enabled_var).grid(row=0, column=0, sticky=tk.W, pady=5)

        # Priority
        ttk.Label(settings_frame, text="Priority:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.module_priority_var = tk.IntVar(value=module_config.get('priority', 0))
        ttk.Spinbox(settings_frame, from_=-10, to=10, textvariable=self.module_priority_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Cooldown
        ttk.Label(settings_frame, text="Cooldown (seconds):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.module_cooldown_var = tk.DoubleVar(value=module_config.get('cooldown', 0.0))
        ttk.Spinbox(settings_frame, from_=0.0, to=3600.0, increment=1.0, textvariable=self.module_cooldown_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Manual trigger
        self.module_manual_var = tk.BooleanVar(value=module_config.get('manual_trigger', False))
        ttk.Checkbutton(settings_frame, text="Manual Trigger Only", variable=self.module_manual_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)

        # Save button
        ttk.Button(settings_frame, text="Save Module Settings", command=self.save_module_settings).grid(row=4, column=0, columnspan=2, pady=20)

    def load_templates(self, templates):
        """Load template configuration"""
        # Clear existing content first
        for widget in self.templates_frame.winfo_children():
            widget.destroy()

        templates_frame = ttk.LabelFrame(self.templates_frame, text="EXECUTION Templates (Module-Specific)", padding=10)
        templates_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add informational note about template types
        info_frame = ttk.Frame(templates_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        info_text = ("ℹ️ EXECUTION Templates: Used by this module during its internal operation.\n"
                    "For TRIGGER templates (centralized scanner), use the Centralized Scanner GUI.")
        info_label = ttk.Label(info_frame, text=info_text, font=('Arial', 9),
                              foreground='blue', wraplength=600)
        info_label.pack(anchor=tk.W)

        # Templates treeview
        columns = ('Name', 'Threshold', 'Required', 'Scanner Priority', 'Scanner Enabled', 'Description')
        self.templates_tree = ttk.Treeview(templates_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.templates_tree.heading(col, text=col)
            self.templates_tree.column(col, width=100)

        # Populate templates
        for template_name, template_config in templates.items():
            values = (
                template_name,
                template_config.get('threshold', 0.8),
                'Yes' if template_config.get('required', False) else 'No',
                template_config.get('scanner_priority', 0),
                'Yes' if template_config.get('scanner_enabled', False) else 'No',
                template_config.get('description', '')
            )
            self.templates_tree.insert('', tk.END, values=values)

        templates_scrollbar = ttk.Scrollbar(templates_frame, orient=tk.VERTICAL, command=self.templates_tree.yview)
        self.templates_tree.configure(yscrollcommand=templates_scrollbar.set)

        self.templates_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        templates_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Template control buttons
        template_buttons = ttk.Frame(templates_frame)
        template_buttons.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(template_buttons, text="Add Template", command=self.add_template).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(template_buttons, text="Edit Template", command=self.edit_template).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(template_buttons, text="Delete Template", command=self.delete_template).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(template_buttons, text="Test Template", command=self.test_selected_template).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(template_buttons, text="Advanced Diagnostics", command=self.open_template_diagnostics).pack(side=tk.LEFT, padx=(0, 10))

    def load_coordinates(self, coordinates):
        """Load coordinate configuration"""
        # Clear existing content first
        for widget in self.coordinates_frame.winfo_children():
            widget.destroy()

        coords_frame = ttk.LabelFrame(self.coordinates_frame, text="Coordinates", padding=10)
        coords_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Coordinates treeview
        columns = ('Name', 'X', 'Y', 'Delay', 'Repeat', 'ESC Key', 'Description')
        self.coords_tree_config = ttk.Treeview(coords_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.coords_tree_config.heading(col, text=col)
            self.coords_tree_config.column(col, width=80)

        # Populate coordinates
        for coord_name, coord_config in coordinates.items():
            values = (
                coord_name,
                coord_config.get('x', 0),
                coord_config.get('y', 0),
                coord_config.get('delay', 1.0),
                coord_config.get('repeat', 1),
                'Yes' if coord_config.get('use_esc_key', False) else 'No',
                coord_config.get('description', '')
            )
            self.coords_tree_config.insert('', tk.END, values=values)

        coords_scrollbar_config = ttk.Scrollbar(coords_frame, orient=tk.VERTICAL, command=self.coords_tree_config.yview)
        self.coords_tree_config.configure(yscrollcommand=coords_scrollbar_config.set)

        self.coords_tree_config.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        coords_scrollbar_config.pack(side=tk.RIGHT, fill=tk.Y)

        # Coordinate control buttons
        coord_buttons = ttk.Frame(coords_frame)
        coord_buttons.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(coord_buttons, text="Add Coordinate", command=self.add_coordinate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(coord_buttons, text="Edit Coordinate", command=self.edit_coordinate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(coord_buttons, text="Delete Coordinate", command=self.delete_coordinate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(coord_buttons, text="Capture Live", command=self.capture_coordinate_live).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(coord_buttons, text="Test Click", command=self.test_selected_coordinate).pack(side=tk.LEFT, padx=(0, 10))

    def load_scan_regions(self, scan_regions):
        """Load scan regions configuration"""
        # Clear existing content first
        for widget in self.scan_regions_frame.winfo_children():
            widget.destroy()

        regions_frame = ttk.LabelFrame(self.scan_regions_frame, text="Scan Regions", padding=10)
        regions_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Scan regions treeview
        columns = ('Name', 'X', 'Y', 'Width', 'Height', 'Description')
        self.regions_tree = ttk.Treeview(regions_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.regions_tree.heading(col, text=col)
            self.regions_tree.column(col, width=80)

        # Populate scan regions
        for region_name, region_config in scan_regions.items():
            values = (
                region_name,
                region_config.get('x', 0),
                region_config.get('y', 0),
                region_config.get('width', 100),
                region_config.get('height', 100),
                region_config.get('description', '')
            )
            self.regions_tree.insert('', tk.END, values=values)

        regions_scrollbar = ttk.Scrollbar(regions_frame, orient=tk.VERTICAL, command=self.regions_tree.yview)
        self.regions_tree.configure(yscrollcommand=regions_scrollbar.set)

        self.regions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        regions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Region control buttons
        region_buttons = ttk.Frame(regions_frame)
        region_buttons.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(region_buttons, text="Add Region", command=self.add_scan_region).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(region_buttons, text="Edit Region", command=self.edit_scan_region).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(region_buttons, text="Delete Region", command=self.delete_scan_region).pack(side=tk.LEFT, padx=(0, 10))

    def update_template_combo(self, templates):
        """Update template combo box for testing - now includes ALL templates from ALL modules"""
        all_templates = {}

        # Get templates from current module (if any)
        if templates:
            for name, config in templates.items():
                all_templates[f"{self.current_module}:{name}"] = config

        # Get templates from ALL modules for comprehensive testing
        for module_name, module_config in self.config_data.get('modules', {}).items():
            module_templates = module_config.get('templates', {})
            for template_name, template_config in module_templates.items():
                key = f"{module_name}:{template_name}"
                if key not in all_templates:  # Avoid duplicates
                    all_templates[key] = template_config

        # Also scan templates folder for any standalone templates
        if os.path.exists(TEMPLATE_FOLDER):
            for filename in os.listdir(TEMPLATE_FOLDER):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif')):
                    template_name = os.path.splitext(filename)[0]
                    key = f"file:{template_name}"
                    if key not in all_templates:
                        all_templates[key] = {'threshold': 0.8, 'description': f'File: {filename}'}

        template_names = list(all_templates.keys())
        self.template_combo['values'] = template_names

        if template_names:
            # Try to keep current selection if it exists
            current_selection = self.template_var.get()
            if current_selection in template_names:
                self.template_combo.set(current_selection)
            else:
                self.template_combo.set(template_names[0])
            # Update preview for the selected template
            self.update_template_preview(self.template_var.get())
        else:
            # Clear preview if no templates
            self.clear_template_preview()

    def on_template_selection_changed(self, event=None):
        """Handle template selection change in combo box"""
        selected_template = self.template_var.get()
        if selected_template:
            self.update_template_preview(selected_template)

    def load_template_preview(self, template_path, zoom_factor=None):
        """Load and cache template preview image with zoom support"""
        try:
            # Check if file exists
            if not os.path.exists(template_path):
                return None

            # Get zoom factor
            if zoom_factor is None:
                zoom_factor = getattr(self, 'preview_zoom', tk.DoubleVar(value=2.0)).get()

            # Get file modification time
            file_mtime = os.path.getmtime(template_path)
            cache_key = f"{template_path}_{file_mtime}_{zoom_factor}"

            # Check if we have a cached version with the same modification time and zoom
            if cache_key in self.template_preview_cache:
                return self.template_preview_cache[cache_key]

            # Remove old cached versions of this file
            old_keys = [k for k in self.template_preview_cache.keys() if k.startswith(f"{template_path}_")]
            for old_key in old_keys:
                del self.template_preview_cache[old_key]

            # Load fresh image
            with Image.open(template_path) as img:
                # Force load to ensure we get current file data
                img.load()

                # Apply zoom factor
                original_width, original_height = img.size
                new_width = int(original_width * zoom_factor)
                new_height = int(original_height * zoom_factor)

                # Resize with high quality
                if zoom_factor > 1.0:
                    # Upscaling - use LANCZOS for better quality
                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                else:
                    # Downscaling - use LANCZOS for better quality
                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

                photo = ImageTk.PhotoImage(img)
                self.template_preview_cache[cache_key] = photo
                return photo

        except Exception as e:
            print(f"Failed to load template preview {template_path}: {e}")
            return None

    def update_template_preview(self, template_name):
        """Update the template preview image - supports module:template and file:template formats"""
        if not template_name:
            self.clear_template_preview()
            return

        # Parse template name format (module:template or file:template)
        actual_template_name = template_name
        if ':' in template_name:
            prefix, actual_template_name = template_name.split(':', 1)

        # Find template file path
        template_path = os.path.join(TEMPLATE_FOLDER, f"{actual_template_name}.png")

        # Try different extensions if .png doesn't exist
        if not os.path.exists(template_path):
            for ext in ['.jpg', '.jpeg', '.bmp', '.gif']:
                alt_path = os.path.join(TEMPLATE_FOLDER, f"{actual_template_name}{ext}")
                if os.path.exists(alt_path):
                    template_path = alt_path
                    break

        if not os.path.exists(template_path):
            self.template_preview_label.config(image='', text="Template file not found")
            self.template_info_label.config(text=f"File: {actual_template_name} (not found)")
            return

        # Load and display preview with zoom
        try:
            zoom_factor = getattr(self, 'preview_zoom', tk.DoubleVar(value=2.0)).get()
            preview_image = self.load_template_preview(template_path, zoom_factor)
            if preview_image:
                self.template_preview_label.config(image=preview_image, text="")
                # Keep a reference to prevent garbage collection
                self.template_preview_label.image = preview_image

                # Update info label with file details and module info
                file_size = os.path.getsize(template_path)
                with Image.open(template_path) as img:
                    width, height = img.size

                # Show module info if available
                module_info = ""
                if ':' in template_name:
                    prefix, _ = template_name.split(':', 1)
                    if prefix != 'file':
                        module_info = f" (Module: {prefix})"
                    else:
                        module_info = " (Standalone file)"

                # Include zoom info
                zoom_info = f" • Zoom: {zoom_factor:.1f}x"
                display_size = f" • Display: {int(width * zoom_factor)}x{int(height * zoom_factor)}"

                self.template_info_label.config(text=f"Original: {width}x{height} ({file_size} bytes){module_info}{zoom_info}{display_size}")
            else:
                self.template_preview_label.config(image='', text="Failed to load preview")
                self.template_info_label.config(text="Preview error")
        except Exception as e:
            print(f"Failed to update template preview: {e}")
            self.template_preview_label.config(image='', text="Preview error")
            self.template_info_label.config(text="Error loading preview")

    def clear_template_preview(self):
        """Clear the template preview"""
        self.template_preview_label.config(image='', text="No template selected")
        self.template_info_label.config(text="")
        # Clear the image reference
        if hasattr(self.template_preview_label, 'image'):
            self.template_preview_label.image = None

    # Event handlers
    def add_module(self):
        """Add a new module"""
        module_name = simpledialog.askstring("Add Module", "Enter module name:")
        if module_name and module_name not in self.config_data['modules']:
            # Create default module structure
            default_module = {
                'enabled': True,
                'priority': 0,
                'cooldown': 0.0,
                'manual_trigger': False,
                'settings': {},
                'templates': {},
                'coordinates': {},
                'scan_regions': {}
            }

            self.config_data['modules'][module_name] = default_module
            self.module_listbox.insert(tk.END, module_name)
            self.status_var.set(f"Added module: {module_name}")

    def delete_module(self):
        """Delete selected module"""
        selection = self.module_listbox.curselection()
        if selection:
            module_name = self.module_listbox.get(selection[0])
            if messagebox.askyesno("Delete Module", f"Are you sure you want to delete module '{module_name}'?"):
                del self.config_data['modules'][module_name]
                self.module_listbox.delete(selection[0])
                self.status_var.set(f"Deleted module: {module_name}")

    def save_config(self):
        """Save the unified configuration"""
        try:
            print(f"💾 Saving configuration...")

            self.config_manager.config_data = self.config_data
            self.config_manager.save_config()
            self.status_var.set("Configuration saved successfully")

            print("✅ Configuration saved successfully")
            messagebox.showinfo("Success", "Configuration saved successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {str(e)}")

    def save_module_settings(self):
        """Save current module settings"""
        if not self.current_module:
            return

        module_config = self.config_data['modules'][self.current_module]
        module_config['enabled'] = self.module_enabled_var.get()
        module_config['priority'] = self.module_priority_var.get()
        module_config['cooldown'] = self.module_cooldown_var.get()
        module_config['manual_trigger'] = self.module_manual_var.get()

        self.status_var.set(f"Saved settings for module: {self.current_module}")

    def save_system_settings(self):
        """Save system settings"""
        system_settings = self.config_data.setdefault('system_settings', {})
        system_settings['scan_interval'] = self.scan_interval_var.get()
        system_settings['templates_path'] = self.templates_path_var.get()
        system_settings['theme'] = self.theme_var.get()

        self.status_var.set("System settings saved")

    # Template management methods
    def add_template(self):
        """Add a new template"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        template_name = simpledialog.askstring("Add Template", "Enter template name:")
        if template_name:
            default_template = {
                'threshold': 0.8,
                'required': False,
                'description': '',
                'scanner_priority': 0,
                'scanner_enabled': False,
                'scanner_action': 'default'
            }

            module_config = self.config_data['modules'][self.current_module]
            module_config.setdefault('templates', {})[template_name] = default_template

            # Refresh the templates display
            self.load_templates(module_config['templates'])
            self.status_var.set(f"Added template: {template_name}")

    def edit_template(self):
        """Edit selected template"""
        selection = self.templates_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to edit")
            return

        # Get selected template
        item = self.templates_tree.item(selection[0])
        template_name = item['values'][0]

        # Open template edit dialog
        self.open_template_edit_dialog(template_name)

    def delete_template(self):
        """Delete selected template"""
        selection = self.templates_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to delete")
            return

        item = self.templates_tree.item(selection[0])
        template_name = item['values'][0]

        if messagebox.askyesno("Delete Template", f"Are you sure you want to delete template '{template_name}'?"):
            module_config = self.config_data['modules'][self.current_module]
            del module_config['templates'][template_name]

            # Refresh the templates display
            self.load_templates(module_config['templates'])
            self.status_var.set(f"Deleted template: {template_name}")

    def test_selected_template(self):
        """Test the selected template"""
        selection = self.templates_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to test")
            return

        item = self.templates_tree.item(selection[0])
        template_name = item['values'][0]

        # Format template name with current module for proper testing
        if self.current_module:
            full_template_name = f"{self.current_module}:{template_name}"
        else:
            full_template_name = template_name

        self.test_template_by_name(full_template_name)

    def open_template_diagnostics(self):
        """Open the advanced template diagnostics interface"""
        try:
            from template_diagnostics_interface import TemplateDetectionDiagnostics

            # Create and show diagnostics window
            diagnostics = TemplateDetectionDiagnostics(self.root)
            diagnostics.show_diagnostics_window()

            # Pre-select template if one is selected in the tree
            selection = self.templates_tree.selection()
            if selection:
                item = self.templates_tree.item(selection[0])
                template_name = item['values'][0]
                diagnostics.template_var.set(template_name)
                diagnostics._load_selected_template()

        except ImportError as e:
            messagebox.showerror("Error", f"Template diagnostics interface not available: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open template diagnostics: {str(e)}")

    def open_template_edit_dialog(self, template_name):
        """Open template editing dialog"""
        module_config = self.config_data['modules'][self.current_module]
        template_config = module_config['templates'][template_name]

        # Create edit dialog
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Edit Template: {template_name}")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # Template settings
        ttk.Label(dialog, text="Threshold:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        threshold_var = tk.DoubleVar(value=template_config.get('threshold', 0.8))
        ttk.Scale(dialog, from_=0.1, to=1.0, variable=threshold_var, orient=tk.HORIZONTAL).grid(row=0, column=1, sticky=tk.EW, padx=10, pady=5)

        ttk.Label(dialog, text="Required:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        required_var = tk.BooleanVar(value=template_config.get('required', False))
        ttk.Checkbutton(dialog, variable=required_var).grid(row=1, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Scanner Priority:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        priority_var = tk.IntVar(value=template_config.get('scanner_priority', 0))
        ttk.Spinbox(dialog, from_=0, to=20, textvariable=priority_var).grid(row=2, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Scanner Enabled:").grid(row=3, column=0, sticky=tk.W, padx=10, pady=5)
        scanner_enabled_var = tk.BooleanVar(value=template_config.get('scanner_enabled', False))
        ttk.Checkbutton(dialog, variable=scanner_enabled_var).grid(row=3, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Description:").grid(row=4, column=0, sticky=tk.W, padx=10, pady=5)
        description_var = tk.StringVar(value=template_config.get('description', ''))
        ttk.Entry(dialog, textvariable=description_var, width=30).grid(row=4, column=1, sticky=tk.EW, padx=10, pady=5)

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)

        def save_template():
            template_config['threshold'] = threshold_var.get()
            template_config['required'] = required_var.get()
            template_config['scanner_priority'] = priority_var.get()
            template_config['scanner_enabled'] = scanner_enabled_var.get()
            template_config['description'] = description_var.get()

            # Refresh the templates display
            self.load_templates(module_config['templates'])
            self.status_var.set(f"Updated template: {template_name}")
            dialog.destroy()

        ttk.Button(button_frame, text="Save", command=save_template).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.LEFT)

        dialog.columnconfigure(1, weight=1)

    def setup_keyboard_listener(self):
        """Setup keyboard listener for ENTER key"""
        def on_enter_pressed(_):
            if self.capturing_mouse:
                self.capture_mouse_position()
            elif self.live_mouse_tracking:
                self.save_current_mouse_coord()
            elif self.template_capture_mode:
                self.capture_template_at_mouse()

        keyboard.on_press_key('enter', on_enter_pressed)

    def load_saved_coordinates(self):
        """Load saved coordinates from file"""
        try:
            coord_file = os.path.join(COORDS_FOLDER, "saved_coordinates.json")
            if os.path.exists(coord_file):
                with open(coord_file, 'r') as f:
                    saved_coords = json.load(f)
                    self.coordinates.update(saved_coords)
        except Exception as e:
            print(f"Could not load saved coordinates: {e}")

    # Advanced detection methods from original debugger
    def detect_number(self, x, y, size, method):
        """Detect number at coordinate using specified method"""
        try:
            # 1. CAPTURE - Always capture at original size
            capture_x = x - size // 2
            capture_y = y - size // 2
            screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
            original_image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            # Store image for training mode
            self.last_captured_image = original_image.copy()

            # 2. PROCESSING IMAGE - Create copy for OCR processing
            processing_image = original_image.copy()

            # Apply scaling for OCR processing if enabled
            if self.use_scaling.get():
                scale = self.scale_factor.get()
                new_width = int(processing_image.shape[1] * scale)
                new_height = int(processing_image.shape[0] * scale)
                processing_image = cv2.resize(processing_image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

            # Apply color inversion for processing if enabled
            if self.invert_colors.get():
                processing_image = cv2.bitwise_not(processing_image)

            # 3. DISPLAY IMAGE - Create copy for display (separate from processing)
            display_image = original_image.copy()

            # Apply color inversion for display if enabled
            if self.invert_colors.get():
                display_image = cv2.bitwise_not(display_image)

            # Apply display zoom (independent of processing scaling)
            zoom = self.display_zoom.get()
            if zoom != 1.0:
                zoom_width = int(display_image.shape[1] * zoom)
                zoom_height = int(display_image.shape[0] * zoom)
                display_image = cv2.resize(display_image, (zoom_width, zoom_height), interpolation=cv2.INTER_NEAREST)

            # Convert processing image to grayscale for analysis
            gray = cv2.cvtColor(processing_image, cv2.COLOR_BGR2GRAY)

            results = {}
            scale_info = f" (OCR scaled {self.scale_factor.get()}x)" if self.use_scaling.get() else ""
            zoom_info = f" (display zoom {self.display_zoom.get()}x)" if self.display_zoom.get() != 1.0 else ""
            invert_info = " (inverted)" if self.invert_colors.get() else ""
            processing_info = f"{scale_info}{invert_info}"
            display_info = f"{zoom_info}{invert_info}"

            if method in ["OCR", "Both"]:
                # Traditional OCR detection with fine-tuning
                ocr_image = gray.copy()

                # Apply morphological operations if enabled
                if self.morphology_enabled.get():
                    strength = self.morphology_strength.get()
                    kernel_size = max(2, strength + 1)  # Kernel size based on strength
                    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (kernel_size, kernel_size))
                    iterations = strength

                    if self.morphology_operation.get() == "Erode":
                        ocr_image = cv2.erode(ocr_image, kernel, iterations=iterations)
                    elif self.morphology_operation.get() == "Dilate":
                        ocr_image = cv2.dilate(ocr_image, kernel, iterations=iterations)
                    elif self.morphology_operation.get() == "Open":
                        ocr_image = cv2.morphologyEx(ocr_image, cv2.MORPH_OPEN, kernel, iterations=iterations)

                # Configure OCR based on text type
                ocr_mode = self.ocr_mode.get()
                if ocr_mode == "Thick":
                    # For thick/bold text - use different PSM and add preprocessing
                    config = '--oem 3 --psm 10 -c tessedit_char_whitelist=0123456789'
                    # Additional preprocessing for thick text
                    _, ocr_image = cv2.threshold(ocr_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                elif ocr_mode == "Thin":
                    # For thin/light text
                    config = '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789'
                    # Enhance thin text
                    ocr_image = cv2.dilate(ocr_image, np.ones((2,2), np.uint8), iterations=1)
                else:
                    # Standard mode
                    config = '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789'

                try:
                    # Get confidence threshold for traditional OCR
                    confidence_threshold = getattr(self, 'traditional_ocr_confidence_threshold', tk.DoubleVar(value=0.7)).get()

                    # Use image_to_data to get confidence information
                    data = pytesseract.image_to_data(ocr_image, config=config, output_type=pytesseract.Output.DICT)

                    # Extract text and confidence
                    confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                    texts = [text for text in data['text'] if text.strip()]

                    mode_info = f" [{ocr_mode}]" if ocr_mode != "Standard" else ""
                    morph_info = f" [{self.morphology_operation.get()}{self.morphology_strength.get()}]" if self.morphology_enabled.get() else ""

                    if texts and confidences:
                        # Get the best text with highest confidence
                        best_text = ""
                        best_confidence = 0
                        for i, text in enumerate(texts):
                            if i < len(confidences) and text.strip().isdigit():
                                conf = confidences[i]
                                if conf > best_confidence:
                                    best_confidence = conf
                                    best_text = text.strip()

                        # Convert confidence from 0-100 to 0-1 scale for comparison
                        confidence_normalized = best_confidence / 100.0

                        if best_text and confidence_normalized >= confidence_threshold:
                            results['Traditional OCR'] = f"{int(best_text)}{processing_info}{mode_info}{morph_info} [Conf: {confidence_normalized:.3f}]"
                        else:
                            results['Traditional OCR'] = f"LOW_CONF: '{best_text}' ({confidence_normalized:.3f} < {confidence_threshold:.3f}){processing_info}{mode_info}{morph_info}"
                    else:
                        # Fallback to simple string extraction
                        text = pytesseract.image_to_string(ocr_image, config=config).strip()
                        if text.isdigit():
                            results['Traditional OCR'] = f"{int(text)}{processing_info}{mode_info}{morph_info} [Conf: N/A]"
                        else:
                            results['Traditional OCR'] = f"FAIL: '{text}'{processing_info}{mode_info}{morph_info}"

                except Exception as e:
                    results['Traditional OCR'] = f"ERROR: {str(e)}{processing_info}"

            # AI OCR Analysis - Disabled for faster loading
            if False:  # AI OCR disabled to prevent slow initialization
                try:
                    # Use original image for AI OCR (before any processing)
                    ai_ocr_result = self.ai_ocr_service.recognize_number(
                        original_image,
                        fast_mode=False,
                        piece_id=f"DETECTION_TEST_{x}_{y}"
                    )

                    # Get detailed information from AI OCR service
                    if hasattr(self.ai_ocr_service, 'last_detection_info'):
                        detection_info = self.ai_ocr_service.last_detection_info
                        confidence = detection_info.get('confidence', 0.0)
                        backend_used = detection_info.get('backend_used', 'Unknown')
                        preprocessing_applied = detection_info.get('preprocessing_applied', 'None')
                        brightness = detection_info.get('brightness', 0.0)

                        ai_info = f" [Backend: {backend_used}, Confidence: {confidence:.3f}, Brightness: {brightness:.1f}, Preprocessing: {preprocessing_applied}]"
                    else:
                        ai_info = f" [Confidence: N/A]"

                    if ai_ocr_result is not None:
                        results['AI OCR'] = f"{ai_ocr_result}{ai_info}"
                    else:
                        results['AI OCR'] = f"FAIL: No detection{ai_info}"

                except Exception as e:
                    results['AI OCR'] = f"ERROR: {str(e)}"

            if method in ["Pixel", "Both"]:
                # Pixel analysis
                _, binary = cv2.threshold(gray, 100, 255, cv2.THRESH_BINARY_INV)
                dark_pixels = cv2.countNonZero(binary)
                total_pixels = gray.shape[0] * gray.shape[1]
                density = dark_pixels / total_pixels if total_pixels > 0 else 0

                if density < 0.02:
                    pixel_result = 1
                elif density < 0.04:
                    pixel_result = 2
                elif density < 0.06:
                    pixel_result = 3
                elif density < 0.08:
                    pixel_result = 4
                elif density < 0.10:
                    pixel_result = 5
                elif density < 0.12:
                    pixel_result = 6
                else:
                    pixel_result = 7

                results['Pixel'] = f"{pixel_result} (d={density:.3f}){processing_info}"

            # Save debug image (save the processing image to see what OCR analyzed)
            timestamp = datetime.now().strftime("%H%M%S")
            scale_suffix = f"_s{self.scale_factor.get()}" if self.use_scaling.get() else ""
            invert_suffix = "_inv" if self.invert_colors.get() else ""
            ocr_suffix = f"_{self.ocr_mode.get().lower()}" if self.ocr_mode.get() != "Standard" else ""
            morph_suffix = f"_{self.morphology_operation.get().lower()}{self.morphology_strength.get()}" if self.morphology_enabled.get() else ""
            filename = f"{DEBUG_FOLDER}/debug_{x}_{y}_{size}{scale_suffix}{invert_suffix}{ocr_suffix}{morph_suffix}_{timestamp}.png"
            cv2.imwrite(filename, processing_image)

            # Store current images for template saving
            self.current_images[f"{x}_{y}_{size}"] = processing_image

            # Display image in GUI (show the display image with zoom)
            zoom_text = f" (zoom {self.display_zoom.get()}x)" if self.display_zoom.get() != 1.0 else ""
            info_text = f"Coord: ({x}, {y})\nOriginal: {size}x{size}{zoom_text}"
            self.display_image(display_image, info_text)

            return results, filename

        except Exception as e:
            return {"Error": str(e)}, None

    def display_image(self, image_array, info_text=""):
        """Display captured image in GUI"""
        try:
            # Resize image for display (scale up for better visibility)
            height, width = image_array.shape[:2]
            scale = min(200 // width, 200 // height, 8)  # Max 200px, max 8x scale
            new_width = width * scale
            new_height = height * scale

            resized = cv2.resize(image_array, (new_width, new_height), interpolation=cv2.INTER_NEAREST)

            # Convert to PIL Image
            if len(resized.shape) == 3:
                resized_rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
                pil_image = Image.fromarray(resized_rgb)
            else:
                pil_image = Image.fromarray(resized)

            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(pil_image)

            # Update label
            self.image_label.config(image=photo, text="")
            self.image_label.image = photo  # Keep reference

            # Update info
            self.image_info.config(text=f"Size: {width}x{height} (scaled {scale}x)\n{info_text}")

        except Exception as e:
            self.image_label.config(text=f"Image error: {e}")
            self.image_info.config(text="")

    # Coordinate management methods
    def add_coordinate(self):
        """Add a new coordinate"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        coord_name = simpledialog.askstring("Add Coordinate", "Enter coordinate name:")
        if coord_name:
            default_coord = {
                'x': 0,
                'y': 0,
                'delay': 1.0,
                'repeat': 1,
                'use_esc_key': False,
                'description': ''
            }

            module_config = self.config_data['modules'][self.current_module]
            module_config.setdefault('coordinates', {})[coord_name] = default_coord

            # Refresh the coordinates display
            self.load_coordinates(module_config['coordinates'])
            self.status_var.set(f"Added coordinate: {coord_name}")

    def edit_coordinate(self):
        """Edit selected coordinate"""
        selection = self.coords_tree_config.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a coordinate to edit")
            return

        item = self.coords_tree_config.item(selection[0])
        coord_name = item['values'][0]
        self.open_coordinate_edit_dialog(coord_name)

    def delete_coordinate(self):
        """Delete selected coordinate"""
        selection = self.coords_tree_config.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a coordinate to delete")
            return

        item = self.coords_tree_config.item(selection[0])
        coord_name = item['values'][0]

        if messagebox.askyesno("Delete Coordinate", f"Are you sure you want to delete coordinate '{coord_name}'?"):
            module_config = self.config_data['modules'][self.current_module]
            del module_config['coordinates'][coord_name]

            # Refresh the coordinates display
            self.load_coordinates(module_config['coordinates'])
            self.status_var.set(f"Deleted coordinate: {coord_name}")

    def capture_coordinate_live(self):
        """Capture coordinate from live mouse position"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        coord_name = simpledialog.askstring("Capture Coordinate", "Enter coordinate name:")
        if coord_name:
            # Get current mouse position
            x, y = pyautogui.position()

            new_coord = {
                'x': x,
                'y': y,
                'delay': 1.0,
                'repeat': 1,
                'use_esc_key': False,
                'description': f'Captured at {datetime.now().strftime("%H:%M:%S")}'
            }

            module_config = self.config_data['modules'][self.current_module]
            module_config.setdefault('coordinates', {})[coord_name] = new_coord

            # Refresh the coordinates display
            self.load_coordinates(module_config['coordinates'])
            self.status_var.set(f"Captured coordinate: {coord_name} at ({x}, {y})")

    def test_selected_coordinate(self):
        """Test the selected coordinate by performing a click"""
        selection = self.coords_tree_config.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a coordinate to test")
            return

        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        item = self.coords_tree_config.item(selection[0])
        coord_name = item['values'][0]

        try:
            # Get coordinate data
            module_config = self.config_data['modules'][self.current_module]
            coord_config = module_config['coordinates'][coord_name]

            x = coord_config.get('x', 0)
            y = coord_config.get('y', 0)
            delay = coord_config.get('delay', 1.0)

            # Focus game window first
            self.focus_game_window()
            time.sleep(0.5)  # Brief pause after focusing

            # Perform the click
            pyautogui.click(x, y)

            # Update status without popup
            self.status_var.set(f"✅ Tested coordinate: {coord_name} at ({x}, {y})")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to test coordinate: {str(e)}")
            self.status_var.set(f"Error testing coordinate: {coord_name}")

    def open_coordinate_edit_dialog(self, coord_name):
        """Open coordinate editing dialog"""
        module_config = self.config_data['modules'][self.current_module]
        coord_config = module_config['coordinates'][coord_name]

        # Create edit dialog
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Edit Coordinate: {coord_name}")
        dialog.geometry("500x350")
        dialog.transient(self.root)
        dialog.grab_set()

        # Live mouse tracking variables
        live_tracking = tk.BooleanVar(value=False)
        mouse_thread = None

        # Coordinate settings
        coord_frame = ttk.LabelFrame(dialog, text="Coordinate Settings", padding=10)
        coord_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=10, pady=5)

        ttk.Label(coord_frame, text="X Position:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        x_var = tk.IntVar(value=coord_config.get('x', 0))
        x_spinbox = ttk.Spinbox(coord_frame, from_=0, to=3000, textvariable=x_var, width=10)
        x_spinbox.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(coord_frame, text="Y Position:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        y_var = tk.IntVar(value=coord_config.get('y', 0))
        y_spinbox = ttk.Spinbox(coord_frame, from_=0, to=2000, textvariable=y_var, width=10)
        y_spinbox.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Live mouse capture section
        capture_frame = ttk.LabelFrame(dialog, text="Live Mouse Capture", padding=10)
        capture_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=10, pady=5)

        # Current mouse position display
        current_pos_label = ttk.Label(capture_frame, text="Current Mouse Position: (0, 0)",
                                     font=('Consolas', 10, 'bold'), foreground='blue')
        current_pos_label.grid(row=0, column=0, columnspan=2, pady=5)

        # Live tracking toggle
        def toggle_live_tracking():
            nonlocal mouse_thread
            if not live_tracking.get():
                # Start live tracking
                live_tracking.set(True)
                start_live_mouse_tracking()
                track_button.config(text="Stop Tracking")
                status_label.config(text="🎯 Live tracking ON - Press ENTER to capture coordinates", foreground="green")
            else:
                # Stop live tracking
                live_tracking.set(False)
                stop_live_mouse_tracking()
                track_button.config(text="Start Live Tracking")
                status_label.config(text="Live tracking OFF", foreground="gray")

        def start_live_mouse_tracking():
            nonlocal mouse_thread
            if mouse_thread and mouse_thread.is_alive():
                return

            def track_mouse():
                while live_tracking.get():
                    try:
                        mouse_x, mouse_y = pyautogui.position()

                        # Update the display in the main thread
                        def update_label():
                            if current_pos_label.winfo_exists():
                                current_pos_label.config(text=f"Current Mouse Position: ({mouse_x}, {mouse_y})")

                        dialog.after(0, update_label)
                        time.sleep(0.05)  # Update 20 times per second
                    except Exception as e:
                        break

            mouse_thread = threading.Thread(target=track_mouse, daemon=True)
            mouse_thread.start()

        def stop_live_mouse_tracking():
            live_tracking.set(False)

        def capture_current_position():
            """Capture current mouse position and update the coordinate fields"""
            try:
                mouse_x, mouse_y = pyautogui.position()
                x_var.set(mouse_x)
                y_var.set(mouse_y)
                status_label.config(text=f"✅ Captured coordinates: ({mouse_x}, {mouse_y})", foreground="green")
                # Flash the coordinate fields to show they were updated
                try:
                    x_spinbox.config(style="Accent.TSpinbox")
                    y_spinbox.config(style="Accent.TSpinbox")
                    dialog.after(500, lambda: [x_spinbox.config(style="TSpinbox"), y_spinbox.config(style="TSpinbox")])
                except:
                    pass  # Style might not exist
            except Exception as e:
                status_label.config(text=f"❌ Error capturing position: {e}", foreground="red")

        track_button = ttk.Button(capture_frame, text="Start Live Tracking", command=toggle_live_tracking)
        track_button.grid(row=1, column=0, padx=5, pady=5)

        capture_button = ttk.Button(capture_frame, text="Capture Now", command=capture_current_position)
        capture_button.grid(row=1, column=1, padx=5, pady=5)

        status_label = ttk.Label(capture_frame, text="Live tracking OFF", foreground="gray")
        status_label.grid(row=2, column=0, columnspan=2, pady=5)

        # Instructions
        instructions = ttk.Label(capture_frame,
                               text="💡 Instructions: Start tracking, move mouse to desired position, press ENTER to capture",
                               font=('Arial', 8), foreground='darkblue', wraplength=450)
        instructions.grid(row=3, column=0, columnspan=2, pady=5)

        # Additional settings
        settings_frame = ttk.LabelFrame(dialog, text="Additional Settings", padding=10)
        settings_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=10, pady=5)

        ttk.Label(settings_frame, text="Delay (seconds):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        delay_var = tk.DoubleVar(value=coord_config.get('delay', 1.0))
        ttk.Spinbox(settings_frame, from_=0.01, to=10.0, increment=0.1, textvariable=delay_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(settings_frame, text="Repeat Count:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        repeat_var = tk.IntVar(value=coord_config.get('repeat', 1))
        ttk.Spinbox(settings_frame, from_=1, to=100, textvariable=repeat_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(settings_frame, text="Use ESC Key:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        esc_var = tk.BooleanVar(value=coord_config.get('use_esc_key', False))
        ttk.Checkbutton(settings_frame, variable=esc_var).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(settings_frame, text="Description:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        desc_var = tk.StringVar(value=coord_config.get('description', ''))
        ttk.Entry(settings_frame, textvariable=desc_var, width=30).grid(row=3, column=1, sticky=tk.EW, padx=5, pady=5)

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)

        def save_coordinate():
            # Stop live tracking if active
            stop_live_mouse_tracking()

            coord_config['x'] = x_var.get()
            coord_config['y'] = y_var.get()
            coord_config['delay'] = delay_var.get()
            coord_config['repeat'] = repeat_var.get()
            coord_config['use_esc_key'] = esc_var.get()
            coord_config['description'] = desc_var.get()

            # Refresh the coordinates display
            self.load_coordinates(module_config['coordinates'])
            self.status_var.set(f"Updated coordinate: {coord_name}")
            dialog.destroy()

        def on_enter_key(event):
            """Handle Enter key press - capture coordinates if tracking is active"""
            if live_tracking.get():
                capture_current_position()
                return "break"  # Prevent default Enter behavior
            else:
                save_coordinate()
                return "break"

        def on_escape_key(event):
            """Handle Escape key press - stop tracking or close dialog"""
            if live_tracking.get():
                stop_live_mouse_tracking()
                return "break"
            else:
                dialog.destroy()
                return "break"

        # Bind keyboard shortcuts to dialog and all child widgets
        def bind_keys_recursively(widget):
            widget.bind('<Return>', on_enter_key)
            widget.bind('<KP_Enter>', on_enter_key)  # Numpad Enter
            widget.bind('<Escape>', on_escape_key)
            for child in widget.winfo_children():
                bind_keys_recursively(child)

        bind_keys_recursively(dialog)

        # Make dialog focusable for key bindings and ensure it stays focused
        dialog.focus_set()
        dialog.focus_force()

        # Also bind to the root window to catch global key presses
        def global_key_handler(event):
            if dialog.winfo_exists() and live_tracking.get():
                return on_enter_key(event)

        self.root.bind('<Return>', global_key_handler, add='+')
        self.root.bind('<KP_Enter>', global_key_handler, add='+')

        ttk.Button(button_frame, text="Save", command=save_coordinate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.LEFT)

        # Cleanup function for when dialog is closed
        def on_dialog_close():
            stop_live_mouse_tracking()
            # Unbind global key handlers
            try:
                self.root.unbind('<Return>')
                self.root.unbind('<KP_Enter>')
            except:
                pass
            dialog.destroy()

        dialog.protocol("WM_DELETE_WINDOW", on_dialog_close)
        dialog.columnconfigure(1, weight=1)

    # Scan region management methods
    def add_scan_region(self):
        """Add a new scan region"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        region_name = simpledialog.askstring("Add Scan Region", "Enter region name:")
        if region_name:
            default_region = {
                'x': 0,
                'y': 0,
                'width': 100,
                'height': 100,
                'description': ''
            }

            module_config = self.config_data['modules'][self.current_module]
            module_config.setdefault('scan_regions', {})[region_name] = default_region

            # Refresh the scan regions display
            self.load_scan_regions(module_config['scan_regions'])
            self.status_var.set(f"Added scan region: {region_name}")

    def edit_scan_region(self):
        """Edit selected scan region"""
        selection = self.regions_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a scan region to edit")
            return

        item = self.regions_tree.item(selection[0])
        region_name = item['values'][0]
        self.open_region_edit_dialog(region_name)

    def delete_scan_region(self):
        """Delete selected scan region"""
        selection = self.regions_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a scan region to delete")
            return

        item = self.regions_tree.item(selection[0])
        region_name = item['values'][0]

        if messagebox.askyesno("Delete Scan Region", f"Are you sure you want to delete scan region '{region_name}'?"):
            module_config = self.config_data['modules'][self.current_module]
            del module_config['scan_regions'][region_name]

            # Refresh the scan regions display
            self.load_scan_regions(module_config['scan_regions'])
            self.status_var.set(f"Deleted scan region: {region_name}")

    def open_region_edit_dialog(self, region_name):
        """Open scan region editing dialog"""
        module_config = self.config_data['modules'][self.current_module]
        region_config = module_config['scan_regions'][region_name]

        # Create edit dialog
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Edit Scan Region: {region_name}")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()

        # Region settings
        ttk.Label(dialog, text="X Position:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        x_var = tk.IntVar(value=region_config.get('x', 0))
        ttk.Spinbox(dialog, from_=0, to=3000, textvariable=x_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Y Position:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        y_var = tk.IntVar(value=region_config.get('y', 0))
        ttk.Spinbox(dialog, from_=0, to=2000, textvariable=y_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Width:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        width_var = tk.IntVar(value=region_config.get('width', 100))
        ttk.Spinbox(dialog, from_=10, to=1000, textvariable=width_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Height:").grid(row=3, column=0, sticky=tk.W, padx=10, pady=5)
        height_var = tk.IntVar(value=region_config.get('height', 100))
        ttk.Spinbox(dialog, from_=10, to=1000, textvariable=height_var, width=10).grid(row=3, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Description:").grid(row=4, column=0, sticky=tk.W, padx=10, pady=5)
        desc_var = tk.StringVar(value=region_config.get('description', ''))
        ttk.Entry(dialog, textvariable=desc_var, width=30).grid(row=4, column=1, sticky=tk.EW, padx=10, pady=5)

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)

        def save_region():
            region_config['x'] = x_var.get()
            region_config['y'] = y_var.get()
            region_config['width'] = width_var.get()
            region_config['height'] = height_var.get()
            region_config['description'] = desc_var.get()

            # Refresh the scan regions display
            self.load_scan_regions(module_config['scan_regions'])
            self.status_var.set(f"Updated scan region: {region_name}")
            dialog.destroy()

        ttk.Button(button_frame, text="Save", command=save_region).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.LEFT)

        dialog.columnconfigure(1, weight=1)

    # Testing and utility methods
    def test_template(self):
        """Test the selected template"""
        template_name = self.template_var.get()
        if not template_name:
            messagebox.showwarning("Warning", "Please select a template to test")
            return

        self.test_template_by_name(template_name)

    def _multi_scale_template_matching(self, screenshot, template, threshold):
        """
        Perform multi-scale template matching to detect templates at different sizes
        Returns: (confidence, center_x, center_y, scale, top_left, bottom_right) or None
        """
        best_match = None
        best_confidence = 0

        # Test different scales from 50% to 200% in 10% increments
        scales = [i/10.0 for i in range(5, 21)]  # 0.5 to 2.0 in 0.1 steps

        original_template_h, original_template_w = template.shape[:2]

        for scale in scales:
            # Resize template
            new_width = int(original_template_w * scale)
            new_height = int(original_template_h * scale)

            # Skip if template becomes too small or too large
            if new_width < 10 or new_height < 10:
                continue
            if new_width > screenshot.shape[1] or new_height > screenshot.shape[0]:
                continue

            try:
                scaled_template = cv2.resize(template, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

                # Perform template matching
                result = cv2.matchTemplate(screenshot, scaled_template, cv2.TM_CCOEFF_NORMED)
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                # Check if this is the best match so far
                if max_val > best_confidence and max_val >= threshold:
                    # Calculate center coordinates
                    center_x = max_loc[0] + new_width // 2
                    center_y = max_loc[1] + new_height // 2
                    top_left = max_loc
                    bottom_right = (max_loc[0] + new_width, max_loc[1] + new_height)

                    best_match = (max_val, center_x, center_y, scale, top_left, bottom_right)
                    best_confidence = max_val

            except Exception as e:
                # Skip this scale if there's an error
                continue

        return best_match

    def _feature_based_detection(self, screenshot, template, threshold=0.7):
        """
        Feature-based detection using SIFT/ORB for scale and rotation invariant matching
        Returns: (confidence, center_x, center_y, scale_estimate, top_left, bottom_right) or None
        """
        try:
            # Convert to grayscale
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)

            # Initialize ORB detector (SIFT requires opencv-contrib-python)
            orb = cv2.ORB_create(nfeatures=1000)

            # Find keypoints and descriptors
            kp1, des1 = orb.detectAndCompute(template_gray, None)
            kp2, des2 = orb.detectAndCompute(screenshot_gray, None)

            if des1 is None or des2 is None or len(des1) < 10 or len(des2) < 10:
                return None

            # Match features
            bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
            matches = bf.match(des1, des2)

            if len(matches) < 10:  # Need at least 10 good matches
                return None

            # Sort matches by distance
            matches = sorted(matches, key=lambda x: x.distance)
            good_matches = matches[:min(50, len(matches))]

            # Calculate match quality
            avg_distance = sum(m.distance for m in good_matches) / len(good_matches)
            max_distance = 100  # ORB descriptor distance range
            confidence = max(0, 1 - (avg_distance / max_distance))

            if confidence < threshold:
                return None

            # Extract matched keypoints
            src_pts = np.float32([kp1[m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
            dst_pts = np.float32([kp2[m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)

            # Find homography
            if len(src_pts) >= 4:
                M, mask = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 5.0)
                if M is not None:
                    # Get template corners
                    h, w = template_gray.shape
                    corners = np.float32([[0, 0], [w, 0], [w, h], [0, h]]).reshape(-1, 1, 2)

                    # Transform corners to screenshot space
                    transformed_corners = cv2.perspectiveTransform(corners, M)

                    # Calculate bounding box
                    x_coords = transformed_corners[:, 0, 0]
                    y_coords = transformed_corners[:, 0, 1]

                    top_left = (int(min(x_coords)), int(min(y_coords)))
                    bottom_right = (int(max(x_coords)), int(max(y_coords)))

                    # Calculate center
                    center_x = int((top_left[0] + bottom_right[0]) / 2)
                    center_y = int((top_left[1] + bottom_right[1]) / 2)

                    # Estimate scale
                    detected_width = bottom_right[0] - top_left[0]
                    detected_height = bottom_right[1] - top_left[1]
                    scale_x = detected_width / w
                    scale_y = detected_height / h
                    scale_estimate = (scale_x + scale_y) / 2

                    return (confidence, center_x, center_y, scale_estimate, top_left, bottom_right)

        except Exception as e:
            # Feature detection failed, return None
            pass

        return None

    def _set_preview_zoom(self, zoom_factor):
        """Set the preview zoom factor and refresh the current template"""
        self.preview_zoom.set(zoom_factor)
        # Refresh current template preview with new zoom
        current_template = self.template_var.get()
        if current_template:
            self.update_template_preview(current_template)

    def _fit_preview_to_window(self):
        """Fit the template preview to the available window space"""
        # Calculate zoom to fit in the preview label
        current_template = self.template_var.get()
        if not current_template:
            return

        # Parse template name format
        actual_template_name = current_template
        if ':' in current_template:
            prefix, actual_template_name = current_template.split(':', 1)

        # Find template file path
        template_path = os.path.join(TEMPLATE_FOLDER, f"{actual_template_name}.png")
        if not os.path.exists(template_path):
            for ext in ['.jpg', '.jpeg', '.bmp', '.gif']:
                alt_path = os.path.join(TEMPLATE_FOLDER, f"{actual_template_name}{ext}")
                if os.path.exists(alt_path):
                    template_path = alt_path
                    break

        if os.path.exists(template_path):
            try:
                with Image.open(template_path) as img:
                    img_width, img_height = img.size

                    # Get available space (approximate - now much larger)
                    available_width = 600   # Larger preview area width
                    available_height = 500  # Much taller preview area height

                    # Calculate zoom to fit
                    zoom_x = available_width / img_width
                    zoom_y = available_height / img_height
                    zoom_factor = min(zoom_x, zoom_y, 6.0)  # Cap at 6x zoom

                    self._set_preview_zoom(zoom_factor)
            except Exception as e:
                print(f"Error calculating fit zoom: {e}")

    def _create_preview_context_menu(self):
        """Create right-click context menu for template preview"""
        self.preview_context_menu = tk.Menu(self.root, tearoff=0)

        # Zoom options
        zoom_menu = tk.Menu(self.preview_context_menu, tearoff=0)
        self.preview_context_menu.add_cascade(label="Zoom", menu=zoom_menu)

        zoom_options = [0.5, 1.0, 1.5, 2.0, 3.0, 4.0, 5.0, 6.0]
        for zoom in zoom_options:
            zoom_menu.add_command(label=f"{zoom}x", command=lambda z=zoom: self._set_preview_zoom(z))

        zoom_menu.add_separator()
        zoom_menu.add_command(label="Fit to Window", command=self._fit_preview_to_window)

        # Other options
        self.preview_context_menu.add_separator()
        self.preview_context_menu.add_command(label="Open in System Viewer", command=self._open_template_externally)
        self.preview_context_menu.add_command(label="Copy Path", command=self._copy_template_path)

        # Bind right-click to preview label
        self.template_preview_label.bind("<Button-3>", self._show_preview_context_menu)

    def _show_preview_context_menu(self, event):
        """Show the preview context menu"""
        try:
            self.preview_context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.preview_context_menu.grab_release()

    def _open_template_externally(self):
        """Open the current template in the system's default image viewer"""
        current_template = self.template_var.get()
        if not current_template:
            return

        # Parse template name format
        actual_template_name = current_template
        if ':' in current_template:
            prefix, actual_template_name = current_template.split(':', 1)

        # Find template file path
        template_path = os.path.join(TEMPLATE_FOLDER, f"{actual_template_name}.png")
        if not os.path.exists(template_path):
            for ext in ['.jpg', '.jpeg', '.bmp', '.gif']:
                alt_path = os.path.join(TEMPLATE_FOLDER, f"{actual_template_name}{ext}")
                if os.path.exists(alt_path):
                    template_path = alt_path
                    break

        if os.path.exists(template_path):
            import subprocess
            import sys
            try:
                if sys.platform == "win32":
                    os.startfile(template_path)
                elif sys.platform == "darwin":
                    subprocess.call(["open", template_path])
                else:
                    subprocess.call(["xdg-open", template_path])
            except Exception as e:
                messagebox.showerror("Error", f"Could not open template: {e}")

    def _copy_template_path(self):
        """Copy the current template path to clipboard"""
        current_template = self.template_var.get()
        if not current_template:
            return

        # Parse template name format
        actual_template_name = current_template
        if ':' in current_template:
            prefix, actual_template_name = current_template.split(':', 1)

        # Find template file path
        template_path = os.path.join(TEMPLATE_FOLDER, f"{actual_template_name}.png")
        if not os.path.exists(template_path):
            for ext in ['.jpg', '.jpeg', '.bmp', '.gif']:
                alt_path = os.path.join(TEMPLATE_FOLDER, f"{actual_template_name}{ext}")
                if os.path.exists(alt_path):
                    template_path = alt_path
                    break

        if os.path.exists(template_path):
            self.root.clipboard_clear()
            self.root.clipboard_append(os.path.abspath(template_path))
            self.status_var.set(f"Copied path: {os.path.basename(template_path)}")

    def test_template_by_name(self, template_name):
        """Test a specific template by name - supports module:template and file:template formats"""
        try:
            # Clear previous results
            self.results_text.delete(1.0, tk.END)

            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            # Parse template name format (module:template or file:template)
            template_path = None
            config_threshold = 0.8
            actual_template_name = template_name

            if ':' in template_name:
                prefix, actual_template_name = template_name.split(':', 1)

                if prefix == 'file':
                    # Direct file reference
                    template_path = os.path.join(TEMPLATE_FOLDER, f"{actual_template_name}.png")
                    if not os.path.exists(template_path):
                        for ext in ['.jpg', '.jpeg', '.bmp', '.gif']:
                            alt_path = os.path.join(TEMPLATE_FOLDER, f"{actual_template_name}{ext}")
                            if os.path.exists(alt_path):
                                template_path = alt_path
                                break
                else:
                    # Module reference - get config from specific module
                    module_config = self.config_data['modules'].get(prefix, {})
                    template_config = module_config.get('templates', {}).get(actual_template_name, {})
                    config_threshold = template_config.get('threshold', 0.8)

                    # Try to find template file
                    template_path = os.path.join(TEMPLATE_FOLDER, f"{actual_template_name}.png")
                    if not os.path.exists(template_path):
                        for ext in ['.jpg', '.jpeg', '.bmp', '.gif']:
                            alt_path = os.path.join(TEMPLATE_FOLDER, f"{actual_template_name}{ext}")
                            if os.path.exists(alt_path):
                                template_path = alt_path
                                break
            else:
                # Legacy format - try to find template file directly
                template_path = os.path.join(TEMPLATE_FOLDER, f"{template_name}.png")
                if not os.path.exists(template_path):
                    for ext in ['.jpg', '.jpeg', '.bmp', '.gif']:
                        alt_path = os.path.join(TEMPLATE_FOLDER, f"{template_name}{ext}")
                        if os.path.exists(alt_path):
                            template_path = alt_path
                            break

            if not template_path or not os.path.exists(template_path):
                self.results_text.insert(tk.END, f"❌ Template file not found: {actual_template_name}\n")
                self.results_text.insert(tk.END, f"   Searched in: {TEMPLATE_FOLDER}\n")
                self.results_text.insert(tk.END, f"   Full template name: {template_name}\n")
                return

            template = cv2.imread(template_path)
            if template is None:
                self.results_text.insert(tk.END, f"❌ Failed to load template: {template_path}\n")
                return

            # Get threshold from GUI
            threshold = self.template_threshold_var.get()

            # Display test info
            self.results_text.insert(tk.END, f"🔍 Testing template: {template_name}\n")
            self.results_text.insert(tk.END, f"   Template file: {os.path.basename(template_path)}\n")
            self.results_text.insert(tk.END, f"   Template size: {template.shape[1]}x{template.shape[0]}\n")
            self.results_text.insert(tk.END, f"   GUI threshold: {threshold:.2f}\n")
            self.results_text.insert(tk.END, f"   Config threshold: {config_threshold:.2f}\n")
            self.results_text.insert(tk.END, f"   Screenshot size: {screenshot_cv.shape[1]}x{screenshot_cv.shape[0]}\n\n")

            # Choose detection method based on user selection
            method = getattr(self, 'template_matching_method', tk.StringVar(value="Multi-Scale")).get()

            best_match = None

            if method == "Exact":
                self.results_text.insert(tk.END, f"🔍 EXACT SIZE DETECTION:\n")
                self.results_text.insert(tk.END, f"   Using standard template matching\n")
                self.results_text.insert(tk.END, f"   Threshold: {threshold:.2f}\n\n")

                result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                if max_val >= threshold:
                    h, w = template.shape[:2]
                    center_x = max_loc[0] + w // 2
                    center_y = max_loc[1] + h // 2
                    top_left = max_loc
                    bottom_right = (max_loc[0] + w, max_loc[1] + h)
                    best_match = (max_val, center_x, center_y, 1.0, top_left, bottom_right)

            elif method == "Features":
                self.results_text.insert(tk.END, f"🔍 FEATURE-BASED DETECTION:\n")
                self.results_text.insert(tk.END, f"   Using ORB features for scale/rotation invariant matching\n")
                self.results_text.insert(tk.END, f"   Threshold: {threshold:.2f}\n\n")

                best_match = self._feature_based_detection(screenshot_cv, template, threshold)

            elif method == "All":
                self.results_text.insert(tk.END, f"🔍 COMPREHENSIVE DETECTION (All Methods):\n")
                self.results_text.insert(tk.END, f"   Testing: Multi-Scale → Feature-Based → Exact Size\n")
                self.results_text.insert(tk.END, f"   Threshold: {threshold:.2f}\n\n")

                # Try multi-scale first
                best_match = self._multi_scale_template_matching(screenshot_cv, template, threshold)
                if not best_match:
                    # Try feature-based
                    best_match = self._feature_based_detection(screenshot_cv, template, threshold * 0.7)
                    if best_match:
                        self.results_text.insert(tk.END, f"   → Found with feature-based detection\n")

            else:  # Multi-Scale (default)
                self.results_text.insert(tk.END, f"🔍 MULTI-SCALE DETECTION:\n")
                self.results_text.insert(tk.END, f"   Testing scales: 0.5x to 2.0x in 0.1x increments\n")
                self.results_text.insert(tk.END, f"   Threshold: {threshold:.2f}\n\n")

                best_match = self._multi_scale_template_matching(screenshot_cv, template, threshold)

            if best_match:
                confidence, center_x, center_y, scale, top_left, bottom_right = best_match

                self.results_text.insert(tk.END, f"✅ TEMPLATE DETECTED!\n")
                self.results_text.insert(tk.END, f"   Method: {method}\n")
                self.results_text.insert(tk.END, f"   Confidence: {confidence:.4f} (≥ {threshold:.2f})\n")
                self.results_text.insert(tk.END, f"   Scale factor: {scale:.2f}x\n")
                self.results_text.insert(tk.END, f"   Center coordinates: ({center_x}, {center_y})\n")
                self.results_text.insert(tk.END, f"   Detection area: {top_left} to {bottom_right}\n")

                # Show confidence level description
                if confidence >= 0.95:
                    confidence_desc = "Excellent match"
                elif confidence >= 0.9:
                    confidence_desc = "Very good match"
                elif confidence >= 0.85:
                    confidence_desc = "Good match"
                else:
                    confidence_desc = "Acceptable match"
                self.results_text.insert(tk.END, f"   Quality: {confidence_desc}\n")

                # Save debug screenshot with match highlighted
                debug_screenshot = screenshot_cv.copy()

                # Use different colors for different methods
                if method == "Features":
                    color = (255, 0, 0)  # Blue for feature-based
                    method_text = f"Features: {scale:.2f}x"
                elif method == "Exact":
                    color = (0, 255, 255)  # Yellow for exact
                    method_text = f"Exact: {scale:.2f}x"
                else:
                    color = (0, 255, 0)  # Green for multi-scale
                    method_text = f"Scale: {scale:.2f}x"

                cv2.rectangle(debug_screenshot, top_left, bottom_right, color, 3)
                cv2.circle(debug_screenshot, (center_x, center_y), 8, (0, 0, 255), -1)
                cv2.putText(debug_screenshot, method_text,
                           (top_left[0], top_left[1] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

                debug_path = os.path.join(DEBUG_FOLDER, f"{method.lower()}_match_{actual_template_name}_{datetime.now().strftime('%H%M%S')}.png")
                cv2.imwrite(debug_path, debug_screenshot)
                self.results_text.insert(tk.END, f"   Debug image saved: {debug_path}\n")

            else:
                # Template not detected with selected method
                result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                self.results_text.insert(tk.END, f"❌ TEMPLATE NOT DETECTED\n")
                self.results_text.insert(tk.END, f"   Method used: {method}\n")
                self.results_text.insert(tk.END, f"   Best exact-size match: {max_val:.4f}\n")
                self.results_text.insert(tk.END, f"   Required threshold: {threshold:.2f}\n")
                self.results_text.insert(tk.END, f"   💡 Suggestions:\n")
                self.results_text.insert(tk.END, f"      - Try 'All Methods' for comprehensive detection\n")
                self.results_text.insert(tk.END, f"      - Lower threshold (try {max(0.3, max_val - 0.1):.2f})\n")
                self.results_text.insert(tk.END, f"      - Check template image quality\n")
                self.results_text.insert(tk.END, f"      - Ensure template matches screen content\n")

                if method == "Exact":
                    self.results_text.insert(tk.END, f"      - Try 'Multi-Scale' if template size differs\n")
                elif method == "Multi-Scale":
                    self.results_text.insert(tk.END, f"      - Try 'Feature-Based' for rotated/distorted templates\n")

                # Save debug screenshot for analysis
                debug_path = os.path.join(DEBUG_FOLDER, f"{method.lower()}_nomatch_{actual_template_name}_{datetime.now().strftime('%H%M%S')}.png")
                cv2.imwrite(debug_path, screenshot_cv)
                self.results_text.insert(tk.END, f"   Debug screenshot saved: {debug_path}\n")

            self.results_text.insert(tk.END, f"\n⏰ Test completed at: {datetime.now().strftime('%H:%M:%S')}\n")
            self.results_text.insert(tk.END, "=" * 50 + "\n\n")
            self.results_text.see(tk.END)

        except Exception as e:
            self.results_text.insert(tk.END, f"Error testing template: {str(e)}\n")
            self.results_text.see(tk.END)

    def capture_screenshot(self):
        """Capture a screenshot for analysis"""
        try:
            screenshot = pyautogui.screenshot()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
            filepath = os.path.join(DEBUG_FOLDER, filename)
            screenshot.save(filepath)

            self.results_text.insert(tk.END, f"\nScreenshot saved: {filepath}\n")
            self.results_text.see(tk.END)
            self.status_var.set(f"Screenshot saved: {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to capture screenshot: {str(e)}")

    def toggle_tracking(self):
        """Toggle live mouse tracking"""
        if self.live_tracking:
            self.live_tracking = False
            self.tracking_button.config(text="Start Tracking")
            self.status_var.set("Mouse tracking stopped")
        else:
            self.live_tracking = True
            self.tracking_button.config(text="Stop Tracking")
            self.status_var.set("Mouse tracking started")
            self.start_mouse_tracking()

    def start_mouse_tracking(self):
        """Start mouse tracking in a separate thread"""
        def track_mouse():
            while self.live_tracking:
                try:
                    x, y = pyautogui.position()
                    self.position_var.set(f"Position: ({x}, {y})")
                    time.sleep(0.1)
                except:
                    break

        tracking_thread = threading.Thread(target=track_mouse, daemon=True)
        tracking_thread.start()

    def capture_position(self):
        """Capture current mouse position"""
        x, y = pyautogui.position()

        # Ask for coordinate name
        coord_name = simpledialog.askstring("Capture Position", "Enter coordinate name:")
        if coord_name:
            # Add to captured coordinates tree
            description = f"Captured at {datetime.now().strftime('%H:%M:%S')}"
            self.coords_tree.insert('', tk.END, values=(coord_name, x, y, description))
            self.status_var.set(f"Captured: {coord_name} at ({x}, {y})")

    # Advanced scanning methods from original debugger
    def scan_all(self):
        """Scan all coordinates"""
        self.results_text_detection.delete(1.0, tk.END)
        self.results_text_detection.insert(tk.END, f"SCANNING ALL - Size: {self.capture_size.get()}x{self.capture_size.get()}\n")
        self.results_text_detection.insert(tk.END, f"Method: {self.detection_method.get()}\n")
        self.results_text_detection.insert(tk.END, "=" * 50 + "\n\n")

        self.last_results = {}

        for map_name, (x, y) in self.coordinates.items():
            results, filename = self.detect_number(x, y, self.capture_size.get(), self.detection_method.get())
            self.last_results[map_name] = results

            self.results_text_detection.insert(tk.END, f"{map_name} at ({x}, {y}):\n")
            for method, result in results.items():
                self.results_text_detection.insert(tk.END, f"  {method}: {result}\n")
            if filename:
                self.results_text_detection.insert(tk.END, f"  Saved: {os.path.basename(filename)}\n")
            self.results_text_detection.insert(tk.END, "\n")

            self.results_text_detection.see(tk.END)
            self.root.update()

    def toggle_live_scan(self):
        """Toggle live scanning"""
        if not self.live_scanning:
            self.live_scanning = True
            self.live_thread = threading.Thread(target=self.live_scan_loop, daemon=True)
            self.live_thread.start()
        else:
            self.live_scanning = False

    def live_scan_loop(self):
        """Live scanning loop"""
        while self.live_scanning:
            try:
                selected = self.selected_map.get()
                if selected in self.coordinates:
                    x, y = self.coordinates[selected]
                    results, filename = self.detect_number(x, y, self.capture_size.get(), self.detection_method.get())

                    # Update results display
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    self.results_text_detection.insert(tk.END, f"[{timestamp}] {selected}: ")
                    for method, result in results.items():
                        self.results_text_detection.insert(tk.END, f"{method}={result} ")
                    self.results_text_detection.insert(tk.END, "\n")
                    self.results_text_detection.see(tk.END)

                time.sleep(1.0)  # Scan every second
            except Exception as e:
                print(f"Live scan error: {e}")
                break

    def save_results(self):
        """Save scan results to file"""
        if not self.last_results:
            messagebox.showwarning("Warning", "No results to save")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"scan_results_{timestamp}.txt"
        filepath = os.path.join(DEBUG_FOLDER, filename)

        try:
            with open(filepath, 'w') as f:
                f.write(f"Scan Results - {datetime.now()}\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Settings: Size={self.capture_size.get()}, Method={self.detection_method.get()}\n")
                f.write(f"OCR Scaling: {self.use_scaling.get()}, Factor: {self.scale_factor.get()}\n")
                f.write(f"Color Inversion: {self.invert_colors.get()}\n")
                f.write(f"OCR Mode: {self.ocr_mode.get()}\n")
                f.write(f"Morphology: {self.morphology_enabled.get()}, Op: {self.morphology_operation.get()}, Strength: {self.morphology_strength.get()}\n\n")

                for map_name, results in self.last_results.items():
                    x, y = self.coordinates[map_name]
                    f.write(f"{map_name} at ({x}, {y}):\n")
                    for method, result in results.items():
                        f.write(f"  {method}: {result}\n")
                    f.write("\n")

            messagebox.showinfo("Saved", f"Results saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Could not save results: {e}")

    def on_map_selected(self, event):
        """Handle map selection change"""
        selected = self.selected_map.get()
        if selected in self.coordinates:
            x, y = self.coordinates[selected]
            self.x_var.set(x)
            self.y_var.set(y)

    def update_coordinate(self):
        """Update selected coordinate (no popup)"""
        selected = self.selected_map.get()
        if selected in self.coordinates:
            self.coordinates[selected] = [self.x_var.get(), self.y_var.get()]

    def live_update_coordinate(self, *args):
        """Live update coordinate as user types/adjusts spinbox"""
        selected = self.selected_map.get()
        if selected in self.coordinates:
            self.coordinates[selected] = [self.x_var.get(), self.y_var.get()]

    def start_mouse_capture(self):
        """Start mouse capture mode"""
        self.capturing_mouse = True
        self.capture_status.config(text="Position mouse and press ENTER", foreground="red")
        self.root.after(10000, self.stop_mouse_capture)  # Auto-stop after 10 seconds

    def stop_mouse_capture(self):
        """Stop mouse capture mode"""
        self.capturing_mouse = False
        self.capture_status.config(text="", foreground="blue")

    def capture_mouse_position(self):
        """Capture current mouse position (called by ENTER key)"""
        if not self.capturing_mouse:
            return

        x, y = pyautogui.position()
        selected = self.selected_map.get()

        if selected in self.coordinates:
            self.coordinates[selected] = [x, y]
            self.x_var.set(x)
            self.y_var.set(y)

            timestamp = datetime.now().strftime("%H:%M:%S")
            self.capture_status.config(text=f"Captured {selected}: ({x}, {y}) at {timestamp}", foreground="green")

            # Auto-stop capture mode
            self.capturing_mouse = False
            self.root.after(3000, lambda: self.capture_status.config(text=""))

    def focus_game_window(self, resize=True):
        """Focus and optionally resize the Last War game window to 2560x1440"""
        try:
            import pygetwindow as gw
            import time

            # Search for Last War window with various possible titles
            possible_titles = [
                "Last War-Survival Game",
                "Last War",
                "Survival Game",
                "Last War - Survival Game",
                "LastWar",
                "last war"
            ]

            game_window = None

            # Try to find the window by exact title match first
            for title in possible_titles:
                try:
                    windows = gw.getWindowsWithTitle(title)
                    if windows:
                        game_window = windows[0]
                        print(f"Found game window: '{title}'")
                        break
                except:
                    continue

            # If exact match failed, try partial match
            if not game_window:
                all_windows = gw.getAllWindows()
                for window in all_windows:
                    window_title = window.title.lower()
                    if any(title.lower() in window_title for title in ["last war", "survival game"]):
                        game_window = window
                        print(f"Found game window by partial match: '{window.title}'")
                        break

            if not game_window:
                print("Could not find Last War game window")
                self.status_var.set("Game window not found")
                return False

            # Focus the window first
            try:
                game_window.activate()
                time.sleep(0.5)
                print("Game window focused")
            except Exception as e:
                print(f"Could not focus window: {str(e)}")

            # Get current size and screen dimensions
            current_width = game_window.width
            current_height = game_window.height

            # Get screen size for proper full screen dimensions
            import tkinter as tk
            root = tk.Tk()
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()
            root.destroy()

            # Use preferred game window dimensions (2560x1440)
            target_width = 2560
            target_height = 1440

            # Only resize if requested
            if resize:
                # Check if resize is needed
                size_tolerance = 20
                if (abs(current_width - target_width) > size_tolerance or
                    abs(current_height - target_height) > size_tolerance):

                    try:
                        # Move to top-left first, then resize
                        game_window.moveTo(0, 0)
                        time.sleep(0.3)
                        game_window.resizeTo(target_width, target_height)
                        time.sleep(1.0)  # Wait for resize to complete

                        print(f"Game window resized from {current_width}x{current_height} to {target_width}x{target_height}")
                        self.status_var.set(f"Game window resized to {target_width}x{target_height}")
                        return True

                    except Exception as e:
                        print(f"Could not resize window: {str(e)}")
                        self.status_var.set("Failed to resize game window")
                        return False
                else:
                    print(f"Game window already correct size: {current_width}x{current_height}")
                    self.status_var.set("Game window already correct size")
                    return True
            else:
                print(f"Game window focused (no resize): {current_width}x{current_height}")
                self.status_var.set("Game window focused")
                return True

        except ImportError:
            print("pygetwindow not available - cannot manage game window")
            self.status_var.set("pygetwindow not available")
            return False
        except Exception as e:
            print(f"Error managing game window: {str(e)}")
            self.status_var.set("Error managing game window")
            return False

    # Template creation methods
    def capture_at_mouse(self):
        """Capture template at current mouse position"""
        x, y = pyautogui.position()
        size = self.template_size.get()

        # Calculate capture region
        left = x - size // 2
        top = y - size // 2

        try:
            screenshot = pyautogui.screenshot(region=(left, top, size, size))
            self.captured_image = screenshot
            self.display_template_preview(screenshot, f"Captured at ({x}, {y})")
        except Exception as e:
            messagebox.showerror("Error", f"Could not capture image: {e}")

    def capture_at_coordinate(self):
        """Capture template at specified coordinates"""
        coord_dialog = tk.Toplevel(self.root)
        coord_dialog.title("Enter Coordinates")
        coord_dialog.geometry("300x150")
        coord_dialog.transient(self.root)
        coord_dialog.grab_set()

        ttk.Label(coord_dialog, text="X:").grid(row=0, column=0, padx=10, pady=10)
        x_entry = ttk.Entry(coord_dialog, width=10)
        x_entry.grid(row=0, column=1, padx=10, pady=10)

        ttk.Label(coord_dialog, text="Y:").grid(row=1, column=0, padx=10, pady=10)
        y_entry = ttk.Entry(coord_dialog, width=10)
        y_entry.grid(row=1, column=1, padx=10, pady=10)

        def capture():
            try:
                x = int(x_entry.get())
                y = int(y_entry.get())
                size = self.template_size.get()

                left = x - size // 2
                top = y - size // 2

                screenshot = pyautogui.screenshot(region=(left, top, size, size))
                self.captured_image = screenshot
                self.display_template_preview(screenshot, f"Captured at ({x}, {y})")
                coord_dialog.destroy()
            except ValueError:
                messagebox.showerror("Error", "Please enter valid coordinates")
            except Exception as e:
                messagebox.showerror("Error", f"Could not capture image: {e}")

        ttk.Button(coord_dialog, text="Capture", command=capture).grid(row=2, column=0, columnspan=2, pady=20)

    def capture_full_screen(self):
        """Capture full screen"""
        try:
            screenshot = pyautogui.screenshot()
            self.captured_image = screenshot
            self.display_template_preview(screenshot, "Full screen capture")
        except Exception as e:
            messagebox.showerror("Error", f"Could not capture screen: {e}")

    def display_template_preview(self, image, info_text):
        """Display captured image in preview"""
        try:
            # Resize for display if too large
            display_image = image.copy()
            if display_image.width > 300 or display_image.height > 300:
                display_image.thumbnail((300, 300), Image.Resampling.LANCZOS)

            photo = ImageTk.PhotoImage(display_image)
            self.template_preview.configure(image=photo, text="")
            self.template_preview.image = photo  # Keep a reference

            # Update info
            self.template_info.configure(text=f"{info_text}\nSize: {image.width}x{image.height}")

        except Exception as e:
            messagebox.showerror("Error", f"Could not display image: {e}")

    def save_template_to_folder(self):
        """Save captured image as template"""
        if not self.captured_image:
            messagebox.showwarning("Warning", "No image captured")
            return

        name = self.template_name.get().strip()
        if not name:
            messagebox.showwarning("Warning", "Please enter a template name")
            return

        if not name.endswith('.png'):
            name += '.png'

        filepath = os.path.join(TEMPLATE_FOLDER, name)

        try:
            self.captured_image.save(filepath)
            messagebox.showinfo("Saved", f"Template saved as {name}")
        except Exception as e:
            messagebox.showerror("Error", f"Could not save template: {e}")

    def test_template_detection(self):
        """Test template detection on current screen"""
        if not self.captured_image:
            messagebox.showwarning("Warning", "No template captured")
            return

        try:
            # Save temp template
            temp_path = os.path.join(DEBUG_FOLDER, "temp_template.png")
            self.captured_image.save(temp_path)

            # Take screenshot and find template
            screenshot = pyautogui.screenshot()
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            template_cv = cv2.cvtColor(np.array(self.captured_image), cv2.COLOR_RGB2BGR)

            result = cv2.matchTemplate(screenshot_cv, template_cv, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            if max_val > 0.8:
                messagebox.showinfo("Detection Result",
                                  f"Template found!\nConfidence: {max_val:.3f}\nLocation: {max_loc}")
            else:
                messagebox.showinfo("Detection Result",
                                  f"Template not found clearly.\nBest match confidence: {max_val:.3f}")

        except Exception as e:
            messagebox.showerror("Error", f"Could not test template: {e}")

    def toggle_template_capture_mode(self):
        """Toggle template capture mode"""
        self.template_capture_mode = self.template_mode_var.get()
        if self.template_capture_mode:
            self.template_mode_status.configure(text="LIVE MODE: Press ENTER to capture", foreground="green")
        else:
            self.template_mode_status.configure(text="", foreground="blue")

    def capture_template_at_mouse(self):
        """Capture template at current mouse position (called by Enter key)"""
        if not self.template_capture_mode:
            return

        try:
            x, y = pyautogui.position()
            size = self.template_size.get()

            # Calculate capture region
            left = x - size // 2
            top = y - size // 2

            screenshot = pyautogui.screenshot(region=(left, top, size, size))
            self.captured_image = screenshot
            self.display_template_preview(screenshot, f"Live captured at ({x}, {y})")

            # Update status
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.template_mode_status.configure(
                text=f"Captured at ({x}, {y}) - {timestamp}",
                foreground="blue"
            )

        except Exception as e:
            messagebox.showerror("Error", f"Could not capture template: {e}")

    def toggle_live_template_preview(self):
        """Toggle live template preview mode"""
        self.live_template_preview = self.live_preview_var.get()
        if self.live_template_preview:
            self.live_preview_status.configure(text="LIVE PREVIEW: Showing mouse area", foreground="green")
            self.template_preview_thread = threading.Thread(target=self.live_preview_loop, daemon=True)
            self.template_preview_thread.start()
        else:
            self.live_preview_status.configure(text="", foreground="green")

    def live_preview_loop(self):
        """Live template preview loop"""
        while self.live_template_preview:
            try:
                x, y = pyautogui.position()
                size = self.template_size.get()

                # Calculate capture region
                left = x - size // 2
                top = y - size // 2

                # Capture screenshot
                screenshot = pyautogui.screenshot(region=(left, top, size, size))

                # Update preview in main thread
                self.root.after(0, self.update_live_preview, screenshot, x, y)

                time.sleep(0.1)  # Update 10 times per second
            except Exception as e:
                print(f"Live preview error: {e}")
                break

    def update_live_preview(self, screenshot, x, y):
        """Update the live preview display"""
        try:
            if not self.live_template_preview:
                return

            # Resize for display if needed
            display_image = screenshot.copy()
            if display_image.width > 300 or display_image.height > 300:
                display_image.thumbnail((300, 300), Image.Resampling.LANCZOS)

            photo = ImageTk.PhotoImage(display_image)
            self.template_preview.configure(image=photo, text="")
            self.template_preview.image = photo  # Keep a reference

            # Update info
            self.template_info.configure(text=f"Live Preview at ({x}, {y})\nSize: {screenshot.width}x{screenshot.height}")

        except Exception as e:
            print(f"Preview update error: {e}")

    # Additional utility methods
    def save_as_template(self):
        """Save current captured image as template"""
        if hasattr(self, 'current_images') and self.current_images:
            # Get the most recent captured image
            latest_key = max(self.current_images.keys())
            image = self.current_images[latest_key]

            name = tk.simpledialog.askstring("Template Name", "Enter template name:")
            if name:
                if not name.endswith('.png'):
                    name += '.png'

                filepath = os.path.join(TEMPLATE_FOLDER, name)
                try:
                    cv2.imwrite(filepath, image)
                    messagebox.showinfo("Saved", f"Template saved as {name}")
                except Exception as e:
                    messagebox.showerror("Error", f"Could not save template: {e}")
        else:
            messagebox.showwarning("Warning", "No image to save as template")

    def save_debug_image(self):
        """Save current image for debugging"""
        if hasattr(self, 'current_images') and self.current_images:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"debug_capture_{timestamp}.png"
            filepath = os.path.join(DEBUG_FOLDER, filename)

            try:
                latest_key = max(self.current_images.keys())
                image = self.current_images[latest_key]
                cv2.imwrite(filepath, image)
                messagebox.showinfo("Saved", f"Debug image saved as {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Could not save debug image: {e}")
        else:
            messagebox.showwarning("Warning", "No image to save")

    def add_new_coordinate(self):
        """Add a new coordinate point"""
        name = tk.simpledialog.askstring("New Coordinate", "Enter coordinate name:")
        if name and name not in self.coordinates:
            x, y = pyautogui.position()
            self.coordinates[name] = [x, y]

            # Update combobox values
            map_combo = None
            for widget in self.detection_frame.winfo_children():
                if isinstance(widget, ttk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.LabelFrame):
                            for grandchild in child.winfo_children():
                                if isinstance(grandchild, ttk.Combobox):
                                    grandchild['values'] = list(self.coordinates.keys())
                                    break

            self.selected_map.set(name)
            self.x_var.set(x)
            self.y_var.set(y)
            # Coordinate added silently - no popup needed

    def load_coordinates_file(self):
        """Load coordinates from JSON file"""
        filename = filedialog.askopenfilename(
            title="Load Coordinates",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r') as f:
                    loaded_coords = json.load(f)
                    self.coordinates.update(loaded_coords)
                    messagebox.showinfo("Loaded", f"Loaded {len(loaded_coords)} coordinates")
            except Exception as e:
                messagebox.showerror("Error", f"Could not load coordinates: {e}")

    def save_coordinates_file(self):
        """Save coordinates to JSON file"""
        filename = filedialog.asksaveasfilename(
            title="Save Coordinates",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w') as f:
                    json.dump(self.coordinates, f, indent=2)
                    messagebox.showinfo("Saved", f"Saved {len(self.coordinates)} coordinates")
            except Exception as e:
                messagebox.showerror("Error", f"Could not save coordinates: {e}")

    # Live mouse tracking methods
    def start_live_mouse_tracking(self):
        """Start live mouse position tracking"""
        if not self.live_mouse_tracking:
            self.live_mouse_tracking = True
            self.mouse_thread = threading.Thread(target=self.mouse_tracking_loop, daemon=True)
            self.mouse_thread.start()

    def stop_live_mouse_tracking(self):
        """Stop live mouse position tracking"""
        self.live_mouse_tracking = False

    def mouse_tracking_loop(self):
        """Mouse tracking loop"""
        while self.live_mouse_tracking:
            try:
                x, y = pyautogui.position()
                self.current_mouse_x.set(str(x))
                self.current_mouse_y.set(str(y))
                time.sleep(0.1)  # Update 10 times per second
            except:
                break

    def save_current_mouse_coord(self):
        """Save current mouse coordinates"""
        x = self.current_mouse_x.get()
        y = self.current_mouse_y.get()
        timestamp = datetime.now().strftime("%H:%M:%S")
        coord_text = f"({x}, {y}) - {timestamp}"

        self.saved_coords.set(coord_text)
        self.coord_history.insert(0, coord_text)

        # Keep only last 50 entries
        if self.coord_history.size() > 50:
            self.coord_history.delete(50, tk.END)

    def copy_coordinates(self):
        """Copy coordinates to clipboard"""
        self.root.clipboard_clear()
        self.root.clipboard_append(self.saved_coords.get())
        messagebox.showinfo("Copied", "Coordinates copied to clipboard")

    def clear_coordinates(self):
        """Clear coordinate display"""
        self.saved_coords.set("Click 'Save Coord' to capture")

    def use_selected_coord(self):
        """Use selected coordinate from history"""
        selection = self.coord_history.curselection()
        if selection:
            coord_text = self.coord_history.get(selection[0])
            self.saved_coords.set(coord_text)

    def delete_selected_coord(self):
        """Delete selected coordinate from history"""
        selection = self.coord_history.curselection()
        if selection:
            self.coord_history.delete(selection[0])

    def save_coord_history(self):
        """Save coordinate history to file"""
        filename = f"coord_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        filepath = os.path.join(COORDS_FOLDER, filename)

        try:
            with open(filepath, 'w') as f:
                f.write(f"Coordinate History - {datetime.now()}\n")
                f.write("=" * 50 + "\n\n")

                for i in range(self.coord_history.size()):
                    f.write(f"{self.coord_history.get(i)}\n")

            messagebox.showinfo("Saved", f"History saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Could not save history: {e}")

    # ==================== FAST MAP OCR METHODS (35x35 OPTIMIZED) ====================







    def _test_ai_ocr(self):
        """Test AI OCR functionality with sample images"""
        try:
            # Initialize AI OCR service if not already done
            if not hasattr(self, 'ai_ocr_service'):
                from ai_ocr_service import AINumberRecognition
                self.ai_ocr_service = AINumberRecognition()

            if not self.ai_ocr_service.active_backends:
                messagebox.showwarning("AI OCR Test", "No OCR backends available")
                return

            import numpy as np
            import cv2
            from datetime import datetime

            # Test on real screen captures from predefined coordinates
            test_coordinates = [
                ("M1", self.coordinates.get('M1', [1079, 833])),
                ("M2", self.coordinates.get('M2', [1245, 835])),
                ("M3", self.coordinates.get('M3', [1412, 835])),
                ("M4", self.coordinates.get('M4', [1577, 833]))
            ]

            results = []
            test_results_text = "🧪 AI OCR Real Screen Test Results:\n" + "="*50 + "\n\n"
            capture_size = 25  # Use 25x25 for map piece detection

            for coord_name, (x, y) in test_coordinates:
                try:
                    # Capture screen region
                    capture_x = x - capture_size // 2
                    capture_y = y - capture_size // 2
                    screenshot = pyautogui.screenshot(region=(capture_x, capture_y, capture_size, capture_size))
                    image_array = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

                    # Test with AI OCR
                    piece_id = f"REAL_TEST_{coord_name}"
                    start_time = time.time()
                    result = self.ai_ocr_service.recognize_number(image_array, fast_mode=False, piece_id=piece_id)
                    test_time = time.time() - start_time

                    # Get detailed detection information
                    if hasattr(self.ai_ocr_service, 'last_detection_info'):
                        info = self.ai_ocr_service.last_detection_info
                        confidence = info.get('confidence', 0.0)
                        backend = info.get('backend_used', 'Unknown')
                        preprocessing = info.get('preprocessing_applied', 'None')
                        brightness = info.get('brightness', 0.0)
                        detected_text = info.get('detected_text', 'None')
                        success = info.get('success', False)

                        test_results_text += f"📍 {coord_name} at ({x}, {y}):\n"
                        if result is not None:
                            test_results_text += f"   ✅ DETECTED: {result}\n"
                            results.append(True)
                        else:
                            test_results_text += f"   ❌ NO DETECTION\n"
                            results.append(False)

                        test_results_text += f"   📊 Details:\n"
                        test_results_text += f"      Backend: {backend}\n"
                        test_results_text += f"      Confidence: {confidence:.3f}\n"
                        test_results_text += f"      Raw Text: '{detected_text}'\n"
                        test_results_text += f"      Preprocessing: {preprocessing}\n"
                        test_results_text += f"      Brightness: {brightness:.1f}\n"
                        test_results_text += f"      Time: {test_time:.3f}s\n"
                        test_results_text += f"      Size: {capture_size}x{capture_size}\n\n"
                    else:
                        test_results_text += f"📍 {coord_name} at ({x}, {y}): "
                        if result is not None:
                            test_results_text += f"✅ {result} (Time: {test_time:.3f}s)\n\n"
                            results.append(True)
                        else:
                            test_results_text += f"❌ No detection (Time: {test_time:.3f}s)\n\n"
                            results.append(False)

                except Exception as e:
                    test_results_text += f"📍 {coord_name} at ({x}, {y}): ❌ ERROR - {str(e)}\n\n"
                    results.append(False)

            success_rate = sum(results) / len(results) * 100 if results else 0
            test_results_text += f"📊 SUMMARY:\n"
            test_results_text += f"   Success Rate: {success_rate:.1f}% ({sum(results)}/{len(results)})\n"
            test_results_text += f"   Active Backends: {', '.join(self.ai_ocr_service.active_backends)}\n"
            test_results_text += f"   Current Settings:\n"
            test_results_text += f"      Confidence Threshold: {self.ai_ocr_confidence_threshold.get():.2f}\n"
            test_results_text += f"      Backend: {self.ai_ocr_backend.get()}\n"
            test_results_text += f"      White Text Preprocessing: {self.ai_ocr_white_text_preprocessing.get()}\n"
            test_results_text += f"      Debug Images: {self.ai_ocr_debug_images.get()}\n"

            # Display results in the results text area
            self.results_text_detection.delete(1.0, tk.END)
            self.results_text_detection.insert(tk.END, test_results_text)

            # Show summary dialog
            if success_rate >= 75:
                messagebox.showinfo("AI OCR Test", f"Test completed successfully!\n\nSuccess Rate: {success_rate:.1f}%\n\nCheck the Results panel for detailed information.")
            else:
                messagebox.showwarning("AI OCR Test", f"Test completed with issues.\n\nSuccess Rate: {success_rate:.1f}%\n\nCheck the Results panel for detailed information.")

        except Exception as e:
            error_msg = f"AI OCR test failed: {str(e)}"
            print(error_msg)
            messagebox.showerror("AI OCR Test Error", error_msg)

    def _open_ai_ocr_monitor(self):
        """Open AI OCR monitoring window"""
        try:
            # Create a simple monitoring window
            monitor_window = tk.Toplevel(self.root)
            monitor_window.title("AI OCR Real-time Monitor")
            monitor_window.geometry("800x600")
            monitor_window.configure(bg='#2b2b2b')

            # Make window stay on top
            monitor_window.attributes('-topmost', True)

            # Main frame
            main_frame = tk.Frame(monitor_window, bg='#2b2b2b')
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Title
            title_label = tk.Label(main_frame, text="AI OCR Real-time Monitoring",
                                  font=('Segoe UI', 14, 'bold'),
                                  fg='white', bg='#2b2b2b')
            title_label.pack(pady=(0, 20))

            # Status frame
            status_frame = tk.Frame(main_frame, bg='#3b3b3b', relief='ridge', bd=1)
            status_frame.pack(fill=tk.X, pady=(0, 10))

            # Initialize AI OCR service if needed
            if not hasattr(self, 'ai_ocr_service'):
                from ai_ocr_service import AINumberRecognition
                self.ai_ocr_service = AINumberRecognition()

            # Status label
            backends = getattr(self.ai_ocr_service, 'active_backends', [])
            status_text = f"AI OCR Status: Active ({', '.join(backends)})" if backends else "AI OCR Status: No backends available"
            status_label = tk.Label(status_frame, text=status_text,
                                   font=('Segoe UI', 10, 'bold'),
                                   fg='white', bg='#3b3b3b')
            status_label.pack(pady=10)

            # Detection log frame
            log_frame = tk.LabelFrame(main_frame, text="Recent AI OCR Detections",
                                     font=('Segoe UI', 10, 'bold'),
                                     fg='white', bg='#2b2b2b')
            log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            # Detection text area
            import tkinter.scrolledtext as scrolledtext
            detections_text = scrolledtext.ScrolledText(log_frame,
                                                       height=20,
                                                       bg='#3b3b3b',
                                                       fg='white',
                                                       font=('Consolas', 9))
            detections_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Control buttons
            control_frame = tk.Frame(main_frame, bg='#2b2b2b')
            control_frame.pack(fill=tk.X)

            def refresh_monitor():
                """Refresh the monitoring display"""
                try:
                    if hasattr(self.ai_ocr_service, 'get_recent_detections'):
                        recent = self.ai_ocr_service.get_recent_detections()
                        if recent:
                            detections_text.insert(tk.END, recent + "\n")
                            detections_text.see(tk.END)
                except Exception as e:
                    print(f"Error refreshing monitor: {str(e)}")

            def clear_log():
                """Clear the detection log"""
                detections_text.delete(1.0, tk.END)

            def test_detection():
                """Generate test detection"""
                self._test_ai_ocr()
                refresh_monitor()

            tk.Button(control_frame, text="Refresh", command=refresh_monitor,
                     bg='#4a90e2', fg='white', font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT, padx=(0, 10))
            tk.Button(control_frame, text="Clear Log", command=clear_log,
                     bg='#e74c3c', fg='white', font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT, padx=(0, 10))
            tk.Button(control_frame, text="Test Detection", command=test_detection,
                     bg='#27ae60', fg='white', font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT)

            # Auto-refresh every 2 seconds
            def auto_refresh():
                if monitor_window.winfo_exists():
                    refresh_monitor()
                    monitor_window.after(2000, auto_refresh)

            monitor_window.after(1000, auto_refresh)  # Start after 1 second

        except Exception as e:
            error_msg = f"Failed to open AI OCR monitor: {str(e)}"
            print(error_msg)
            messagebox.showerror("Monitor Error", error_msg)

    # ==================== FAST MAP OCR METHODS (35x35 OPTIMIZED) ====================

    def _test_number_recognition(self):
        """Test number recognition on the last captured image (map system + future dig)"""
        if not hasattr(self, 'last_captured_image') or self.last_captured_image is None:
            messagebox.showwarning("No Image", "Please capture an image first using SCAN ALL!")
            return

        try:
            from fast_map_ocr import get_fast_map_ocr
            fast_ocr = get_fast_map_ocr()

            # Enable debug mode for testing
            fast_ocr.set_debug_mode(True)
            fast_ocr.set_validation(self.number_color_preprocessing.get())  # Use checkbox for validation

            # Perform fast map piece recognition
            result = fast_ocr.recognize_map_piece(self.last_captured_image, "TEST")

            # Get detailed stats
            stats = fast_ocr.get_stats()

            # Show fast OCR results
            result_text = "🚀 FAST MAP OCR RESULTS\n"
            result_text += "=" * 40 + "\n\n"

            if result is not None:
                result_text += f"✅ SUCCESS!\n"
                result_text += f"Detected Number: {result}\n"
                result_text += f"Processing: Fast & Optimized\n\n"
            else:
                result_text += f"❌ FAILED TO RECOGNIZE\n\n"

            # Show performance statistics
            result_text += f"📊 PERFORMANCE STATS:\n"
            result_text += f"Total Attempts: {stats['total_attempts']}\n"
            result_text += f"Success Rate: {stats['success_rate_percent']:.1f}%\n"
            result_text += f"Avg Processing Time: {stats['average_processing_time']:.3f}s\n"
            result_text += f"Failed Detections: {stats['failed_detections']}\n"
            result_text += f"Validation Rejections: {stats['validation_rejections']}\n\n"

            result_text += f"🎯 OPTIMIZATIONS:\n"
            result_text += f"• Designed for 35x35 pixel captures\n"
            result_text += f"• 7 OCR configurations (PSM 6,8,7,13,10 + Legacy)\n"
            result_text += f"• Confidence-based result selection\n"
            result_text += f"• Enhanced preprocessing with adaptive scaling\n"
            result_text += f"• Relaxed validation for better detection\n"
            result_text += f"• Minimal processing time\n\n"

            result_text += f"💡 PROGRESS UPDATE:\n"
            result_text += f"• Current: 50% accuracy on all M1-M7 pieces\n"
            result_text += f"• Target: 80%+ accuracy with optimizations\n"
            result_text += f"• Fast automation workflow maintained\n"

            messagebox.showinfo("Fast Map OCR Test", result_text)

        except Exception as e:
            messagebox.showerror("Test Error", f"Failed to test fast map OCR: {str(e)}")

    def _reset_ocr_stats(self):
        """Reset Fast Map OCR performance statistics"""
        try:
            from fast_map_ocr import get_fast_map_ocr
            fast_ocr = get_fast_map_ocr()
            fast_ocr.reset_stats()
            messagebox.showinfo("Stats Reset", "Fast Map OCR performance statistics have been reset.")
        except Exception as e:
            messagebox.showerror("Reset Error", f"Failed to reset OCR stats: {str(e)}")

    def _show_number_stats(self):
        """Show Fast Map OCR performance statistics"""
        try:
            from fast_map_ocr import get_fast_map_ocr
            fast_ocr = get_fast_map_ocr()

            stats = fast_ocr.get_stats()

            # Create stats window
            stats_window = tk.Toplevel(self.root)
            stats_window.title("Fast Map OCR Performance Statistics")
            stats_window.geometry("500x400")
            stats_window.transient(self.root)

            # Stats text
            stats_text = tk.Text(stats_window, wrap=tk.WORD, font=('Consolas', 10))
            scrollbar = ttk.Scrollbar(stats_window, orient=tk.VERTICAL, command=stats_text.yview)
            stats_text.configure(yscrollcommand=scrollbar.set)

            stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # Format stats
            stats_content = "🚀 FAST MAP OCR PERFORMANCE STATISTICS\n"
            stats_content += "=" * 50 + "\n\n"

            stats_content += f"Total Attempts: {stats['total_attempts']}\n"
            stats_content += f"Successful Detections: {stats['successful_detections']}\n"
            stats_content += f"Failed Detections: {stats['failed_detections']}\n"
            stats_content += f"Validation Rejections: {stats['validation_rejections']}\n"
            stats_content += f"Success Rate: {stats['success_rate_percent']:.1f}%\n"
            stats_content += f"Average Processing Time: {stats['average_processing_time']:.3f}s\n\n"

            stats_content += "🎯 OPTIMIZATION FEATURES:\n"
            stats_content += "-" * 30 + "\n"
            stats_content += "• 7 OCR configurations (PSM 6,8,7,13,10 + Legacy)\n"
            stats_content += "• Enhanced preprocessing for 35x35 captures\n"
            stats_content += "• Confidence-based result selection\n"
            stats_content += "• Adaptive scaling and thresholding\n"
            stats_content += "• Relaxed validation for better detection\n"
            stats_content += "• Minimal processing time\n\n"

            stats_content += "💡 PROGRESS TRACKING:\n"
            stats_content += "-" * 20 + "\n"
            stats_content += "• Current baseline: 50% accuracy on all M1-M7\n"
            stats_content += "• Target improvement: 80%+ accuracy\n"
            stats_content += "• Optimized for 35x35 pixel captures\n"
            stats_content += "• Fast automation workflow maintained\n"
            stats_content += "\n"

            # System status based on performance
            if stats['total_attempts'] > 0:
                success_rate = stats['success_rate_percent']
                if success_rate >= 80:
                    stats_content += "✅ SYSTEM STATUS: EXCELLENT\n"
                    stats_content += "-" * 25 + "\n"
                    stats_content += f"✅ {success_rate:.1f}% success rate achieved!\n"
                    stats_content += "✅ Fast OCR system performing optimally\n"
                    stats_content += "✅ Ready for production automation\n\n"
                elif success_rate >= 60:
                    stats_content += "⚠️  SYSTEM STATUS: GOOD\n"
                    stats_content += "-" * 20 + "\n"
                    stats_content += f"⚠️  {success_rate:.1f}% success rate (target: 80%+)\n"
                    stats_content += "🔧 Consider adjusting validation settings\n"
                    stats_content += "📊 Monitor for consistent performance\n\n"
                else:
                    stats_content += "❌ SYSTEM STATUS: NEEDS IMPROVEMENT\n"
                    stats_content += "-" * 30 + "\n"
                    stats_content += f"❌ {success_rate:.1f}% success rate (target: 80%+)\n"
                    stats_content += "🔧 Check capture size and preprocessing\n"
                    stats_content += "📝 Verify 35x35 pixel captures are being used\n\n"
            else:
                stats_content += "⚠️  SYSTEM STATUS: NOT TESTED\n"
                stats_content += "-" * 25 + "\n"
                stats_content += "📝 Use 'Test Fast OCR' to evaluate performance\n"
                stats_content += "🔧 Capture test images for validation\n\n"

            stats_content += "💡 OPTIMIZATION TIPS:\n"
            stats_content += "-" * 20 + "\n"
            stats_content += "• Use 35x35 pixel captures for best results\n"
            stats_content += "• Enable Smart Validation to reject OCR errors\n"
            stats_content += "• Test with actual map piece screenshots\n"
            stats_content += "• Monitor success rate and processing time\n"
            stats_content += "• Reset stats periodically for fresh metrics\n"

            stats_text.insert(tk.END, stats_content)
            stats_text.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("Stats Error", f"Failed to show template stats: {str(e)}")



    def run(self):
        """Run the Config Helper application"""
        self.root.mainloop()


# Main execution
if __name__ == "__main__":
    try:
        app = ConfigHelper()
        app.run()
    except Exception as e:
        print(f"Error starting Config Helper: {str(e)}")
        import traceback
        traceback.print_exc()
