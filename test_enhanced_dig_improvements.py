"""
Test Enhanced Dig Control Panel Improvements
Verifies all new features: scan regions, template/action management, click adjustments, delays
"""

def test_config_helper_window_size():
    """Test Config Helper window size reduction"""
    print("🔧 TESTING CONFIG HELPER WINDOW SIZE")
    print("=" * 60)
    
    try:
        from config_helper import Config<PERSON>elper
        import tkinter as tk
        
        # Create Config Helper
        config_helper = ConfigHelper()
        
        # Check window dimensions
        expected_width = 700
        expected_height = 450
        
        if config_helper.window_width == expected_width and config_helper.window_height == expected_height:
            print(f"✅ Window size correctly set to {expected_width}x{expected_height}")
            
            # Test actual geometry
            geometry = config_helper.root.geometry()
            if f"{expected_width}x{expected_height}" in geometry:
                print(f"✅ Window geometry applied: {geometry}")
                return True
            else:
                print(f"❌ Window geometry mismatch: {geometry}")
                return False
        else:
            print(f"❌ Window size incorrect: {config_helper.window_width}x{config_helper.window_height}")
            return False
            
    except Exception as e:
        print(f"❌ Config Helper test failed: {e}")
        return False

def test_enhanced_dig_control_panel():
    """Test Enhanced Dig Control Panel improvements"""
    print("\n🚀 TESTING ENHANCED DIG CONTROL PANEL IMPROVEMENTS")
    print("=" * 80)
    
    try:
        from dig_control_panel_enhanced import EnhancedDigControlPanel
        import tkinter as tk
        from tkinter import ttk
        
        root = tk.Tk()
        root.withdraw()
        
        panel = EnhancedDigControlPanel(root)
        
        # Test 1: Scan region editing capability
        print("1. Testing scan region editing capability...")
        if hasattr(panel, '_create_scan_region_section'):
            print("✅ Scan region section creation method available")
        else:
            print("❌ Scan region section creation method missing")
            return False
        
        if hasattr(panel, '_capture_scan_region'):
            print("✅ Scan region capture method available")
        else:
            print("❌ Scan region capture method missing")
            return False
        
        # Test 2: Template and action management
        print("\n2. Testing template and action management...")
        management_methods = [
            '_add_verification_template',
            '_delete_verification_template',
            '_add_scan_template',
            '_delete_scan_template',
            '_add_send_template',
            '_delete_send_template',
            '_add_action',
            '_delete_action'
        ]
        
        for method_name in management_methods:
            if hasattr(panel, method_name):
                print(f"✅ {method_name} available")
            else:
                print(f"❌ {method_name} missing")
                return False
        
        # Test 3: Configuration data completeness
        print("\n3. Testing configuration data completeness...")
        config = panel.config_data
        
        # Check for send_to_dig_templates in step 3
        step_3 = config.get('execution_steps', {}).get('step_3_verify_navigation', {})
        send_templates = step_3.get('send_to_dig_templates', [])
        
        if send_templates:
            print(f"✅ Found {len(send_templates)} send_to_dig_templates in step 3")
            
            # Check for click adjustments in step 2
            step_2 = config.get('execution_steps', {}).get('step_2_find_treasure', {})
            scan_templates = step_2.get('scan_templates', [])
            
            click_adjustments_found = 0
            for template in scan_templates:
                if 'click_adjustment' in template:
                    click_adjustments_found += 1
            
            if click_adjustments_found > 0:
                print(f"✅ Found {click_adjustments_found} templates with click adjustments")
            else:
                print("❌ No click adjustments found in scan templates")
                return False
        else:
            print("❌ No send_to_dig_templates found in step 3")
            return False
        
        # Test 4: Advanced configuration variables
        print("\n4. Testing advanced configuration variables...")
        if hasattr(panel, 'advanced_coord_vars'):
            print("✅ Advanced coordinate variables system available")
        else:
            print("❌ Advanced coordinate variables system missing")
            return False
        
        if hasattr(panel, 'advanced_template_vars'):
            print("✅ Advanced template variables system available")
        else:
            print("❌ Advanced template variables system missing")
            return False
        
        if hasattr(panel, 'advanced_settings_vars'):
            print("✅ Advanced settings variables system available")
        else:
            print("❌ Advanced settings variables system missing")
            return False
        
        # Test 5: GUI creation with improvements
        print("\n5. Testing GUI creation with improvements...")
        try:
            panel.show_control_panel()
            print("✅ Enhanced control panel GUI created successfully")
            
            # Test advanced configuration window
            step_key = 'step_2_find_treasure'
            step_config = config['execution_steps'][step_key]
            
            # Create a test advanced window
            advanced_window = tk.Toplevel(root)
            advanced_window.title(f"Advanced Configuration - {step_key}")
            advanced_window.geometry("800x600")
            
            notebook = ttk.Notebook(advanced_window)
            notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # Test creating tabs
            panel._create_coordinates_tab(notebook, step_config, step_key)
            panel._create_templates_tab(notebook, step_config, step_key)
            panel._create_settings_tab(notebook, step_config, step_key)
            
            print("✅ Advanced configuration tabs created successfully")
            
            # Check if scan region section was added
            if hasattr(panel, 'advanced_settings_vars') and step_key in panel.advanced_settings_vars:
                settings_vars = panel.advanced_settings_vars[step_key]
                scan_vars = ['scan_x', 'scan_y', 'scan_width', 'scan_height', 'scan_description']
                
                scan_vars_found = sum(1 for var in scan_vars if var in settings_vars)
                if scan_vars_found == len(scan_vars):
                    print("✅ All scan region variables created")
                else:
                    print(f"⚠️ Only {scan_vars_found}/{len(scan_vars)} scan region variables found")
            
            advanced_window.destroy()
            
        except Exception as e:
            print(f"❌ GUI creation failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n✅ ALL ENHANCED DIG CONTROL PANEL TESTS PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced dig control panel test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_improvements():
    """Test specific improvements in detail"""
    print("\n🔍 TESTING SPECIFIC IMPROVEMENTS")
    print("=" * 60)
    
    try:
        from dig_control_panel_enhanced import EnhancedDigControlPanel
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        
        panel = EnhancedDigControlPanel(root)
        config = panel.config_data
        
        # Test click adjustment configuration
        print("1. Testing click adjustment configuration...")
        step_2 = config['execution_steps']['step_2_find_treasure']
        scan_templates = step_2.get('scan_templates', [])
        
        for i, template in enumerate(scan_templates):
            template_name = template.get('name', f'Template {i+1}')
            click_adj = template.get('click_adjustment', {})
            
            if click_adj:
                x_percent = click_adj.get('x_offset_percent', 50)
                y_percent = click_adj.get('y_offset_percent', 50)
                description = click_adj.get('description', '')
                
                print(f"   ✅ {template_name}: {x_percent}% X, {y_percent}% Y - {description}")
            else:
                print(f"   ⚠️ {template_name}: No click adjustment")
        
        # Test delay configuration
        print("\n2. Testing delay configuration...")
        step_1 = config['execution_steps']['step_1_open_chat']
        actions = step_1.get('actions', [])
        
        for i, action in enumerate(actions):
            description = action.get('description', f'Action {i+1}')
            delay = action.get('delay_after', 0)
            coords = action.get('coordinates', [0, 0])
            
            print(f"   ✅ {description}: ({coords[0]}, {coords[1]}) - {delay}s delay")
        
        # Test send_to_dig templates
        print("\n3. Testing send_to_dig templates...")
        step_3 = config['execution_steps']['step_3_verify_navigation']
        send_templates = step_3.get('send_to_dig_templates', [])
        
        print(f"   Found {len(send_templates)} send_to_dig templates:")
        for i, template in enumerate(send_templates):
            name = template.get('name', f'Template {i+1}')
            threshold = template.get('threshold', 0.7)
            coords = template.get('coordinates', [0, 0])
            
            print(f"   ✅ {name}: threshold {threshold}, coords ({coords[0]}, {coords[1]})")
        
        # Test timer settings
        print("\n4. Testing timer settings...")
        step_5 = config['execution_steps']['step_5_timer_management']
        timer_detection = step_5.get('timer_detection', {})
        rapid_clicking = step_5.get('rapid_clicking', {})
        
        if timer_detection:
            regions = timer_detection.get('regions', [])
            ocr_settings = timer_detection.get('ocr_settings', {})
            
            print(f"   ✅ Timer detection: {len(regions)} regions")
            print(f"   ✅ OCR settings: PSM {ocr_settings.get('psm_modes', [])}, whitelist '{ocr_settings.get('whitelist', '')}'")
        
        if rapid_clicking:
            threshold = rapid_clicking.get('trigger_threshold', 0)
            speed = rapid_clicking.get('click_speed', 0)
            duration = rapid_clicking.get('duration', 0)
            coords = rapid_clicking.get('coordinates', [0, 0])
            
            print(f"   ✅ Rapid clicking: {threshold}s threshold, {speed}s speed, {duration}s duration")
            print(f"   ✅ Rapid click coords: ({coords[0]}, {coords[1]})")
        
        print("\n✅ ALL SPECIFIC IMPROVEMENTS VERIFIED!")
        return True
        
    except Exception as e:
        print(f"❌ Specific improvements test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🎯 ENHANCED DIG CONTROL PANEL IMPROVEMENTS TEST")
    print("=" * 80)
    
    # Test 1: Config Helper window size
    success1 = test_config_helper_window_size()
    
    # Test 2: Enhanced dig control panel
    success2 = test_enhanced_dig_control_panel()
    
    # Test 3: Specific improvements
    success3 = test_specific_improvements()
    
    if success1 and success2 and success3:
        print("\n🎉 ALL IMPROVEMENT TESTS PASSED!")
        print("\n✅ IMPROVEMENTS DELIVERED:")
        print("🔧 Config Helper window size reduced to 700x450")
        print("📍 Scan region editing with drag-to-select capture")
        print("➕ Template and action addition/deletion functionality")
        print("🎯 Complete template management (verification, scan, send_to_dig)")
        print("⚙️ Click adjustment configuration (x/y offset percentages)")
        print("⏱️ Delay configuration for all actions (decimal precision)")
        print("💾 Advanced configuration saving to JSON")
        print("🔍 Missing template detection and display")
        
        print("\n🚀 READY FOR PRODUCTION USE!")
    else:
        print("\n⚠️ Some tests failed - check the output above")

if __name__ == "__main__":
    main()
