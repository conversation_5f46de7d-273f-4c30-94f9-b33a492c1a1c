#!/usr/bin/env python3
"""
Test script to verify intelligent ESC sequence implementations
"""

def test_gui_esc_sequence():
    """Test the GUI's intelligent ESC sequence method"""
    print("=" * 60)
    print("TESTING GUI INTELLIGENT ESC SEQUENCE")
    print("=" * 60)
    
    try:
        # Test 1: Import GUI module
        print("📦 Testing GUI import...")
        from gui import LastWarGUI
        print("✅ GUI import successful")
        
        # Test 2: Check if the enhanced method exists
        print("\n🔍 Checking for enhanced ESC sequence method...")
        if hasattr(LastWarGUI, '_execute_esc_to_main_panel'):
            print("✅ _execute_esc_to_main_panel method exists")
        else:
            print("❌ _execute_esc_to_main_panel method missing")
            return False
        
        # Test 3: Check method implementation
        print("\n🔍 Checking method implementation...")
        import inspect
        source = inspect.getsource(LastWarGUI._execute_esc_to_main_panel)
        
        if "ScreenScanner" in source:
            print("✅ Uses ScreenScanner for template detection")
        else:
            print("❌ ScreenScanner not found in method")
            return False
            
        if "events_button" in source:
            print("✅ Checks for events_button template (main panel)")
        else:
            print("❌ events_button template check not found")
            return False
            
        if "quit_game_dialog" in source:
            print("✅ Checks for quit_game_dialog template")
        else:
            print("❌ quit_game_dialog template check not found")
            return False
            
        if "max_attempts" in source:
            print("✅ Has maximum attempts limit to prevent infinite loops")
        else:
            print("❌ Maximum attempts limit not found")
            return False
        
        print("\n✅ GUI ESC sequence enhancement verified!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing GUI ESC sequence: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_map_trade_exit_logic():
    """Test the map trade module's intelligent exit logic"""
    print("\n" + "=" * 60)
    print("TESTING MAP TRADE INTELLIGENT EXIT LOGIC")
    print("=" * 60)
    
    try:
        # Test 1: Import map trade module
        print("📦 Testing map trade module import...")
        from modules.map_trade import MapTradeModule
        print("✅ Map trade module import successful")
        
        # Test 2: Check if the enhanced exit method exists
        print("\n🔍 Checking for enhanced exit sequence method...")
        if hasattr(MapTradeModule, '_enhanced_exit_sequence'):
            print("✅ _enhanced_exit_sequence method exists")
        else:
            print("❌ _enhanced_exit_sequence method missing")
            return False
        
        # Test 3: Check method implementation
        print("\n🔍 Checking method implementation...")
        import inspect
        source = inspect.getsource(MapTradeModule._enhanced_exit_sequence)
        
        if "ScreenScanner" in source:
            print("✅ Uses ScreenScanner for template detection")
        else:
            print("❌ ScreenScanner not found in method")
            return False
            
        if "events_button" in source:
            print("✅ Checks for events_button template (main panel)")
        else:
            print("❌ events_button template check not found")
            return False
            
        if "Already on main panel" in source:
            print("✅ Detects when already on main panel (avoids unnecessary ESC)")
        else:
            print("❌ Main panel detection logic not found")
            return False
            
        if "cancel_exchange_100x40" in source:
            print("✅ Checks for trade dialog templates")
        else:
            print("❌ Trade dialog template checks not found")
            return False
        
        print("\n✅ Map trade exit logic enhancement verified!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing map trade exit logic: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_availability():
    """Test that required templates are available"""
    print("\n" + "=" * 60)
    print("TESTING TEMPLATE AVAILABILITY")
    print("=" * 60)
    
    try:
        from screen_scanner import ScreenScanner
        scanner = ScreenScanner()
        
        required_templates = [
            'events_button',
            'quit_game_dialog', 
            'cancel_exchange_100x40',
            'initiate_exchange_100x40'
        ]
        
        print("🔍 Checking required templates...")
        all_available = True
        
        for template in required_templates:
            if template in scanner.templates:
                print(f"✅ {template} - Available")
            else:
                print(f"❌ {template} - Missing")
                all_available = False
        
        if all_available:
            print("\n✅ All required templates are available!")
            return True
        else:
            print("\n❌ Some required templates are missing!")
            return False
            
    except Exception as e:
        print(f"❌ Error checking templates: {e}")
        return False

if __name__ == "__main__":
    print("🧪 INTELLIGENT ESC SEQUENCE TESTING")
    print("=" * 60)
    
    # Run all tests
    gui_test = test_gui_esc_sequence()
    map_trade_test = test_map_trade_exit_logic()
    template_test = test_template_availability()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    if gui_test:
        print("✅ GUI ESC Sequence: ENHANCED")
        print("   • Uses template detection instead of blind ESC presses")
        print("   • Detects main panel, quit dialog, and trade dialogs")
        print("   • Has maximum attempts limit for safety")
    else:
        print("❌ GUI ESC Sequence: FAILED")
    
    if map_trade_test:
        print("✅ Map Trade Exit Logic: ENHANCED")
        print("   • Uses screen state detection before ESC presses")
        print("   • Avoids unnecessary ESC when already on main panel")
        print("   • Intelligently handles different dialog states")
    else:
        print("❌ Map Trade Exit Logic: FAILED")
    
    if template_test:
        print("✅ Template System: READY")
        print("   • All required templates are available")
    else:
        print("❌ Template System: INCOMPLETE")
    
    if gui_test and map_trade_test and template_test:
        print("\n🎉 ALL ENHANCEMENTS SUCCESSFUL!")
        print("Both ESC sequences now use intelligent template detection!")
    else:
        print("\n⚠️ SOME ENHANCEMENTS FAILED!")
        print("Please check the error messages above.")
