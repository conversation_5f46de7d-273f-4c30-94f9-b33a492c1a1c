2025-10-08 08:52:35,731 - LastWar.ModuleConfigManager - INFO - Loaded module configuration from module_configs.json
2025-10-08 08:52:35,732 - LastWar.dig - INFO - Using legacy configuration for module: dig
2025-10-08 08:52:35,732 - LastWar.dig - INFO - Loaded dig config: speed=0.005s, duration=20.0s, threshold=10s
2025-10-08 08:52:35,732 - LastWar.dig - INFO - Expected clicks: 4000 clicks over 20.0 seconds
2025-10-08 08:52:35,732 - LastWar.dig - INFO - Thank you message: 'Thank you for the dig!'
2025-10-08 08:52:35,732 - LastWar.ModuleConfigManager - INFO - Loaded module configuration from module_configs.json
2025-10-08 08:52:35,732 - LastWar.dig - INFO - Using legacy configuration for module: dig
2025-10-08 08:52:35,732 - LastWar.dig - INFO - Loaded dig config: speed=0.005s, duration=20.0s, threshold=10s
2025-10-08 08:52:35,733 - LastWar.dig - INFO - Expected clicks: 4000 clicks over 20.0 seconds
2025-10-08 08:52:35,733 - LastWar.dig - INFO - Thank you message: 'Thank you for the dig!'
2025-10-08 08:52:35,733 - LastWar.dig - INFO - Dig trigger detected: dig_icon - starting dig sequence
2025-10-08 08:52:35,734 - LastWar.ModuleConfigManager - INFO - Loaded module configuration from module_configs.json
2025-10-08 08:52:35,734 - LastWar.dig - INFO - Using legacy configuration for module: dig
2025-10-08 08:52:35,734 - LastWar.dig - INFO - Loaded dig config: speed=0.005s, duration=20.0s, threshold=10s
2025-10-08 08:52:35,734 - LastWar.dig - INFO - Expected clicks: 4000 clicks over 20.0 seconds
2025-10-08 08:52:35,734 - LastWar.dig - INFO - Thank you message: 'Thank you for the dig!'
2025-10-08 08:52:35,734 - LastWar.dig - INFO - Rapid click speed set to 0.01 seconds (2000 clicks over 20.0s)
2025-10-08 08:52:35,734 - LastWar.dig - INFO - Rapid click speed set to 1.0 seconds (20 clicks over 20.0s)
2025-10-08 08:52:35,734 - LastWar.dig - INFO - Rapid click speed set to 0.001 seconds (20000 clicks over 20.0s)
2025-10-08 08:52:35,734 - LastWar.dig - INFO - Rapid click duration set to 30.0 seconds (30000 total clicks)
2025-10-08 08:52:35,735 - LastWar.dig - INFO - Timer threshold set to 15 seconds
2025-10-08 08:52:35,735 - LastWar.dig - INFO - Thank you message set to: Test completion message
2025-10-08 08:52:35,736 - LastWar.ModuleConfigManager - INFO - Loaded module configuration from module_configs.json
2025-10-08 08:52:35,736 - LastWar.dig - INFO - Using legacy configuration for module: dig
2025-10-08 08:52:35,736 - LastWar.dig - INFO - Loaded dig config: speed=0.005s, duration=20.0s, threshold=10s
2025-10-08 08:52:35,736 - LastWar.dig - INFO - Expected clicks: 4000 clicks over 20.0 seconds
2025-10-08 08:52:35,736 - LastWar.dig - INFO - Thank you message: 'Thank you for the dig!'
2025-10-08 08:52:35,737 - LastWar.ModuleConfigManager - INFO - Loaded module configuration from module_configs.json
2025-10-08 08:52:35,737 - LastWar.dig - INFO - Using legacy configuration for module: dig
2025-10-08 08:52:35,737 - LastWar.dig - INFO - Loaded dig config: speed=0.005s, duration=20.0s, threshold=10s
2025-10-08 08:52:35,737 - LastWar.dig - INFO - Expected clicks: 4000 clicks over 20.0 seconds
2025-10-08 08:52:35,737 - LastWar.dig - INFO - Thank you message: 'Thank you for the dig!'
2025-10-08 08:52:35,737 - LastWar.dig - INFO - Clicking test click at (0, 0) - attempt 1
2025-10-08 08:52:35,738 - LastWar.dig - WARNING - Click attempt 1 failed: PyAutoGUI fail-safe triggered from mouse moving to a corner of the screen. To disable this fail-safe, set pyautogui.FAILSAFE to False. DISABLING FAIL-SAFE IS NOT RECOMMENDED.
2025-10-08 08:52:35,738 - LastWar.dig - ERROR - All click attempts failed for test click
2025-10-08 08:52:35,738 - LastWar.dig - INFO - Typing test typing - attempt 1
2025-10-08 08:52:35,738 - LastWar.dig - WARNING - Type attempt 1 failed: PyAutoGUI fail-safe triggered from mouse moving to a corner of the screen. To disable this fail-safe, set pyautogui.FAILSAFE to False. DISABLING FAIL-SAFE IS NOT RECOMMENDED.
2025-10-08 08:52:35,738 - LastWar.dig - ERROR - All type attempts failed for test typing
2025-10-08 08:52:35,746 - LastWar.dig - WARNING - Initiating emergency recovery - test recovery
2025-10-08 08:52:35,746 - mouse_override_controller - WARNING - [MOUSE_OVERRIDE] No main controller reference - cannot disable
2025-10-08 08:52:35,746 - LastWar.dig - INFO - Emergency ESC sequence 1/5
2025-10-08 08:52:35,746 - mouse_override_controller - WARNING - [MOUSE_OVERRIDE] No main controller reference - cannot enable
2025-10-08 08:52:35,746 - LastWar.dig - ERROR - Emergency recovery failed: PyAutoGUI fail-safe triggered from mouse moving to a corner of the screen. To disable this fail-safe, set pyautogui.FAILSAFE to False. DISABLING FAIL-SAFE IS NOT RECOMMENDED.
