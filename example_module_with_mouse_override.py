"""
Example Module demonstrating Mouse Override Controller usage
Shows how modules can properly control mouse override to prevent false positives
"""
import time
import pya<PERSON><PERSON><PERSON>
from typing import Dict, Any
import logging

from enhanced_base_module import EnhancedBaseModule
from mouse_override_controller import get_mouse_controller, mouse_override_disabled


class ExampleModuleWithMouseOverride(EnhancedBaseModule):
    """Example module showing proper mouse override usage"""

    def __init__(self):
        super().__init__(name="example_mouse_override", priority=5, enabled=False)  # Disabled by default
        
        self.cooldown_time = 5.0  # 5 second cooldown
        self.logger = logging.getLogger("LastWar.example_mouse_override")
        
        # Get reference to mouse controller
        self.mouse_controller = get_mouse_controller()
    
    def can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Check if this module should execute"""
        # Example: execute if some template is found
        templates_found = screen_data.get('templates_found', {})
        return 'example_template' in templates_found
    
    def run(self, screen_data: Dict[str, Any]) -> bool:
        """
        Execute the module with proper mouse override control
        
        Method 1: Using context manager (recommended)
        """
        try:
            # Method 1: Context manager approach (automatic cleanup)
            with mouse_override_disabled("Example module execution"):
                self.logger.info("[EXAMPLE] Starting automation sequence with mouse override disabled")
                
                # Perform automation tasks here
                success = self._perform_automation_sequence()
                
                if success:
                    self.logger.info("[EXAMPLE] Automation sequence completed successfully")
                    return True
                else:
                    self.logger.warning("[EXAMPLE] Automation sequence failed")
                    return False
                    
            # Mouse override is automatically re-enabled here with default cooldown
            
        except Exception as e:
            self.logger.error(f"[EXAMPLE] Error during execution: {e}")
            return False
    
    def run_manual_control(self, screen_data: Dict[str, Any]) -> bool:
        """
        Alternative method: Manual mouse override control
        Use this when you need more control over the timing
        """
        try:
            # Method 2: Manual control approach
            self.mouse_controller.disable_mouse_override("Example module - manual control")
            
            try:
                self.logger.info("[EXAMPLE] Starting automation with manual mouse control")
                
                # Perform automation tasks
                success = self._perform_automation_sequence()
                
                return success
                
            finally:
                # Always re-enable mouse override in finally block (3-second cooldown is now default)
                self.mouse_controller.enable_mouse_override("Example module complete")
                
        except Exception as e:
            self.logger.error(f"[EXAMPLE] Error during manual control execution: {e}")
            # Force enable mouse override in case of error
            self.mouse_controller.force_enable_mouse_override()
            return False
    
    def run_with_custom_cooldown(self, screen_data: Dict[str, Any]) -> bool:
        """
        Example with custom cooldown period
        """
        try:
            # Use context manager with custom cooldown
            with mouse_override_disabled("Example with custom cooldown", cooldown=5.0):
                self.logger.info("[EXAMPLE] Running with 5-second cooldown")
                
                # Perform quick automation tasks
                pyautogui.click(100, 100)
                time.sleep(0.5)
                pyautogui.click(200, 200)
                
                return True
                
        except Exception as e:
            self.logger.error(f"[EXAMPLE] Error with custom cooldown: {e}")
            return False
    
    def _perform_automation_sequence(self) -> bool:
        """
        Example automation sequence that would normally trigger mouse override
        """
        try:
            self.logger.info("[EXAMPLE] Performing clicks that would normally trigger mouse override")
            
            # Simulate automation clicks
            pyautogui.click(500, 300)
            time.sleep(0.2)
            
            pyautogui.click(600, 400)
            time.sleep(0.2)
            
            pyautogui.click(700, 500)
            time.sleep(0.2)
            
            # Simulate mouse movement that would normally trigger override
            pyautogui.moveTo(800, 600)
            time.sleep(0.1)
            pyautogui.moveTo(900, 700)
            
            self.logger.info("[EXAMPLE] Automation sequence completed - no mouse override triggered")
            return True
            
        except Exception as e:
            self.logger.error(f"[EXAMPLE] Error in automation sequence: {e}")
            return False
    
    def get_status_info(self) -> Dict[str, Any]:
        """Get current status including mouse override state"""
        base_status = super().get_status_info()
        
        # Add mouse override status
        mouse_status = self.mouse_controller.get_status()
        base_status.update({
            'mouse_override_enabled': mouse_status['enabled'],
            'mouse_override_active_overrides': mouse_status['active_overrides'],
            'mouse_override_cooldown_remaining': mouse_status['cooldown_remaining']
        })
        
        return base_status


# Example of how to use mouse override in a simple function
def example_standalone_function():
    """
    Example of using mouse override controller in a standalone function
    """
    from mouse_override_controller import disable_mouse_override, enable_mouse_override
    
    try:
        # Disable mouse override
        disable_mouse_override("Standalone function execution")
        
        # Perform automation
        print("Performing automation tasks...")
        pyautogui.click(100, 100)
        time.sleep(1)
        pyautogui.click(200, 200)
        
        print("Automation complete")
        
    finally:
        # Always re-enable (3-second cooldown is now default)
        enable_mouse_override("Standalone function complete")


# Example of checking mouse override status
def check_mouse_override_status():
    """Example of how to check mouse override status"""
    controller = get_mouse_controller()
    status = controller.get_status()
    
    print(f"Mouse Override Status:")
    print(f"  Enabled: {status['enabled']}")
    print(f"  Module Executing: {status['module_executing']}")
    print(f"  Detection Enabled: {status['detection_enabled']}")
    print(f"  Active Overrides: {status['active_overrides']}")
    print(f"  Override Reasons: {status['override_reasons']}")
    print(f"  Cooldown Remaining: {status['cooldown_remaining']:.1f}s")
    print(f"  In Cooldown: {status['in_cooldown']}")


if __name__ == "__main__":
    # Example usage
    print("Testing mouse override controller...")
    
    # Check initial status
    check_mouse_override_status()
    
    # Test standalone function
    example_standalone_function()
    
    # Check status after
    check_mouse_override_status()
