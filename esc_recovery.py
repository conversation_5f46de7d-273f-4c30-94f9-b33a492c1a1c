"""
ESC Recovery Utility Module
Simple standalone utility for performing ESC sequences to return to main menu
"""

import logging
import time
import pyautogui
from typing import Optional, Dict, Any

class EscRecovery:
    """Standalone ESC recovery utility with simple execute function"""
    
    def __init__(self, screen_scanner=None):
        self.logger = logging.getLogger("LastWar.EscRecovery")
        self.screen_scanner = screen_scanner
        
    def execute(self, max_attempts: int = 8, reason: str = "Manual recovery") -> bool:
        """
        Execute ESC recovery sequence to return to main menu
        
        Args:
            max_attempts: Maximum number of ESC attempts (default: 8)
            reason: Reason for recovery (for logging)
            
        Returns:
            bool: True if successfully returned to main menu, False otherwise
        """
        self.logger.info(f"[ESC_RECOVERY] Starting ESC recovery - Reason: {reason}")
        
        if not self.screen_scanner:
            self.logger.warning("[ESC_RECOVERY] No screen scanner available - performing blind ESC sequence")
            return self._blind_esc_recovery(max_attempts)
        
        return self._smart_esc_recovery(max_attempts)
    
    def _smart_esc_recovery(self, max_attempts: int) -> bool:
        """ESC recovery with screen detection"""
        for attempt in range(1, max_attempts + 1):
            self.logger.info(f"[ESC_RECOVERY] ESC attempt {attempt}/{max_attempts}")
            
            # Take screenshot and check current state
            screen_data = self.screen_scanner.scan_screen_cache_optimized(
                required_templates=['events_button', 'quit_game_dialog'],
                enhanced_detection=False,
                debug_screenshots=False
            )
            
            templates_detected = screen_data.get('templates_detected', {})
            
            # SUCCESS: Main menu detected
            if templates_detected.get('events_button', False):
                self.logger.info(f"[ESC_RECOVERY] ✅ SUCCESS: Main menu detected after {attempt-1} ESC attempts")
                return True
            
            # SUCCESS: Quit dialog detected - press ESC once more to dismiss it
            elif templates_detected.get('quit_game_dialog', False):
                self.logger.info(f"[ESC_RECOVERY] ✅ SUCCESS: Quit dialog detected after {attempt-1} ESC attempts - dismissing it")
                pyautogui.press('escape')
                time.sleep(1.5)  # Wait for quit dialog to close
                
                # Verify we're back to main menu
                screen_data = self.screen_scanner.scan_screen_cache_optimized(
                    required_templates=['events_button'],
                    enhanced_detection=False,
                    debug_screenshots=False
                )
                
                if screen_data.get('templates_detected', {}).get('events_button', False):
                    self.logger.info("[ESC_RECOVERY] ✅ Successfully returned to main menu after dismissing quit dialog")
                    return True
            
            # CONTINUE: Neither main menu nor quit dialog detected - press ESC and continue
            else:
                self.logger.info(f"[ESC_RECOVERY] Neither main menu nor quit dialog detected - pressing ESC {attempt}")
                pyautogui.press('escape')
                time.sleep(1.2)  # Wait for ESC effect
        
        # If we reach here, we've exceeded max attempts
        self.logger.warning(f"[ESC_RECOVERY] ⚠️ Reached max ESC attempts ({max_attempts}) without reaching main menu")
        return False
    
    def _blind_esc_recovery(self, max_attempts: int) -> bool:
        """ESC recovery without screen detection (fallback)"""
        self.logger.info(f"[ESC_RECOVERY] Performing blind ESC recovery - {max_attempts} attempts")
        
        for attempt in range(1, max_attempts + 1):
            self.logger.info(f"[ESC_RECOVERY] Blind ESC attempt {attempt}/{max_attempts}")
            pyautogui.press('escape')
            time.sleep(1.2)
        
        self.logger.info("[ESC_RECOVERY] Blind ESC recovery completed - cannot verify success without screen scanner")
        return True  # Assume success since we can't verify
    
    def quick_escape(self, count: int = 3) -> None:
        """
        Perform a quick ESC sequence without verification
        
        Args:
            count: Number of ESC presses (default: 3)
        """
        self.logger.info(f"[ESC_RECOVERY] Quick escape - {count} ESC presses")
        
        for i in range(count):
            pyautogui.press('escape')
            time.sleep(0.5)
        
        self.logger.info("[ESC_RECOVERY] Quick escape completed")


# Global instance for easy access
_esc_recovery_instance: Optional[EscRecovery] = None

def get_instance(screen_scanner=None) -> EscRecovery:
    """Get the global ESC recovery instance"""
    global _esc_recovery_instance
    if _esc_recovery_instance is None:
        _esc_recovery_instance = EscRecovery(screen_scanner)
    elif screen_scanner and not _esc_recovery_instance.screen_scanner:
        # Update screen scanner if provided and not already set
        _esc_recovery_instance.screen_scanner = screen_scanner
    return _esc_recovery_instance

def execute(max_attempts: int = 8, reason: str = "Manual recovery", screen_scanner=None) -> bool:
    """Execute ESC recovery - convenience function"""
    return get_instance(screen_scanner).execute(max_attempts, reason)

def quick_escape(count: int = 3, screen_scanner=None) -> None:
    """Quick escape - convenience function"""
    get_instance(screen_scanner).quick_escape(count)


# Example usage:
# import esc_recovery
# 
# # In any automation module:
# success = esc_recovery.execute(reason="After dig sequence")
# if not success:
#     print("Failed to return to main menu")
#
# # Quick escape without verification:
# esc_recovery.quick_escape(count=5)
#
# # Using with screen scanner:
# success = esc_recovery.execute(screen_scanner=my_scanner, reason="Map trade cleanup")
