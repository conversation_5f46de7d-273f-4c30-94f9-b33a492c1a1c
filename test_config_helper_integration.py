"""
Test Config Helper integration with Dig Control Panel
"""

import tkinter as tk
from tkinter import ttk
import sys
import os
import traceback

def test_config_helper_integration():
    """Test the Config Helper integration with Dig Control Panel"""
    print("🧪 Testing Config Helper Integration...")
    print("=" * 50)
    
    try:
        # Test 1: Import Config Helper
        print("Test 1: Importing Config Helper...")
        from config_helper import ConfigHelper
        print("✅ Config Helper import successful")
        
        # Test 2: Create Config Helper instance
        print("\nTest 2: Creating Config Helper instance...")
        config_helper = ConfigHelper()
        print("✅ Config Helper created successfully")
        
        # Test 3: Check if dig control panel button exists
        print("\nTest 3: Checking dig control panel button...")
        # The button should be in the main_control_frame
        found_button = False
        for widget in config_helper.root.winfo_children():
            if isinstance(widget, ttk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, ttk.Button):
                        button_text = child.cget('text')
                        if 'Dig Module Control' in button_text:
                            found_button = True
                            print(f"✅ Found button: {button_text}")
                            break
        
        if not found_button:
            print("❌ Dig control panel button not found")
            return False
        
        # Test 4: Test the dig control panel method
        print("\nTest 4: Testing dig control panel method...")
        if hasattr(config_helper, '_show_dig_control_panel'):
            print("✅ _show_dig_control_panel method exists")
            
            # Test calling the method (but don't actually show the window)
            try:
                # We'll test the import and initialization without showing GUI
                from dig_control_panel import DigControlPanel
                test_panel = DigControlPanel(config_helper.root)
                print("✅ Dig control panel can be created from Config Helper")
                
                # Clean up test panel
                if hasattr(test_panel, 'window') and test_panel.window:
                    test_panel.window.destroy()
                    
            except Exception as e:
                print(f"❌ Error creating dig control panel: {e}")
                return False
        else:
            print("❌ _show_dig_control_panel method not found")
            return False
        
        # Test 5: Check other specialized interface buttons
        print("\nTest 5: Checking other interface buttons...")
        specialized_buttons = [
            'Template Diagnostics',
            'Centralized Scanner'
        ]
        
        for button_name in specialized_buttons:
            method_name = f"_show_{button_name.lower().replace(' ', '_')}"
            if hasattr(config_helper, method_name):
                print(f"✅ {button_name} method exists")
            else:
                print(f"⚠️ {button_name} method missing")
        
        # Test 6: Test configuration access
        print("\nTest 6: Testing configuration access...")
        if hasattr(config_helper, 'config_manager') and config_helper.config_manager:
            config_data = config_helper.config_manager.load_config()
            print(f"✅ Configuration accessible: {len(config_data)} sections")
        else:
            print("⚠️ Configuration manager not available")
        
        print("\n" + "=" * 50)
        print("🎉 Config Helper integration tests completed!")
        print("\nYou can now test the actual integration:")
        print("1. The Config Helper window is open")
        print("2. Click the '🔧 Dig Module Control Panel' button")
        print("3. The dig control panel should open without errors")
        print("4. Test all tabs and features in the control panel")
        print("\nClose the Config Helper window when done testing.")
        
        # Show the Config Helper GUI for manual testing
        config_helper.root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        print("\nFull traceback:")
        traceback.print_exc()
        return False

def test_button_functionality():
    """Test the actual button functionality"""
    print("\n🔘 Testing Button Functionality...")
    print("=" * 40)
    
    try:
        from config_helper import ConfigHelper
        
        # Create Config Helper
        config_helper = ConfigHelper()
        
        # Test the dig control panel method directly
        print("Testing _show_dig_control_panel method...")
        
        # This should work without errors now
        config_helper._show_dig_control_panel()
        print("✅ Dig control panel method executed successfully")
        
        # The control panel window should now be open
        print("✅ Control panel window should be visible")
        
        return True
        
    except Exception as e:
        print(f"❌ Button functionality test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 Config Helper + Dig Control Panel Integration Test")
    print("=" * 70)
    
    # Run the integration test
    success = test_config_helper_integration()
    
    if success:
        print("\n🎉 INTEGRATION TEST PASSED!")
        print("The Dig Control Panel is properly integrated with Config Helper.")
    else:
        print("\n❌ Integration test failed")

if __name__ == "__main__":
    main()
