# Template Architecture Guide

## Overview

The Last War automation system uses a **dual-template architecture** to separate concerns between detection and execution. Understanding this separation is crucial for proper configuration and avoiding template duplication issues.

## Template Types

### 1. TRIGGER Templates (Centralized Scanner)
**Location**: `template_scanner_config.json`
**Purpose**: Detect when to activate modules
**Managed by**: Centralized Template Scanner
**Usage**: Screen scanning to determine which module should execute

#### Characteristics:
- **Detection-focused**: Used to identify game states that require action
- **Module activation**: When detected, triggers specific module execution
- **Priority-based**: Higher priority templates are checked first
- **Cooldown management**: Prevents spam execution
- **Exclusion rules**: Can exclude other templates when detected

#### Example TRIGGER templates:
```json
{
  "template_name": "Help_Template",
  "priority": 1,
  "module": "help_click",
  "action": "action_1_HelpButton",
  "description": "Help_template -> help_click.1_HelpButton",
  "enabled": true,
  "threshold": 0.8,
  "cooldown": 0.0
}
```

### 2. EXECUTION Templates (Module-Specific)
**Location**: `module_configs.json` (per module)
**Purpose**: Used by modules during their internal execution logic
**Managed by**: Individual modules
**Usage**: Internal module decision-making and navigation

#### Characteristics:
- **Execution-focused**: Used during module operation for navigation/decisions
- **Module-internal**: Only used by the specific module that owns them
- **Context-specific**: Help modules make decisions during execution
- **No priority system**: Managed by module logic
- **No cooldowns**: Managed by module timing

#### Example EXECUTION templates:
```json
{
  "name": "daily_tasks_button",
  "threshold": 0.8,
  "required": false,
  "description": "Daily tasks button in UI"
}
```

## Template Separation Rules

### DO NOT Duplicate Templates
- **TRIGGER templates** should only exist in `template_scanner_config.json`
- **EXECUTION templates** should only exist in `module_configs.json`
- Same template file should not appear in both configurations

### Clear Naming Convention
- **TRIGGER templates**: Use descriptive names for what they detect
  - `Help_Template` - detects help requests
  - `dig_icon` - detects dig opportunities
  - `squad_0_4` - detects empty squad status

- **EXECUTION templates**: Use action-oriented names
  - `daily_tasks_button` - button to click for daily tasks
  - `claim_reward` - reward claim button
  - `close_button` - dialog close button

### Configuration Guidelines

#### For TRIGGER Templates (template_scanner_config.json):
1. **Focus on detection**: What game state triggers this module?
2. **Set appropriate priority**: Lower number = higher priority
3. **Configure cooldowns**: Prevent spam execution
4. **Use exclusion rules**: Manage template conflicts
5. **Map to module actions**: Specify which module method to call

#### For EXECUTION Templates (module_configs.json):
1. **Focus on execution**: What UI elements does the module need to interact with?
2. **Set appropriate thresholds**: Balance accuracy vs reliability
3. **Mark as required/optional**: Critical vs nice-to-have templates
4. **Provide descriptions**: Document what each template is for

## Common Mistakes to Avoid

### ❌ Template Duplication
```json
// WRONG: Same template in both files
// template_scanner_config.json
{"template_name": "help_button", "module": "help_click"}

// module_configs.json -> help_click
{"name": "help_button", "threshold": 0.8}
```

### ✅ Proper Separation
```json
// CORRECT: Different templates for different purposes
// template_scanner_config.json (TRIGGER)
{"template_name": "Help_Template", "module": "help_click"}

// module_configs.json -> help_click (EXECUTION)
{"name": "help_button_ui", "threshold": 0.8}
```

### ❌ Wrong Template Location
```json
// WRONG: Execution template in scanner config
// template_scanner_config.json
{"template_name": "close_dialog_button", "module": "daily_tasks"}
```

### ✅ Correct Template Location
```json
// CORRECT: Execution template in module config
// module_configs.json -> daily_tasks
{"name": "close_dialog_button", "threshold": 0.8}
```

## Module-Specific Guidelines

### Help Click Module
- **TRIGGER**: `Help_Template`, `Help_chat` (detect help requests)
- **EXECUTION**: UI navigation templates (if needed for complex workflows)

### Daily Tasks Module
- **TRIGGER**: None (manual trigger only)
- **EXECUTION**: `daily_tasks_button`, `claim_reward`, etc.

### Dig Module
- **TRIGGER**: `dig_icon`, `dig_up_dropdown`, `test_flight_treasure`
- **EXECUTION**: Navigation and confirmation templates

### Map Trade Module
- **TRIGGER**: Timer-based or manual
- **EXECUTION**: Map interface templates, trade confirmation templates

## Configuration Interface Usage

### Centralized Scanner GUI
- Manages TRIGGER templates only
- Controls module activation priorities
- Sets up exclusion rules
- Configures cooldowns

### Module Configuration Interface
- Manages EXECUTION templates only
- Sets template thresholds
- Configures template requirements
- Tests template detection

## Best Practices

1. **Single Responsibility**: Each template should have one clear purpose
2. **Clear Naming**: Template names should indicate their purpose and location
3. **Proper Thresholds**: Balance between false positives and missed detections
4. **Regular Testing**: Test both TRIGGER and EXECUTION templates regularly
5. **Documentation**: Document the purpose of each template
6. **Version Control**: Track changes to template configurations

## Troubleshooting

### Template Not Detected
1. Check if it's in the correct configuration file
2. Verify threshold settings
3. Test template detection manually
4. Check for template file existence

### Module Not Activating
1. Check TRIGGER template configuration
2. Verify template priority and cooldowns
3. Check exclusion rules
4. Test centralized scanner detection

### Module Execution Issues
1. Check EXECUTION template configuration
2. Verify module-specific template thresholds
3. Test individual template detection
4. Check module logic and timing

This architecture ensures clean separation of concerns and prevents template duplication issues.
