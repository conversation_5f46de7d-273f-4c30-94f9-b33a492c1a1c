#!/usr/bin/env python3
"""
Test script to verify the Fast Map OCR confidence scoring fixes
This script tests the new realistic confidence system and enhanced validation
"""

import sys
import os
import numpy as np
import cv2

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fast_map_ocr import get_fast_map_ocr

def create_test_image(text: str, size: tuple = (35, 35)) -> np.ndarray:
    """Create a test image with white text on black background"""
    image = np.zeros((size[1], size[0], 3), dtype=np.uint8)
    
    # Add white text
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.8
    thickness = 1
    color = (255, 255, 255)  # White text
    
    # Get text size and center it
    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
    x = (size[0] - text_size[0]) // 2
    y = (size[1] + text_size[1]) // 2
    
    cv2.putText(image, text, (x, y), font, font_scale, color, thickness)
    
    return image

def test_confidence_scoring():
    """Test the new confidence scoring system"""
    print("=" * 60)
    print("TESTING FAST MAP OCR CONFIDENCE SCORING FIXES")
    print("=" * 60)
    
    # Initialize Fast Map OCR with debug mode
    ocr = get_fast_map_ocr()
    ocr.set_debug_mode(True)
    ocr.set_validation(True)
    
    # Test cases: (text_to_render, expected_behavior)
    test_cases = [
        # Good cases - should have reasonable confidence
        ("17", "Should pass validation with moderate confidence"),
        ("5", "Should pass validation with moderate confidence"),
        ("23", "Should pass validation with moderate confidence"),
        ("42", "Should pass validation with moderate confidence"),
        
        # Bad cases - should have low confidence or fail validation
        ("111", "Should FAIL validation (OCR artifact)"),
        ("888", "Should FAIL validation (OCR artifact)"),
        ("181", "Should FAIL validation (OCR error pattern)"),
        ("118", "Should FAIL validation (OCR error pattern)"),
        ("1111", "Should FAIL validation (OCR artifact)"),
        ("138", "Should FAIL validation (too large)"),
        ("200", "Should FAIL validation (too large)"),
        ("88", "Should FAIL validation (repeated digits)"),
        ("77", "Should FAIL validation (repeated digits)"),
    ]
    
    print(f"\nTesting {len(test_cases)} cases...\n")
    
    results = []
    for i, (test_text, expected) in enumerate(test_cases, 1):
        print(f"Test {i}: '{test_text}' - {expected}")
        print("-" * 50)
        
        # Create test image
        test_image = create_test_image(test_text)
        
        # Test OCR recognition
        result = ocr.recognize_map_piece(test_image, f"TEST_{i}")
        
        if result is not None:
            print(f"✅ RESULT: {result}")
            results.append((test_text, result, "PASSED"))
        else:
            print(f"❌ RESULT: None (rejected)")
            results.append((test_text, None, "REJECTED"))
        
        print()
    
    # Summary
    print("=" * 60)
    print("SUMMARY OF RESULTS")
    print("=" * 60)
    
    passed_count = 0
    rejected_count = 0
    
    for test_text, result, status in results:
        if status == "PASSED":
            passed_count += 1
            print(f"✅ '{test_text}' -> {result}")
        else:
            rejected_count += 1
            print(f"❌ '{test_text}' -> REJECTED")
    
    print(f"\nTotal: {len(results)} tests")
    print(f"Passed: {passed_count}")
    print(f"Rejected: {rejected_count}")
    
    # Expected behavior analysis
    print("\n" + "=" * 60)
    print("EXPECTED BEHAVIOR ANALYSIS")
    print("=" * 60)
    
    good_cases = ["17", "5", "23", "42"]
    bad_cases = ["111", "888", "181", "118", "1111", "138", "200", "88", "77"]
    
    good_passed = sum(1 for text, result, status in results if text in good_cases and status == "PASSED")
    bad_rejected = sum(1 for text, result, status in results if text in bad_cases and status == "REJECTED")
    
    print(f"Good cases passed: {good_passed}/{len(good_cases)} ({good_passed/len(good_cases)*100:.1f}%)")
    print(f"Bad cases rejected: {bad_rejected}/{len(bad_cases)} ({bad_rejected/len(bad_cases)*100:.1f}%)")
    
    if good_passed == len(good_cases) and bad_rejected == len(bad_cases):
        print("\n🎉 SUCCESS: All tests behaved as expected!")
        print("✅ Confidence scoring and validation fixes are working correctly")
    else:
        print("\n⚠️  Some tests didn't behave as expected")
        print("❌ May need further tuning of confidence scoring or validation")
    
    return results

if __name__ == "__main__":
    print("Starting confidence scoring test...")
    try:
        test_confidence_scoring()
        print("Test completed successfully!")
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
