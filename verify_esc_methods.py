#!/usr/bin/env python3
"""
Simple verification script to check ESC method implementations
"""

def verify_gui_method():
    """Verify the GUI ESC method implementation"""
    print("🔍 VERIFYING GUI ESC METHOD")
    print("=" * 40)
    
    try:
        from gui import LastWarGUI
        import inspect
        
        # Get the method source
        source = inspect.getsource(LastWarGUI._execute_esc_to_main_panel)
        
        print("📋 Method source preview:")
        lines = source.split('\n')[:10]  # First 10 lines
        for i, line in enumerate(lines, 1):
            print(f"  {i:2d}: {line}")
        
        # Check for key indicators
        if "ScreenScanner" in source:
            print("✅ Uses ScreenScanner")
        else:
            print("❌ No ScreenScanner found")
            
        if "max_attempts" in source:
            print("✅ Has max_attempts logic")
        else:
            print("❌ No max_attempts found")
            
        if "events_button" in source:
            print("✅ Checks for events_button")
        else:
            print("❌ No events_button check")
            
        # Check for old patterns
        if "ESC press 1/5" in source:
            print("❌ Still has old fixed ESC sequence!")
        else:
            print("✅ No old fixed ESC sequence found")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def verify_map_trade_method():
    """Verify the map trade exit method implementation"""
    print("\n🔍 VERIFYING MAP TRADE EXIT METHOD")
    print("=" * 40)
    
    try:
        from modules.map_trade import MapTradeModule
        import inspect
        
        # Get the method source
        source = inspect.getsource(MapTradeModule._enhanced_exit_sequence)
        
        print("📋 Method source preview:")
        lines = source.split('\n')[:10]  # First 10 lines
        for i, line in enumerate(lines, 1):
            print(f"  {i:2d}: {line}")
        
        # Check for key indicators
        if "ScreenScanner" in source:
            print("✅ Uses ScreenScanner")
        else:
            print("❌ No ScreenScanner found")
            
        if "Already on main panel" in source:
            print("✅ Checks if already on main panel")
        else:
            print("❌ No main panel check")
            
        if "events_button" in source:
            print("✅ Checks for events_button")
        else:
            print("❌ No events_button check")
            
        # Check for old patterns
        if "pressing ESC again for safety" in source:
            print("❌ Still has old blind ESC logic!")
        else:
            print("✅ No old blind ESC logic found")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 ESC METHOD VERIFICATION")
    print("=" * 50)
    
    gui_ok = verify_gui_method()
    map_trade_ok = verify_map_trade_method()
    
    print("\n📊 VERIFICATION RESULTS")
    print("=" * 30)
    
    if gui_ok:
        print("✅ GUI ESC method: UPDATED")
    else:
        print("❌ GUI ESC method: ISSUES FOUND")
        
    if map_trade_ok:
        print("✅ Map Trade exit method: UPDATED")
    else:
        print("❌ Map Trade exit method: ISSUES FOUND")
        
    if gui_ok and map_trade_ok:
        print("\n🎉 BOTH METHODS SUCCESSFULLY UPDATED!")
        print("The intelligent ESC sequences are ready to use.")
    else:
        print("\n⚠️ SOME ISSUES DETECTED!")
        print("Please check the error messages above.")
