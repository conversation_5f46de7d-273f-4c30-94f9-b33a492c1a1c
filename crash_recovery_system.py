#!/usr/bin/env python3
"""
COMPREHENSIVE CRASH RECOVERY AND DEBUGGING SYSTEM
Handles module failures, game window detection, automatic recovery, and game launching
"""

import time
import os
import subprocess
import logging
from typing import Dict, Optional, Tuple
from datetime import datetime

try:
    import pygetwindow as gw
    WINDOW_DETECTION_AVAILABLE = True
except ImportError:
    WINDOW_DETECTION_AVAILABLE = False
    print("Warning: pygetwindow not available - window detection disabled")

class CrashRecoverySystem:
    """Comprehensive crash recovery and debugging system"""
    
    def __init__(self, logger: logging.Logger = None):
        self.logger = logger or logging.getLogger(__name__)
        
        # Game configuration
        self.game_shortcut_path = r"C:\Users\<USER>\Desktop\Last War：Survival Game.lnk"
        self.game_window_title = "Last War-Survival Game"
        self.game_load_wait_time = 30.0  # Wait 30 seconds for game to load
        
        # Failure tracking
        self.module_failure_counts = {}  # Track consecutive failures per module
        self.max_module_failures = 3  # Pause automation after 3 consecutive failures
        self.game_launch_attempts = 0  # Track consecutive game launch attempts
        self.max_game_launch_attempts = 2  # Pause for 10 minutes after 2 failed launches
        self.game_launch_pause_duration = 600.0  # 10 minutes in seconds
        self.last_game_launch_pause = 0  # Timestamp of last 10-minute pause
        
        # Recovery state
        self.automation_paused = False
        self.pause_reason = ""
        self.pause_until = 0  # Timestamp when pause ends
        
        # References to main system components
        self.main_controller = None
        self.centralized_scanner = None
        
        self.logger.info("[CRASH_RECOVERY] Crash recovery system initialized")
    
    def set_main_controller(self, controller):
        """Set reference to main controller"""
        self.main_controller = controller
        self.logger.info("[CRASH_RECOVERY] Main controller reference set")
    
    def set_centralized_scanner(self, scanner):
        """Set reference to centralized scanner"""
        self.centralized_scanner = scanner
        self.logger.info("[CRASH_RECOVERY] Centralized scanner reference set")
    
    def is_game_window_active(self) -> Tuple[bool, str]:
        """
        Check if the Last War game window is currently focused and active
        Returns: (is_active, status_message)
        """
        if not WINDOW_DETECTION_AVAILABLE:
            return False, "Window detection not available (pygetwindow missing)"
        
        try:
            # Search for game window with various possible titles
            possible_titles = [
                "Last War-Survival Game",
                "Last War",
                "Survival Game", 
                "Last War - Survival Game",
                "LastWar"
            ]
            
            game_window = None
            found_title = ""
            
            # Try to find the window by exact title match first
            for title in possible_titles:
                try:
                    windows = gw.getWindowsWithTitle(title)
                    if windows:
                        game_window = windows[0]
                        found_title = title
                        break
                except Exception as e:
                    self.logger.debug(f"[WINDOW_CHECK] Error checking title '{title}': {e}")
                    continue
            
            if not game_window:
                # Try partial matching as fallback
                all_windows = gw.getAllWindows()
                for window in all_windows:
                    if window.title and "Last War" in window.title:
                        game_window = window
                        found_title = window.title
                        break
            
            if game_window:
                # Check if window is visible and has reasonable size
                is_visible = game_window.visible
                has_size = game_window.width > 100 and game_window.height > 100
                
                if is_visible and has_size:
                    self.logger.info(f"[WINDOW_CHECK] Game window found and active: '{found_title}' ({game_window.width}x{game_window.height})")
                    return True, f"Game window active: {found_title}"
                else:
                    self.logger.warning(f"[WINDOW_CHECK] Game window found but not active: visible={is_visible}, size={game_window.width}x{game_window.height}")
                    return False, f"Game window found but not active (visible={is_visible}, size={game_window.width}x{game_window.height})"
            else:
                self.logger.warning("[WINDOW_CHECK] Game window not found")
                return False, "Game window not found"
                
        except Exception as e:
            self.logger.error(f"[WINDOW_CHECK] Error checking game window: {e}")
            return False, f"Window check error: {e}"
    
    def execute_esc_recovery(self) -> bool:
        """
        Execute ESC recovery sequence to return to main menu with proper template detection
        Returns True if successful, False otherwise
        """
        try:
            self.logger.info("[ESC_RECOVERY] Starting ESC recovery sequence with template detection...")

            # PRIORITY 1: Use centralized scanner's ESC recovery (has proper template detection)
            if self.centralized_scanner and hasattr(self.centralized_scanner, '_perform_esc_recovery'):
                self.logger.info("[ESC_RECOVERY] Using centralized scanner ESC recovery (with template detection)")
                success = self.centralized_scanner._perform_esc_recovery()
                if success:
                    self.logger.info("[ESC_RECOVERY] ✅ Centralized scanner ESC recovery successful")
                    return True
                else:
                    self.logger.warning("[ESC_RECOVERY] ⚠️ Centralized scanner ESC recovery failed, trying enhanced fallback")
            else:
                self.logger.warning("[ESC_RECOVERY] Centralized scanner not available, using enhanced fallback")

            # PRIORITY 2: Enhanced fallback ESC recovery with template detection
            return self._execute_enhanced_esc_recovery()

        except Exception as e:
            self.logger.error(f"[ESC_RECOVERY] Error during ESC recovery: {e}")
            return False

    def _execute_enhanced_esc_recovery(self) -> bool:
        """
        Enhanced fallback ESC recovery with proper template detection
        Returns True if successful, False otherwise
        """
        try:
            import pyautogui
            from screen_scanner import ScreenScanner

            self.logger.info("[ESC_RECOVERY] Starting enhanced fallback ESC recovery with template detection...")

            # Initialize screen scanner for template detection
            scanner = ScreenScanner()
            max_esc_attempts = 8  # Maximum ESC presses to prevent infinite loops
            esc_attempt = 0

            while esc_attempt < max_esc_attempts:
                esc_attempt += 1
                self.logger.info(f"[ESC_RECOVERY] Enhanced ESC attempt {esc_attempt}/{max_esc_attempts}")

                # Check current screen state BEFORE pressing ESC
                screen_data = scanner.scan_screen_cache_optimized(
                    required_templates=['events_button', 'quit_game_dialog']
                )
                templates_detected = screen_data.get('templates_detected', {})

                # SUCCESS: Main menu already detected (events button visible)
                if templates_detected.get('events_button', False):
                    self.logger.info(f"[ESC_RECOVERY] ✅ SUCCESS: Main menu already detected (attempt {esc_attempt})")
                    return True

                # SUCCESS: Quit dialog detected - press ESC once more to dismiss it
                elif templates_detected.get('quit_game_dialog', False):
                    self.logger.info(f"[ESC_RECOVERY] ✅ Quit dialog detected (attempt {esc_attempt}) - dismissing it")
                    pyautogui.press('escape')
                    time.sleep(1.5)  # Wait for quit dialog to close

                    # Verify we're back to main menu
                    screen_data = scanner.scan_screen_cache_optimized(required_templates=['events_button'])
                    if screen_data.get('templates_detected', {}).get('events_button', False):
                        self.logger.info("[ESC_RECOVERY] ✅ Successfully returned to main menu after dismissing quit dialog")
                        return True
                    else:
                        self.logger.warning("[ESC_RECOVERY] ⚠️ Quit dialog dismissed but main menu not detected - continuing")

                # CONTINUE: Neither main menu nor quit dialog detected - press ESC and continue
                else:
                    self.logger.info(f"[ESC_RECOVERY] Neither main menu nor quit dialog detected - pressing ESC {esc_attempt}")
                    pyautogui.press('escape')
                    time.sleep(1.2)  # Wait for ESC effect

            # If we reach here, we've exceeded max attempts
            self.logger.warning(f"[ESC_RECOVERY] ⚠️ Reached max ESC attempts ({max_esc_attempts}) without reaching main menu")

            # Final verification attempt
            screen_data = scanner.scan_screen_cache_optimized(required_templates=['events_button'])
            if screen_data.get('templates_detected', {}).get('events_button', False):
                self.logger.info("[ESC_RECOVERY] ✅ Final check: Main menu confirmed despite max attempts reached")
                return True
            else:
                self.logger.error("[ESC_RECOVERY] ❌ Final check: Main menu not confirmed - ESC recovery failed")
                return False

        except Exception as e:
            self.logger.error(f"[ESC_RECOVERY] Error during enhanced ESC recovery: {e}")
            return False
    
    def launch_game(self) -> bool:
        """
        Launch the game using the shortcut
        Returns True if launch was attempted, False if shortcut not found
        """
        try:
            if not os.path.exists(self.game_shortcut_path):
                self.logger.error(f"[GAME_LAUNCH] Game shortcut not found: {self.game_shortcut_path}")
                return False
            
            self.logger.info(f"[GAME_LAUNCH] Launching game from: {self.game_shortcut_path}")
            
            # Launch the game
            subprocess.Popen([self.game_shortcut_path], shell=True)
            
            self.logger.info(f"[GAME_LAUNCH] Game launch command executed, waiting {self.game_load_wait_time} seconds for game to load...")
            
            # Wait for game to load
            time.sleep(self.game_load_wait_time)
            
            # Check if game window is now active
            is_active, status = self.is_game_window_active()
            
            if is_active:
                self.logger.info(f"[GAME_LAUNCH] Game launched successfully: {status}")
                self.game_launch_attempts = 0  # Reset counter on success
                return True
            else:
                self.logger.warning(f"[GAME_LAUNCH] Game launch may have failed: {status}")
                self.game_launch_attempts += 1
                return False
                
        except Exception as e:
            self.logger.error(f"[GAME_LAUNCH] Error launching game: {e}")
            self.game_launch_attempts += 1
            return False
    
    def reactivate_centralized_scanner(self):
        """Re-activate the centralized scanner to resume automation"""
        try:
            if self.centralized_scanner:
                self.logger.info("[SCANNER_REACTIVATE] Re-activating centralized scanner...")
                # Reset any failure counters in the scanner
                if hasattr(self.centralized_scanner, 'failed_scan_count'):
                    self.centralized_scanner.failed_scan_count = 0
                self.logger.info("[SCANNER_REACTIVATE] Centralized scanner re-activated")
            else:
                self.logger.warning("[SCANNER_REACTIVATE] No centralized scanner reference available")
        except Exception as e:
            self.logger.error(f"[SCANNER_REACTIVATE] Error re-activating scanner: {e}")
    
    def handle_module_failure(self, module_name: str, error: Exception, error_details: str = "") -> bool:
        """
        Handle a module execution failure with comprehensive recovery
        
        Args:
            module_name: Name of the failed module
            error: The exception that occurred
            error_details: Additional error details
            
        Returns:
            True if automation should continue, False if automation should be paused
        """
        try:
            # Log the failure with detailed information
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.logger.error(f"[MODULE_FAILURE] {timestamp} - Module '{module_name}' failed")
            self.logger.error(f"[MODULE_FAILURE] Error type: {type(error).__name__}")
            self.logger.error(f"[MODULE_FAILURE] Error message: {str(error)}")
            if error_details:
                self.logger.error(f"[MODULE_FAILURE] Additional details: {error_details}")
            
            # Track consecutive failures for this module
            if module_name not in self.module_failure_counts:
                self.module_failure_counts[module_name] = 0
            self.module_failure_counts[module_name] += 1
            
            consecutive_failures = self.module_failure_counts[module_name]
            self.logger.warning(f"[MODULE_FAILURE] Module '{module_name}' consecutive failures: {consecutive_failures}/{self.max_module_failures}")
            
            # Check if we've exceeded the failure limit for this module
            if consecutive_failures >= self.max_module_failures:
                self.logger.critical(f"[MODULE_FAILURE] Module '{module_name}' has failed {consecutive_failures} times consecutively - PAUSING AUTOMATION")
                self.pause_automation(f"Module '{module_name}' failed {consecutive_failures} times consecutively")
                return False
            
            # Step 1: Check if game window is active
            is_game_active, window_status = self.is_game_window_active()
            self.logger.info(f"[MODULE_FAILURE] Window detection result: {window_status}")
            
            if is_game_active:
                # Game is running but module failed - execute ESC recovery and restart module
                self.logger.info(f"[MODULE_FAILURE] Game is running - executing ESC recovery for module '{module_name}'")
                
                esc_success = self.execute_esc_recovery()
                if esc_success:
                    self.logger.info(f"[MODULE_FAILURE] ESC recovery successful - module '{module_name}' will be restarted automatically")
                    return True  # Continue automation - module will be retried
                else:
                    self.logger.error(f"[MODULE_FAILURE] ESC recovery failed for module '{module_name}'")
                    return True  # Still continue - let normal retry logic handle it
                    
            else:
                # Game is not running - attempt to launch it
                self.logger.warning(f"[MODULE_FAILURE] Game window not detected - attempting to launch game")
                
                # Check if we're in a game launch pause period
                current_time = time.time()
                if current_time < self.last_game_launch_pause + self.game_launch_pause_duration:
                    remaining_pause = (self.last_game_launch_pause + self.game_launch_pause_duration) - current_time
                    self.logger.info(f"[MODULE_FAILURE] Still in game launch pause period - {remaining_pause:.0f} seconds remaining")
                    return True  # Continue automation but don't launch game yet
                
                # Check if we've exceeded game launch attempts
                if self.game_launch_attempts >= self.max_game_launch_attempts:
                    self.logger.warning(f"[MODULE_FAILURE] Game launch failed {self.game_launch_attempts} times - pausing for {self.game_launch_pause_duration/60:.0f} minutes")
                    self.last_game_launch_pause = current_time
                    self.game_launch_attempts = 0  # Reset counter
                    return True  # Continue automation but wait before next launch attempt
                
                # Attempt to launch the game
                launch_success = self.launch_game()
                
                if launch_success:
                    self.logger.info(f"[MODULE_FAILURE] Game launched successfully - re-activating scanner")
                    self.reactivate_centralized_scanner()
                    return True  # Continue automation
                else:
                    self.logger.error(f"[MODULE_FAILURE] Game launch failed (attempt {self.game_launch_attempts}/{self.max_game_launch_attempts})")
                    return True  # Continue automation - will retry on next failure
            
        except Exception as recovery_error:
            self.logger.error(f"[MODULE_FAILURE] Error in crash recovery system: {recovery_error}")
            return True  # Continue automation despite recovery error
    
    def reset_module_failure_count(self, module_name: str):
        """Reset the failure count for a specific module (called on successful execution)"""
        if module_name in self.module_failure_counts:
            if self.module_failure_counts[module_name] > 0:
                self.logger.info(f"[MODULE_SUCCESS] Resetting failure count for module '{module_name}' (was {self.module_failure_counts[module_name]})")
            self.module_failure_counts[module_name] = 0
    
    def pause_automation(self, reason: str):
        """Pause the entire automation system"""
        self.automation_paused = True
        self.pause_reason = reason
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.logger.critical(f"[AUTOMATION_PAUSE] {timestamp} - Automation paused: {reason}")
        
        # Pause the main controller if available
        if self.main_controller and hasattr(self.main_controller, 'pause'):
            self.main_controller.pause()
    
    def resume_automation(self):
        """Resume the automation system"""
        if self.automation_paused:
            self.automation_paused = False
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.logger.info(f"[AUTOMATION_RESUME] {timestamp} - Automation resumed (was paused: {self.pause_reason})")
            self.pause_reason = ""
            
            # Resume the main controller if available
            if self.main_controller and hasattr(self.main_controller, 'resume'):
                self.main_controller.resume()
    
    def get_status(self) -> Dict:
        """Get current crash recovery system status"""
        return {
            'automation_paused': self.automation_paused,
            'pause_reason': self.pause_reason,
            'module_failure_counts': self.module_failure_counts.copy(),
            'game_launch_attempts': self.game_launch_attempts,
            'last_game_launch_pause': self.last_game_launch_pause,
            'game_launch_pause_remaining': max(0, (self.last_game_launch_pause + self.game_launch_pause_duration) - time.time()) if self.last_game_launch_pause > 0 else 0
        }

# Global instance
_crash_recovery_system = None

def get_crash_recovery_system(logger: logging.Logger = None) -> CrashRecoverySystem:
    """Get the global crash recovery system instance"""
    global _crash_recovery_system
    if _crash_recovery_system is None:
        _crash_recovery_system = CrashRecoverySystem(logger)
    return _crash_recovery_system
