"""
Last War Automation Helper v1.0
Main application entry point with Unified Configuration System
"""
import sys
import os
import logging
import warnings

# Suppress libpng warnings globally at application startup
warnings.filterwarnings("ignore", message=".*libpng warning.*")
warnings.filterwarnings("ignore", message=".*sBIT.*")

# Add modules directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from main_controller import MainController
from gui import LastWarGUI
from unified_config_manager import UnifiedConfigManager

# Import all modules
from modules.daily_tasks import DailyTasksModule
from modules.help_click import HelpClickModule
from modules.enhanced_help_click import EnhancedHelpClickModule
from modules.alliance_donation import AllianceDonationModule
from modules.zombie_invasion import ZombieInvasionModule
from modules.map_trade import MapTradeModule
from modules.dig import DigModule
from modules.find_truck import FindTruckModule


def setup_application():
    """Setup the application with all modules using unified configuration"""
    print("Initializing unified configuration system...")

    # Initialize unified configuration manager
    unified_config = UnifiedConfigManager()
    config_data = unified_config.load_config()

    print(f"✅ Unified configuration loaded:")
    print(f"   - Config version: {config_data.get('config_version', 'Unknown')}")
    print(f"   - Modules configured: {len(config_data.get('modules', {}))}")
    print(f"   - System settings: {len(config_data.get('system_settings', {}))}")

    # Create main controller (will automatically use unified config)
    controller = MainController()

    print("Registering modules with unified configuration...")

    # Register all modules (they will automatically load unified config)
    modules = [
        DigModule(),  # Highest priority dig module
        DailyTasksModule(),
        # HelpClickModule(),  # Using enhanced version instead
        EnhancedHelpClickModule(),  # Your custom help click module
        AllianceDonationModule(),
        ZombieInvasionModule(),  # Zombie invasion event automation
        MapTradeModule(),  # Map trade automation every 5 minutes
        FindTruckModule(controller.screen_scanner),  # Manual truck finder for plundering
        # Add more modules as they're implemented
    ]

    for module in modules:
        controller.register_module(module)
        module_config = unified_config.get_module_config(module.name)
        if module_config:
            print(f"   ✅ {module.name}: enabled={module_config.get('enabled')}, priority={module_config.get('priority')}")
        else:
            print(f"   ⚠️ {module.name}: Using legacy configuration")

    print(f"✅ All {len(modules)} modules registered successfully")
    return controller


def main():
    """Main application entry point with unified configuration system"""
    try:
        print("=" * 60)
        print("🚀 LAST WAR AUTOMATION HELPER v1.0")
        print("   Enhanced with Unified Configuration System")
        print("=" * 60)

        # Setup enhanced logging for overnight stability
        import logging.handlers

        # Create logs directory
        os.makedirs('logs', exist_ok=True)

        # Configure rotating file handler with UTF-8 encoding
        file_handler = logging.handlers.RotatingFileHandler(
            'logs/lastwar_automation.log',
            maxBytes=10*1024*1024,  # 10MB max file size
            backupCount=5,  # Keep 5 backup files
            encoding='utf-8'  # Explicit UTF-8 encoding for file handler
        )
        file_handler.setLevel(logging.INFO)

        # Console handler with proper UTF-8 encoding
        import sys

        # Create a custom stream handler that handles Unicode properly
        class UnicodeStreamHandler(logging.StreamHandler):
            def emit(self, record):
                try:
                    msg = self.format(record)
                    # Ensure the message can be encoded properly
                    if hasattr(self.stream, 'encoding') and self.stream.encoding:
                        # Try to encode with the stream's encoding
                        msg.encode(self.stream.encoding, errors='replace')
                    self.stream.write(msg + self.terminator)
                    self.flush()
                except Exception:
                    # Fallback: remove problematic characters
                    try:
                        safe_msg = msg.encode('ascii', errors='ignore').decode('ascii')
                        self.stream.write(safe_msg + self.terminator)
                        self.flush()
                    except Exception:
                        # Last resort: skip the message
                        pass

        console_handler = UnicodeStreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)

        # Set UTF-8 encoding for console if possible
        try:
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            elif hasattr(sys.stdout, 'buffer'):
                # For older Python versions, try to set encoding on buffer
                import codecs
                sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
        except Exception:
            # If all else fails, we'll rely on the UnicodeStreamHandler fallback
            pass

        # Enhanced format with more details - Unicode-safe formatter
        class UnicodeFormatter(logging.Formatter):
            def format(self, record):
                try:
                    # Try normal formatting first
                    return super().format(record)
                except UnicodeEncodeError:
                    # Fallback: remove problematic Unicode characters
                    record.msg = str(record.msg).encode('ascii', errors='ignore').decode('ascii')
                    if hasattr(record, 'args') and record.args:
                        safe_args = []
                        for arg in record.args:
                            safe_arg = str(arg).encode('ascii', errors='ignore').decode('ascii')
                            safe_args.append(safe_arg)
                        record.args = tuple(safe_args)
                    return super().format(record)

        formatter = UnicodeFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)

        # Log startup information
        logging.info("Last War Automation Helper v1.0 - Enhanced with Unified Configuration")
        logging.info("Logs will be saved to logs/lastwar_automation.log")
        logging.info("Log rotation: 10MB max, 5 backup files")

        # Setup modules and controller with unified configuration
        controller = setup_application()

        # Create and run GUI with the controller
        print("\nInitializing GUI with unified configuration support...")
        app = LastWarGUI(controller)

        print("\n🎉 Application started successfully!")
        print("✅ Unified configuration system active")
        print("✅ All modules configured and ready")
        print("✅ Use the GUI to control automation modules")
        print("✅ Config Helper available for advanced configuration")
        print("\n" + "=" * 60)

        # Run the application
        app.run()

    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
    except Exception as e:
        print(f"Error starting application: {str(e)}")
        logging.error(f"Application error: {str(e)}")
    finally:
        print("Application shutting down...")


if __name__ == "__main__":
    main()                                                             