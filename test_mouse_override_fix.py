#!/usr/bin/env python3
"""
Test script to verify mouse override false positive fix
"""

import time
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_old_vs_new_mouse_system():
    """Test the difference between old and new mouse override systems"""
    print("🔧 MOUSE OVERRIDE SYSTEM COMPARISON TEST")
    print("=" * 50)
    
    try:
        from main_controller import MainController
        from mouse_override_controller import get_mouse_controller
        
        print("✅ Controllers imported successfully")
        
        # Initialize main controller
        main_ctrl = MainController()
        print("✅ Main controller initialized")
        
        # Check old system status
        print(f"\n📊 OLD SYSTEM STATUS:")
        print(f"   mouse_detection_enabled: {main_ctrl.mouse_detection_enabled}")
        print(f"   mouse_pause_end_time: {main_ctrl.mouse_pause_end_time}")
        print(f"   module_executing: {main_ctrl.module_executing}")
        
        # Check new system status
        mouse_controller = get_mouse_controller()
        new_status = mouse_controller.get_status()
        print(f"\n📊 NEW SYSTEM STATUS:")
        print(f"   enabled: {new_status['enabled']}")
        print(f"   active_overrides: {new_status['active_overrides']}")
        print(f"   in_cooldown: {new_status['in_cooldown']}")
        
        # Test should_pause_automation method
        print(f"\n🧪 TESTING should_pause_automation():")
        should_pause = mouse_controller.should_pause_automation()
        print(f"   should_pause_automation(): {should_pause}")
        
        if not should_pause:
            print("   ✅ GOOD: Automation should NOT be paused (no false positives)")
        else:
            print("   ⚠️ WARNING: Automation would be paused")
        
        # Test module execution simulation
        print(f"\n🎯 TESTING MODULE EXECUTION SIMULATION:")
        
        print("1. Simulating module start...")
        mouse_controller.disable_mouse_override("Test module execution")
        
        should_pause = mouse_controller.should_pause_automation()
        print(f"   should_pause_automation() during module: {should_pause}")
        
        if should_pause:
            print("   ✅ GOOD: Automation paused during module execution")
        else:
            print("   ❌ BAD: Automation not paused during module execution")
        
        print("2. Simulating module completion...")
        mouse_controller.enable_mouse_override("Test module complete", cooldown=3.0)
        
        should_pause = mouse_controller.should_pause_automation()
        print(f"   should_pause_automation() after module: {should_pause}")
        
        if should_pause:
            print("   ✅ GOOD: Automation paused during cooldown period")
        else:
            print("   ⚠️ WARNING: Automation not paused during cooldown")
        
        # Monitor cooldown
        print("3. Monitoring cooldown period...")
        for i in range(4):
            status = mouse_controller.get_status()
            remaining = status.get('cooldown_remaining', 0)
            should_pause = mouse_controller.should_pause_automation()
            print(f"   Cooldown: {remaining:.1f}s, should_pause: {should_pause}")
            time.sleep(1)
        
        print("\n✅ Mouse override system test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_controller_integration():
    """Test main controller integration with new mouse system"""
    print("\n🎮 MAIN CONTROLLER INTEGRATION TEST")
    print("=" * 40)
    
    try:
        from main_controller import MainController
        
        # Create main controller
        main_ctrl = MainController()
        print("✅ Main controller created")
        
        # Test the main loop logic (without actually running the loop)
        print("\n🔄 Testing main loop mouse override logic...")
        
        # Simulate what happens in the main loop
        should_pause = main_ctrl.mouse_controller.should_pause_automation()
        print(f"   Main loop would pause: {should_pause}")
        
        if not should_pause:
            print("   ✅ GOOD: Main loop would continue (no false positives)")
        else:
            print("   ⚠️ WARNING: Main loop would pause")
        
        # Test module execution flow
        print("\n🚀 Testing module execution flow...")
        
        # Simulate module execution
        print("1. Disabling mouse override for module...")
        main_ctrl.mouse_controller.disable_mouse_override("Test module")
        
        should_pause = main_ctrl.mouse_controller.should_pause_automation()
        print(f"   Main loop would pause during module: {should_pause}")
        
        # Simulate module completion
        print("2. Re-enabling mouse override after module...")
        main_ctrl.mouse_controller.enable_mouse_override("Test module complete", cooldown=3.0)
        
        should_pause = main_ctrl.mouse_controller.should_pause_automation()
        print(f"   Main loop would pause after module: {should_pause}")
        
        print("\n✅ Main controller integration test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_false_positive_elimination():
    """Test that false positives are eliminated"""
    print("\n🎯 FALSE POSITIVE ELIMINATION TEST")
    print("=" * 40)
    
    try:
        from mouse_override_controller import get_mouse_controller
        
        controller = get_mouse_controller()
        print("✅ Mouse controller obtained")
        
        # Test multiple rapid checks (simulating main loop)
        print("\n⚡ Testing rapid should_pause_automation() calls...")
        
        false_positives = 0
        total_checks = 10
        
        for i in range(total_checks):
            should_pause = controller.should_pause_automation()
            if should_pause:
                false_positives += 1
            print(f"   Check {i+1}: should_pause = {should_pause}")
            time.sleep(0.1)  # Small delay between checks
        
        print(f"\n📊 RESULTS:")
        print(f"   Total checks: {total_checks}")
        print(f"   False positives: {false_positives}")
        print(f"   Success rate: {((total_checks - false_positives) / total_checks * 100):.1f}%")
        
        if false_positives == 0:
            print("   ✅ EXCELLENT: No false positives detected!")
        elif false_positives <= 2:
            print("   ✅ GOOD: Very few false positives")
        else:
            print("   ⚠️ WARNING: Multiple false positives detected")
        
        return false_positives == 0
        
    except Exception as e:
        print(f"❌ False positive test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 MOUSE OVERRIDE FALSE POSITIVE FIX TEST")
    print("=" * 60)
    
    # Run tests
    test1_result = test_old_vs_new_mouse_system()
    test2_result = test_main_controller_integration()
    test3_result = test_false_positive_elimination()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    if test1_result:
        print("✅ Mouse System Comparison: PASSED")
        print("   - New system properly replaces old system")
        print("   - Module execution detection working")
        print("   - Cooldown periods working")
    else:
        print("❌ Mouse System Comparison: FAILED")
    
    if test2_result:
        print("✅ Main Controller Integration: PASSED")
        print("   - Main loop integration working")
        print("   - Module execution flow working")
    else:
        print("❌ Main Controller Integration: FAILED")
    
    if test3_result:
        print("✅ False Positive Elimination: PASSED")
        print("   - No false positives detected")
        print("   - System stable under rapid checks")
    else:
        print("❌ False Positive Elimination: FAILED")
    
    print("\n" + "=" * 60)
    
    if test1_result and test2_result and test3_result:
        print("🎯 ALL TESTS PASSED!")
        print("\n✅ MOUSE OVERRIDE FALSE POSITIVES ELIMINATED!")
        print("\nThe system now:")
        print("• Uses new MouseOverrideController instead of old detection")
        print("• Only pauses during actual module execution")
        print("• Only pauses during cooldown periods after modules")
        print("• No longer triggers false positives from automation clicks")
        print("• Properly handles ESC sequences without interference")
    else:
        print("⚠️ SOME TESTS FAILED")
        print("\nIf issues persist:")
        print("1. Check that old mouse detection is disabled")
        print("2. Verify new controller is properly integrated")
        print("3. Monitor logs for any remaining old system messages")
    
    print("\nPress Enter to exit...")
    input()
