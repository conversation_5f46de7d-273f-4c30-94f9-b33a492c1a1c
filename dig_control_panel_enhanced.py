"""
Enhanced Dig Module Control Panel
Matches the functionality and usability of the map config interface with:
- Visual template detection with bounding boxes
- Click coordinate capture and editing with ENTER key
- Template confidence threshold adjustment
- Real-time detection testing
- Step-by-step execution testing
- OCR region configuration and testing
- Screenshot capture for template creation
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import time
import threading
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime
import pyautogui
import cv2
import numpy as np
from PIL import Image, ImageTk
import keyboard

class EnhancedDigControlPanel:
    def __init__(self, parent_window, main_controller=None, dig_module=None):
        self.parent = parent_window
        self.main_controller = main_controller
        self.dig_module = dig_module
        self.logger = logging.getLogger("EnhancedDigControlPanel")

        # Configuration
        self.config_file = "dig_module_config.json"
        self.config_data = {}
        
        # Template and coordinate capture
        self.capturing_mouse = False
        self.template_capture_mode = False
        self.captured_image = None
        self.current_step = None
        self.current_template = None
        
        # GUI components
        self.window = None
        self.notebook = None
        self.step_vars = {}
        self.coordinate_vars = {}
        self.template_vars = {}
        
        # Results and preview
        self.results_text = None
        self.template_preview_label = None
        self.detection_canvas = None
        
        # Load configuration and initialize
        self._load_configuration()
        self._initialize_dependencies()
        self._setup_keyboard_listener()

    def _load_configuration(self):
        """Load dig module configuration"""
        try:
            with open(self.config_file, 'r') as f:
                self.config_data = json.load(f)
            self.logger.info(f"Loaded dig configuration from {self.config_file}")
        except FileNotFoundError:
            self.logger.warning(f"Configuration file {self.config_file} not found, using defaults")
            self.config_data = self._get_default_configuration()
        except Exception as e:
            self.logger.error(f"Error loading configuration: {e}")
            self.config_data = self._get_default_configuration()

    def _get_default_configuration(self):
        """Get default configuration structure"""
        return {
            "config_version": "1.0.0",
            "last_updated": datetime.now().isoformat(),
            "description": "Enhanced dig module configuration with visual testing",
            "execution_steps": {
                "step_1_dig_icon": {
                    "name": "Detect Dig Icon",
                    "templates": ["dig_icon"],
                    "click_coordinates": {"x": 0, "y": 0},
                    "delay_after": 1.5,
                    "confidence_threshold": 0.8,
                    "scan_region": {"x": 0, "y": 0, "width": 2560, "height": 1440}
                },
                "step_2_open_chat": {
                    "name": "Open Chat",
                    "templates": ["chat_click"],
                    "click_coordinates": {"x": 0, "y": 0},
                    "delay_after": 2.0,
                    "confidence_threshold": 0.8,
                    "scan_region": {"x": 0, "y": 0, "width": 2560, "height": 1440}
                },
                "step_3_find_treasure": {
                    "name": "Find Treasure Links",
                    "templates": ["dig_up_treasure", "test_flight_treasure"],
                    "click_coordinates": {"x": 0, "y": 0},
                    "delay_after": 1.0,
                    "confidence_threshold": 0.5,
                    "scan_region": {"x": 400, "y": 600, "width": 800, "height": 600}
                },
                "step_4_verify_base": {
                    "name": "Verify Base Navigation",
                    "templates": ["Base"],
                    "click_coordinates": {"x": 0, "y": 0},
                    "delay_after": 1.0,
                    "confidence_threshold": 0.8,
                    "scan_region": {"x": 0, "y": 0, "width": 2560, "height": 1440}
                },
                "step_5_deploy_squad": {
                    "name": "Deploy Squad",
                    "templates": ["send_to_dig"],
                    "click_coordinates": {"x": 0, "y": 0},
                    "delay_after": 2.0,
                    "confidence_threshold": 0.8,
                    "scan_region": {"x": 0, "y": 0, "width": 2560, "height": 1440}
                },
                "step_6_timer_management": {
                    "name": "Timer Management",
                    "templates": ["timer_1"],
                    "click_coordinates": {"x": 0, "y": 0},
                    "delay_after": 0.1,
                    "confidence_threshold": 0.7,
                    "scan_region": {"x": 1200, "y": 300, "width": 400, "height": 200},
                    "ocr_settings": {
                        "enabled": True,
                        "threshold_seconds": 10,
                        "click_duration": 20,
                        "preprocessing": "white_on_gray"
                    }
                }
            },
            "template_settings": {
                "default_confidence": 0.8,
                "multi_scale_detection": True,
                "scale_range": [0.8, 1.2],
                "max_detections": 5
            },
            "ocr_settings": {
                "engine": "tesseract",
                "psm_mode": 6,
                "preprocessing_methods": ["invert", "contrast", "denoise"],
                "confidence_threshold": 0.6
            }
        }

    def _initialize_dependencies(self):
        """Initialize screen scanner and dig module"""
        try:
            # Initialize screen scanner for template testing
            from screen_scanner import ScreenScanner
            self.screen_scanner = ScreenScanner(
                templates_path='templates/',
                screenshots_path='screenshots/'
            )
            self.logger.info("Screen scanner initialized")
        except ImportError:
            self.screen_scanner = None
            self.logger.warning("Screen scanner not available")

        try:
            # Initialize or use provided dig module
            if not self.dig_module:
                from dig_module_refactored import DigModule
                self.dig_module = DigModule()
            self.logger.info("Dig module initialized")
        except ImportError:
            self.logger.warning("Dig module not available")

    def _setup_keyboard_listener(self):
        """Setup keyboard listener for ENTER key coordinate capture"""
        def on_enter_pressed(_):
            if self.capturing_mouse:
                self.capture_mouse_position()
            elif self.template_capture_mode:
                self.capture_template_at_mouse()

        keyboard.on_press_key('enter', on_enter_pressed)

    @property
    def config(self):
        """Get the current configuration data"""
        return self.config_data

    def show_control_panel(self):
        """Show the enhanced dig control panel"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return

        self.window = tk.Toplevel(self.parent)
        self.window.title("🔧 Enhanced Dig Module Control Panel")
        self.window.geometry("1400x900")
        self.window.configure(bg='#2b2b2b')

        # Create main notebook with enhanced tabs
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create enhanced tabs
        self._create_step_configuration_tab()
        self._create_template_testing_tab()
        self._create_coordinate_capture_tab()
        self._create_real_time_monitor_tab()
        self._create_advanced_settings_tab()

        # Bottom control panel
        self._create_bottom_controls()

    def _create_step_configuration_tab(self):
        """Create enhanced step configuration tab with coordinate editing"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="⚙️ Step Configuration")

        # Create scrollable frame
        canvas = tk.Canvas(frame, bg='#2b2b2b')
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Create step configuration sections
        steps = self.config_data.get('execution_steps', {})
        for step_key, step_config in steps.items():
            self._create_step_section(scrollable_frame, step_key, step_config)

    def _create_step_section(self, parent, step_key, step_config):
        """Create a comprehensive step configuration section"""
        # Main step frame
        step_frame = ttk.LabelFrame(parent, text=f"📋 {step_config.get('name', step_key)}")
        step_frame.pack(fill=tk.X, padx=5, pady=5)

        # Create grid layout
        row = 0

        # Templates section
        ttk.Label(step_frame, text="Templates:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        templates_text = ", ".join(step_config.get('templates', []))
        ttk.Label(step_frame, text=templates_text, foreground="blue").grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Button(step_frame, text="🔍 Test Templates", 
                  command=lambda sk=step_key: self._test_step_templates(sk)).grid(row=row, column=2, padx=5, pady=2)
        row += 1

        # Click coordinates section with capture
        ttk.Label(step_frame, text="Click Coordinates:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        
        coord_frame = ttk.Frame(step_frame)
        coord_frame.grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        
        # X coordinate
        ttk.Label(coord_frame, text="X:").pack(side=tk.LEFT)
        x_var = tk.IntVar(value=step_config.get('click_coordinates', {}).get('x', 0))
        self.coordinate_vars[f"{step_key}_x"] = x_var
        x_spinbox = ttk.Spinbox(coord_frame, from_=0, to=2560, width=8, textvariable=x_var)
        x_spinbox.pack(side=tk.LEFT, padx=2)
        
        # Y coordinate
        ttk.Label(coord_frame, text="Y:").pack(side=tk.LEFT, padx=(10, 0))
        y_var = tk.IntVar(value=step_config.get('click_coordinates', {}).get('y', 0))
        self.coordinate_vars[f"{step_key}_y"] = y_var
        y_spinbox = ttk.Spinbox(coord_frame, from_=0, to=1440, width=8, textvariable=y_var)
        y_spinbox.pack(side=tk.LEFT, padx=2)
        
        # Capture button
        ttk.Button(step_frame, text="📍 Capture (ENTER)", 
                  command=lambda sk=step_key: self._start_coordinate_capture(sk)).grid(row=row, column=2, padx=5, pady=2)
        row += 1

        # Additional step controls
        controls_frame = ttk.Frame(step_frame)
        controls_frame.grid(row=row, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)
        
        ttk.Button(controls_frame, text="▶️ Test Step", 
                  command=lambda sk=step_key: self._test_individual_step(sk)).pack(side=tk.LEFT, padx=2)
        ttk.Button(controls_frame, text="📸 Screenshot", 
                  command=lambda sk=step_key: self._capture_step_screenshot(sk)).pack(side=tk.LEFT, padx=2)
        ttk.Button(controls_frame, text="🔧 Advanced", 
                  command=lambda sk=step_key: self._show_step_advanced_settings(sk)).pack(side=tk.LEFT, padx=2)

    def _create_template_testing_tab(self):
        """Create enhanced template testing tab with visual feedback"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="🔍 Template Testing")

        # Left panel - Template selection and controls
        left_frame = ttk.Frame(frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)

        # Template selection
        ttk.Label(left_frame, text="Select Template:").pack(anchor=tk.W, pady=2)
        self.template_var = tk.StringVar()
        template_combo = ttk.Combobox(left_frame, textvariable=self.template_var, width=30)
        template_combo['values'] = self._get_all_templates()
        template_combo.pack(fill=tk.X, pady=2)
        template_combo.bind('<<ComboboxSelected>>', self._on_template_selected)

        # Confidence threshold
        ttk.Label(left_frame, text="Confidence Threshold:").pack(anchor=tk.W, pady=(10, 2))
        self.confidence_var = tk.DoubleVar(value=0.8)
        confidence_scale = ttk.Scale(left_frame, from_=0.1, to=1.0, variable=self.confidence_var, orient=tk.HORIZONTAL)
        confidence_scale.pack(fill=tk.X, pady=2)
        
        confidence_label = ttk.Label(left_frame, text="0.80")
        confidence_label.pack(anchor=tk.W)
        
        def update_confidence_label(*args):
            confidence_label.config(text=f"{self.confidence_var.get():.2f}")
        self.confidence_var.trace('w', update_confidence_label)

        # Testing controls
        ttk.Button(left_frame, text="🔍 Test Template", 
                  command=self._test_selected_template).pack(fill=tk.X, pady=5)
        ttk.Button(left_frame, text="📸 Live Capture", 
                  command=self._toggle_template_capture).pack(fill=tk.X, pady=2)
        ttk.Button(left_frame, text="🎯 Multi-Scale Test", 
                  command=self._multi_scale_template_test).pack(fill=tk.X, pady=2)

        # Right panel - Results and preview
        right_frame = ttk.Frame(frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Template preview
        preview_frame = ttk.LabelFrame(right_frame, text="Template Preview")
        preview_frame.pack(fill=tk.X, pady=2)
        
        self.template_preview_label = ttk.Label(preview_frame, text="No template selected")
        self.template_preview_label.pack(pady=10)

        # Detection results
        results_frame = ttk.LabelFrame(right_frame, text="Detection Results")
        results_frame.pack(fill=tk.BOTH, expand=True, pady=2)
        
        self.results_text = tk.Text(results_frame, height=15, bg='#1e1e1e', fg='white', font=('Consolas', 10))
        results_scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=results_scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def _get_all_templates(self):
        """Get all available templates from steps and template folder"""
        templates = set()
        
        # Get templates from step configuration
        steps = self.config_data.get('execution_steps', {})
        for step_config in steps.values():
            step_templates = step_config.get('templates', [])
            templates.update(step_templates)
        
        # Get templates from template folder
        template_folder = 'templates'
        if os.path.exists(template_folder):
            for file in os.listdir(template_folder):
                if file.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp')):
                    templates.add(os.path.splitext(file)[0])
        
        return sorted(list(templates))

    def _on_template_selected(self, event=None):
        """Handle template selection"""
        template_name = self.template_var.get()
        if template_name:
            self._load_template_preview(template_name)

    def _load_template_preview(self, template_name):
        """Load and display template preview"""
        try:
            template_path = os.path.join('templates', f"{template_name}.png")
            if not os.path.exists(template_path):
                # Try other extensions
                for ext in ['.jpg', '.jpeg', '.bmp']:
                    alt_path = os.path.join('templates', f"{template_name}{ext}")
                    if os.path.exists(alt_path):
                        template_path = alt_path
                        break
            
            if os.path.exists(template_path):
                # Load and resize image for preview
                image = Image.open(template_path)
                image.thumbnail((150, 150), Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(image)
                
                self.template_preview_label.configure(image=photo, text="")
                self.template_preview_label.image = photo  # Keep a reference
            else:
                self.template_preview_label.configure(image="", text=f"Template not found:\n{template_name}")
                
        except Exception as e:
            self.template_preview_label.configure(image="", text=f"Error loading template:\n{str(e)}")

    def _test_selected_template(self):
        """Test the selected template with visual feedback"""
        template_name = self.template_var.get()
        if not template_name:
            messagebox.showwarning("Warning", "Please select a template to test")
            return
        
        self._test_template_with_feedback(template_name, self.confidence_var.get())

    def _test_template_with_feedback(self, template_name, confidence_threshold):
        """Test template with comprehensive feedback"""
        try:
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, f"🔍 Testing template: {template_name}\n")
            self.results_text.insert(tk.END, f"Confidence threshold: {confidence_threshold:.2f}\n")
            self.results_text.insert(tk.END, "=" * 50 + "\n\n")
            
            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
            
            # Load template
            template_path = os.path.join('templates', f"{template_name}.png")
            if not os.path.exists(template_path):
                for ext in ['.jpg', '.jpeg', '.bmp']:
                    alt_path = os.path.join('templates', f"{template_name}{ext}")
                    if os.path.exists(alt_path):
                        template_path = alt_path
                        break
            
            if not os.path.exists(template_path):
                self.results_text.insert(tk.END, f"❌ Template file not found: {template_path}\n")
                return
            
            template_cv = cv2.imread(template_path)
            if template_cv is None:
                self.results_text.insert(tk.END, f"❌ Could not load template: {template_path}\n")
                return
            
            # Perform template matching
            result = cv2.matchTemplate(screenshot_cv, template_cv, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            # Get template dimensions
            template_h, template_w = template_cv.shape[:2]
            
            # Display results
            self.results_text.insert(tk.END, f"📊 Detection Results:\n")
            self.results_text.insert(tk.END, f"   Best confidence: {max_val:.4f}\n")
            self.results_text.insert(tk.END, f"   Location: {max_loc}\n")
            self.results_text.insert(tk.END, f"   Template size: {template_w}x{template_h}\n\n")
            
            if max_val >= confidence_threshold:
                center_x = max_loc[0] + template_w // 2
                center_y = max_loc[1] + template_h // 2
                
                self.results_text.insert(tk.END, f"✅ TEMPLATE DETECTED!\n")
                self.results_text.insert(tk.END, f"   Click coordinates: ({center_x}, {center_y})\n")
                self.results_text.insert(tk.END, f"   Quality: {self._get_confidence_quality(max_val)}\n\n")
                
                # Find all matches above threshold
                locations = np.where(result >= confidence_threshold)
                matches = list(zip(*locations[::-1]))
                
                if len(matches) > 1:
                    self.results_text.insert(tk.END, f"🔍 Found {len(matches)} matches above threshold:\n")
                    for i, (x, y) in enumerate(matches[:5]):  # Show first 5
                        conf = result[y, x]
                        self.results_text.insert(tk.END, f"   Match {i+1}: ({x}, {y}) - {conf:.4f}\n")
                    if len(matches) > 5:
                        self.results_text.insert(tk.END, f"   ... and {len(matches) - 5} more\n")
            else:
                self.results_text.insert(tk.END, f"❌ Template not detected (below threshold)\n")
                self.results_text.insert(tk.END, f"   Suggestions:\n")
                self.results_text.insert(tk.END, f"   • Lower confidence threshold (current: {confidence_threshold:.2f})\n")
                self.results_text.insert(tk.END, f"   • Try multi-scale detection\n")
                self.results_text.insert(tk.END, f"   • Check if template matches current screen\n")
            
            self.results_text.insert(tk.END, f"\n⏰ Test completed at {datetime.now().strftime('%H:%M:%S')}\n")
            
        except Exception as e:
            self.results_text.insert(tk.END, f"❌ Error during template testing: {str(e)}\n")
            import traceback
            self.results_text.insert(tk.END, f"Traceback:\n{traceback.format_exc()}\n")

    def _get_confidence_quality(self, confidence):
        """Get quality description for confidence score"""
        if confidence >= 0.95:
            return "Excellent"
        elif confidence >= 0.85:
            return "Very Good"
        elif confidence >= 0.75:
            return "Good"
        elif confidence >= 0.65:
            return "Acceptable"
        else:
            return "Poor"

    def _multi_scale_template_test(self):
        """Test template at multiple scales"""
        template_name = self.template_var.get()
        if not template_name:
            messagebox.showwarning("Warning", "Please select a template to test")
            return

        try:
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, f"🔍 Multi-scale testing: {template_name}\n")
            self.results_text.insert(tk.END, "=" * 50 + "\n\n")

            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            # Load template
            template_path = os.path.join('templates', f"{template_name}.png")
            if not os.path.exists(template_path):
                for ext in ['.jpg', '.jpeg', '.bmp']:
                    alt_path = os.path.join('templates', f"{template_name}{ext}")
                    if os.path.exists(alt_path):
                        template_path = alt_path
                        break

            if not os.path.exists(template_path):
                self.results_text.insert(tk.END, f"❌ Template file not found\n")
                return

            template_cv = cv2.imread(template_path)
            if template_cv is None:
                self.results_text.insert(tk.END, f"❌ Could not load template\n")
                return

            # Test different scales
            scales = [0.5, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.5]
            best_match = None
            best_confidence = 0

            for scale in scales:
                # Resize template
                new_width = int(template_cv.shape[1] * scale)
                new_height = int(template_cv.shape[0] * scale)

                if new_width < 10 or new_height < 10:
                    continue
                if new_width > screenshot_cv.shape[1] or new_height > screenshot_cv.shape[0]:
                    continue

                scaled_template = cv2.resize(template_cv, (new_width, new_height))

                # Perform matching
                result = cv2.matchTemplate(screenshot_cv, scaled_template, cv2.TM_CCOEFF_NORMED)
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                self.results_text.insert(tk.END, f"Scale {scale:.1f}: {max_val:.4f} at {max_loc}\n")

                if max_val > best_confidence:
                    best_confidence = max_val
                    best_match = {
                        'scale': scale,
                        'confidence': max_val,
                        'location': max_loc,
                        'size': (new_width, new_height)
                    }

            self.results_text.insert(tk.END, f"\n🏆 Best match:\n")
            if best_match:
                self.results_text.insert(tk.END, f"   Scale: {best_match['scale']:.1f}\n")
                self.results_text.insert(tk.END, f"   Confidence: {best_match['confidence']:.4f}\n")
                self.results_text.insert(tk.END, f"   Location: {best_match['location']}\n")
                self.results_text.insert(tk.END, f"   Size: {best_match['size']}\n")

                center_x = best_match['location'][0] + best_match['size'][0] // 2
                center_y = best_match['location'][1] + best_match['size'][1] // 2
                self.results_text.insert(tk.END, f"   Click point: ({center_x}, {center_y})\n")
            else:
                self.results_text.insert(tk.END, "   No matches found\n")

        except Exception as e:
            self.results_text.insert(tk.END, f"❌ Error during multi-scale testing: {str(e)}\n")

    def _toggle_template_capture(self):
        """Toggle template capture mode"""
        self.template_capture_mode = not self.template_capture_mode

        if self.template_capture_mode:
            messagebox.showinfo("Template Capture",
                              "Template capture mode enabled!\n\n"
                              "1. Move mouse to desired location\n"
                              "2. Press ENTER to capture template\n"
                              "3. Template will be captured at mouse position")
        else:
            messagebox.showinfo("Template Capture", "Template capture mode disabled")

    def capture_template_at_mouse(self):
        """Capture template at current mouse position (called by Enter key)"""
        if not self.template_capture_mode:
            return

        try:
            x, y = pyautogui.position()
            size = 50  # Default capture size

            # Calculate capture region
            left = x - size // 2
            top = y - size // 2

            screenshot = pyautogui.screenshot(region=(left, top, size, size))
            self.captured_image = screenshot

            # Display preview
            image_copy = screenshot.copy()
            image_copy.thumbnail((150, 150), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(image_copy)

            self.template_preview_label.configure(image=photo, text="")
            self.template_preview_label.image = photo

            # Update results
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, f"📸 Template captured at ({x}, {y})\n")
            self.results_text.insert(tk.END, f"Size: {size}x{size} pixels\n")
            self.results_text.insert(tk.END, f"Time: {datetime.now().strftime('%H:%M:%S')}\n\n")
            self.results_text.insert(tk.END, "Use 'Save Template' to save this capture\n")

        except Exception as e:
            messagebox.showerror("Error", f"Could not capture template: {e}")

    def _create_coordinate_capture_tab(self):
        """Create coordinate capture and editing tab"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="📍 Coordinate Capture")

        # Left panel - Step selection and coordinates
        left_frame = ttk.Frame(frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)

        # Step selection
        ttk.Label(left_frame, text="Select Step:").pack(anchor=tk.W, pady=2)
        self.step_var = tk.StringVar()
        step_combo = ttk.Combobox(left_frame, textvariable=self.step_var, width=30)
        steps = list(self.config_data.get('execution_steps', {}).keys())
        step_combo['values'] = steps
        step_combo.pack(fill=tk.X, pady=2)
        step_combo.bind('<<ComboboxSelected>>', self._on_step_selected)

        # Current coordinates display
        coord_frame = ttk.LabelFrame(left_frame, text="Current Coordinates")
        coord_frame.pack(fill=tk.X, pady=10)

        # X coordinate
        ttk.Label(coord_frame, text="X:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.current_x_var = tk.IntVar()
        x_spinbox = ttk.Spinbox(coord_frame, from_=0, to=2560, width=10, textvariable=self.current_x_var)
        x_spinbox.grid(row=0, column=1, padx=5, pady=2)

        # Y coordinate
        ttk.Label(coord_frame, text="Y:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.current_y_var = tk.IntVar()
        y_spinbox = ttk.Spinbox(coord_frame, from_=0, to=1440, width=10, textvariable=self.current_y_var)
        y_spinbox.grid(row=1, column=1, padx=5, pady=2)

        # Capture controls
        capture_frame = ttk.LabelFrame(left_frame, text="Coordinate Capture")
        capture_frame.pack(fill=tk.X, pady=10)

        ttk.Button(capture_frame, text="📍 Start Capture (ENTER)",
                  command=self._start_coordinate_capture_mode).pack(fill=tk.X, pady=2)
        ttk.Button(capture_frame, text="🎯 Test Current Position",
                  command=self._test_current_coordinates).pack(fill=tk.X, pady=2)
        ttk.Button(capture_frame, text="💾 Save Coordinates",
                  command=self._save_current_coordinates).pack(fill=tk.X, pady=2)

        # Right panel - Live preview and results
        right_frame = ttk.Frame(frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Live mouse position
        mouse_frame = ttk.LabelFrame(right_frame, text="Live Mouse Position")
        mouse_frame.pack(fill=tk.X, pady=2)

        self.mouse_pos_label = ttk.Label(mouse_frame, text="Move mouse to see coordinates", font=('Consolas', 12))
        self.mouse_pos_label.pack(pady=10)

        # Screenshot preview
        preview_frame = ttk.LabelFrame(right_frame, text="Coordinate Preview")
        preview_frame.pack(fill=tk.X, pady=2)

        self.coord_preview_label = ttk.Label(preview_frame, text="No coordinates selected")
        self.coord_preview_label.pack(pady=10)

        # Results
        results_frame = ttk.LabelFrame(right_frame, text="Capture Results")
        results_frame.pack(fill=tk.BOTH, expand=True, pady=2)

        self.coord_results_text = tk.Text(results_frame, height=10, bg='#1e1e1e', fg='white', font=('Consolas', 10))
        coord_scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.coord_results_text.yview)
        self.coord_results_text.configure(yscrollcommand=coord_scrollbar.set)

        self.coord_results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        coord_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Start mouse tracking
        self._start_mouse_tracking()

    def _on_step_selected(self, event=None):
        """Handle step selection for coordinate editing"""
        step_key = self.step_var.get()
        if step_key:
            step_config = self.config_data.get('execution_steps', {}).get(step_key, {})
            coords = step_config.get('click_coordinates', {'x': 0, 'y': 0})

            self.current_x_var.set(coords.get('x', 0))
            self.current_y_var.set(coords.get('y', 0))

            # Update preview
            self._update_coordinate_preview()

    def _start_mouse_tracking(self):
        """Start tracking mouse position"""
        def track_mouse():
            try:
                x, y = pyautogui.position()
                self.mouse_pos_label.config(text=f"Mouse: ({x}, {y})")
                self.window.after(100, track_mouse)  # Update every 100ms
            except:
                pass  # Window might be closed

        if self.window and self.window.winfo_exists():
            track_mouse()

    def _start_coordinate_capture_mode(self):
        """Start coordinate capture mode"""
        step_key = self.step_var.get()
        if not step_key:
            messagebox.showwarning("Warning", "Please select a step first")
            return

        self.capturing_mouse = True
        self.current_step = step_key

        messagebox.showinfo("Coordinate Capture",
                          f"Coordinate capture started for: {step_key}\n\n"
                          "1. Move mouse to desired click position\n"
                          "2. Press ENTER to capture coordinates\n"
                          "3. Coordinates will be automatically saved")

    def capture_mouse_position(self):
        """Capture current mouse position (called by Enter key)"""
        if not self.capturing_mouse or not self.current_step:
            return

        try:
            x, y = pyautogui.position()

            # Update GUI
            self.current_x_var.set(x)
            self.current_y_var.set(y)

            # Update configuration
            step_config = self.config_data.get('execution_steps', {}).get(self.current_step, {})
            step_config['click_coordinates'] = {'x': x, 'y': y}

            # Update preview
            self._update_coordinate_preview()

            # Update results
            self.coord_results_text.delete(1.0, tk.END)
            self.coord_results_text.insert(tk.END, f"📍 Captured coordinates for {self.current_step}\n")
            self.coord_results_text.insert(tk.END, f"Position: ({x}, {y})\n")
            self.coord_results_text.insert(tk.END, f"Time: {datetime.now().strftime('%H:%M:%S')}\n\n")

            # Test the position immediately
            self._test_coordinates_at_position(x, y)

            # Stop capturing
            self.capturing_mouse = False
            self.current_step = None

            messagebox.showinfo("Captured", f"Coordinates captured: ({x}, {y})")

        except Exception as e:
            messagebox.showerror("Error", f"Could not capture coordinates: {e}")

    def _update_coordinate_preview(self):
        """Update coordinate preview"""
        try:
            x = self.current_x_var.get()
            y = self.current_y_var.get()

            # Take small screenshot around coordinates
            size = 100
            left = max(0, x - size // 2)
            top = max(0, y - size // 2)

            screenshot = pyautogui.screenshot(region=(left, top, size, size))

            # Draw crosshair at center
            import PIL.ImageDraw as ImageDraw
            draw = ImageDraw.Draw(screenshot)
            center_x = min(x - left, size - 1)
            center_y = min(y - top, size - 1)

            # Draw crosshair
            draw.line([(center_x - 10, center_y), (center_x + 10, center_y)], fill='red', width=2)
            draw.line([(center_x, center_y - 10), (center_x, center_y + 10)], fill='red', width=2)

            # Resize for display
            screenshot.thumbnail((150, 150), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(screenshot)

            self.coord_preview_label.configure(image=photo, text="")
            self.coord_preview_label.image = photo

        except Exception as e:
            self.coord_preview_label.configure(image="", text=f"Preview error: {str(e)}")

    def _test_current_coordinates(self):
        """Test current coordinates"""
        x = self.current_x_var.get()
        y = self.current_y_var.get()
        self._test_coordinates_at_position(x, y)

    def _test_coordinates_at_position(self, x, y):
        """Test what's at the given coordinates"""
        try:
            # Take screenshot around the coordinates
            size = 50
            left = max(0, x - size // 2)
            top = max(0, y - size // 2)

            screenshot = pyautogui.screenshot(region=(left, top, size, size))

            # Add to results
            self.coord_results_text.insert(tk.END, f"🔍 Testing position ({x}, {y})\n")
            self.coord_results_text.insert(tk.END, f"Screenshot region: ({left}, {top}, {size}, {size})\n")

            # If we have a selected step, test its templates at this position
            step_key = self.step_var.get()
            if step_key:
                step_config = self.config_data.get('execution_steps', {}).get(step_key, {})
                templates = step_config.get('templates', [])

                if templates:
                    self.coord_results_text.insert(tk.END, f"Testing templates: {', '.join(templates)}\n")

                    for template_name in templates:
                        confidence = self._test_template_at_coordinates(template_name, x, y)
                        if confidence:
                            self.coord_results_text.insert(tk.END, f"  {template_name}: {confidence:.4f}\n")
                        else:
                            self.coord_results_text.insert(tk.END, f"  {template_name}: Not found\n")

            self.coord_results_text.insert(tk.END, "\n")

        except Exception as e:
            self.coord_results_text.insert(tk.END, f"❌ Error testing coordinates: {str(e)}\n")

    def _test_template_at_coordinates(self, template_name, x, y):
        """Test if template exists at given coordinates"""
        try:
            template_path = os.path.join('templates', f"{template_name}.png")
            if not os.path.exists(template_path):
                return None

            template_cv = cv2.imread(template_path)
            if template_cv is None:
                return None

            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            # Perform template matching
            result = cv2.matchTemplate(screenshot_cv, template_cv, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            return max_val

        except Exception:
            return None

    def _save_current_coordinates(self):
        """Save current coordinates to configuration"""
        step_key = self.step_var.get()
        if not step_key:
            messagebox.showwarning("Warning", "Please select a step first")
            return

        try:
            x = self.current_x_var.get()
            y = self.current_y_var.get()

            # Update configuration
            if 'execution_steps' not in self.config_data:
                self.config_data['execution_steps'] = {}

            if step_key not in self.config_data['execution_steps']:
                self.config_data['execution_steps'][step_key] = {}

            self.config_data['execution_steps'][step_key]['click_coordinates'] = {'x': x, 'y': y}

            # Save to file
            self._save_configuration()

            messagebox.showinfo("Saved", f"Coordinates saved for {step_key}: ({x}, {y})")

        except Exception as e:
            messagebox.showerror("Error", f"Could not save coordinates: {e}")

    def _save_configuration(self):
        """Save configuration to file"""
        try:
            self.config_data['last_updated'] = datetime.now().isoformat()
            with open(self.config_file, 'w') as f:
                json.dump(self.config_data, f, indent=2)
            self.logger.info(f"Configuration saved to {self.config_file}")
        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
            raise

    def _create_real_time_monitor_tab(self):
        """Create real-time monitoring tab"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="📡 Real-Time Monitor")

        # Control panel
        control_frame = ttk.Frame(frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        self.monitoring_var = tk.BooleanVar()
        ttk.Checkbutton(control_frame, text="Enable Live Monitoring",
                       variable=self.monitoring_var,
                       command=self._toggle_monitoring).pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="📸 Take Screenshot",
                  command=self._take_monitoring_screenshot).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="🔄 Refresh",
                  command=self._refresh_monitoring).pack(side=tk.LEFT, padx=5)

        # Status display
        status_frame = ttk.LabelFrame(frame, text="Current Status")
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        self.status_text = tk.Text(status_frame, height=8, bg='#1e1e1e', fg='white', font=('Consolas', 10))
        status_scrollbar = ttk.Scrollbar(status_frame, orient="vertical", command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=status_scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        status_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Live detection results
        detection_frame = ttk.LabelFrame(frame, text="Live Template Detection")
        detection_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.live_results_text = tk.Text(detection_frame, bg='#1e1e1e', fg='white', font=('Consolas', 10))
        live_scrollbar = ttk.Scrollbar(detection_frame, orient="vertical", command=self.live_results_text.yview)
        self.live_results_text.configure(yscrollcommand=live_scrollbar.set)

        self.live_results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        live_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def _toggle_monitoring(self):
        """Toggle real-time monitoring"""
        if self.monitoring_var.get():
            self._start_monitoring()
        else:
            self._stop_monitoring()

    def _start_monitoring(self):
        """Start real-time monitoring"""
        self.monitoring_active = True
        self.status_text.delete(1.0, tk.END)
        self.status_text.insert(tk.END, "🟢 Real-time monitoring started\n")
        self.status_text.insert(tk.END, f"Started at: {datetime.now().strftime('%H:%M:%S')}\n\n")

        # Start monitoring thread
        threading.Thread(target=self._monitoring_loop, daemon=True).start()

    def _stop_monitoring(self):
        """Stop real-time monitoring"""
        self.monitoring_active = False
        self.status_text.insert(tk.END, f"🔴 Monitoring stopped at: {datetime.now().strftime('%H:%M:%S')}\n")

    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                # Test all templates from all steps
                all_templates = set()
                steps = self.config_data.get('execution_steps', {})
                for step_config in steps.values():
                    step_templates = step_config.get('templates', [])
                    all_templates.update(step_templates)

                if all_templates:
                    results = []
                    for template_name in all_templates:
                        confidence = self._quick_template_test(template_name)
                        if confidence and confidence > 0.5:  # Only show decent matches
                            results.append((template_name, confidence))

                    # Update GUI in main thread
                    if results:
                        self.window.after(0, self._update_live_results, results)

                time.sleep(2)  # Check every 2 seconds

            except Exception as e:
                self.window.after(0, self._update_monitoring_error, str(e))
                break

    def _quick_template_test(self, template_name):
        """Quick template test for monitoring"""
        try:
            template_path = os.path.join('templates', f"{template_name}.png")
            if not os.path.exists(template_path):
                return None

            template_cv = cv2.imread(template_path)
            if template_cv is None:
                return None

            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            # Quick template matching
            result = cv2.matchTemplate(screenshot_cv, template_cv, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            return max_val

        except Exception:
            return None

    def _update_live_results(self, results):
        """Update live results display"""
        self.live_results_text.delete(1.0, tk.END)
        self.live_results_text.insert(tk.END, f"🔍 Live Detection Results - {datetime.now().strftime('%H:%M:%S')}\n")
        self.live_results_text.insert(tk.END, "=" * 50 + "\n\n")

        if results:
            # Sort by confidence
            results.sort(key=lambda x: x[1], reverse=True)

            for template_name, confidence in results:
                quality = self._get_confidence_quality(confidence)
                self.live_results_text.insert(tk.END, f"✅ {template_name}: {confidence:.4f} ({quality})\n")
        else:
            self.live_results_text.insert(tk.END, "No templates detected above threshold\n")

    def _update_monitoring_error(self, error_msg):
        """Update monitoring with error message"""
        self.live_results_text.insert(tk.END, f"❌ Monitoring error: {error_msg}\n")
        self.monitoring_active = False
        self.monitoring_var.set(False)

    def _take_monitoring_screenshot(self):
        """Take screenshot for monitoring"""
        try:
            screenshot = pyautogui.screenshot()
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"dig_monitor_screenshot_{timestamp}.png"
            screenshot.save(filename)

            self.status_text.insert(tk.END, f"📸 Screenshot saved: {filename}\n")
            messagebox.showinfo("Screenshot", f"Screenshot saved as {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Could not take screenshot: {e}")

    def _refresh_monitoring(self):
        """Refresh monitoring display"""
        self.status_text.insert(tk.END, f"🔄 Refreshed at: {datetime.now().strftime('%H:%M:%S')}\n")

        # Force a monitoring update if active
        if self.monitoring_active:
            threading.Thread(target=self._force_monitoring_update, daemon=True).start()

    def _force_monitoring_update(self):
        """Force a monitoring update"""
        try:
            all_templates = set()
            steps = self.config_data.get('execution_steps', {})
            for step_config in steps.values():
                step_templates = step_config.get('templates', [])
                all_templates.update(step_templates)

            if all_templates:
                results = []
                for template_name in all_templates:
                    confidence = self._quick_template_test(template_name)
                    if confidence:
                        results.append((template_name, confidence))

                self.window.after(0, self._update_live_results, results)

        except Exception as e:
            self.window.after(0, self._update_monitoring_error, str(e))

    def _create_advanced_settings_tab(self):
        """Create advanced settings tab"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="🔧 Advanced Settings")

        # Create scrollable frame
        canvas = tk.Canvas(frame, bg='#2b2b2b')
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Template Settings
        template_frame = ttk.LabelFrame(scrollable_frame, text="🎯 Template Detection Settings")
        template_frame.pack(fill=tk.X, padx=5, pady=5)

        # Default confidence
        ttk.Label(template_frame, text="Default Confidence Threshold:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.default_confidence_var = tk.DoubleVar(value=self.config_data.get('template_settings', {}).get('default_confidence', 0.8))
        confidence_scale = ttk.Scale(template_frame, from_=0.1, to=1.0, variable=self.default_confidence_var, orient=tk.HORIZONTAL)
        confidence_scale.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)

        confidence_label = ttk.Label(template_frame, text="0.80")
        confidence_label.grid(row=0, column=2, padx=5, pady=2)

        def update_confidence_label(*args):
            confidence_label.config(text=f"{self.default_confidence_var.get():.2f}")
        self.default_confidence_var.trace('w', update_confidence_label)

        # Multi-scale detection
        self.multi_scale_var = tk.BooleanVar(value=self.config_data.get('template_settings', {}).get('multi_scale_detection', True))
        ttk.Checkbutton(template_frame, text="Enable Multi-Scale Detection",
                       variable=self.multi_scale_var).grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)

        # OCR Settings
        ocr_frame = ttk.LabelFrame(scrollable_frame, text="🔤 OCR Settings")
        ocr_frame.pack(fill=tk.X, padx=5, pady=5)

        # OCR Engine
        ttk.Label(ocr_frame, text="OCR Engine:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.ocr_engine_var = tk.StringVar(value=self.config_data.get('ocr_settings', {}).get('engine', 'tesseract'))
        ocr_combo = ttk.Combobox(ocr_frame, textvariable=self.ocr_engine_var, values=['tesseract', 'easyocr', 'paddleocr'])
        ocr_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)

        # PSM Mode
        ttk.Label(ocr_frame, text="Tesseract PSM Mode:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.psm_mode_var = tk.IntVar(value=self.config_data.get('ocr_settings', {}).get('psm_mode', 6))
        psm_spinbox = ttk.Spinbox(ocr_frame, from_=0, to=13, width=5, textvariable=self.psm_mode_var)
        psm_spinbox.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)

        # Timer Settings
        timer_frame = ttk.LabelFrame(scrollable_frame, text="⏱️ Timer Detection Settings")
        timer_frame.pack(fill=tk.X, padx=5, pady=5)

        # Timer threshold
        ttk.Label(timer_frame, text="Timer Threshold (seconds):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.timer_threshold_var = tk.IntVar(value=10)
        timer_spinbox = ttk.Spinbox(timer_frame, from_=1, to=60, width=5, textvariable=self.timer_threshold_var)
        timer_spinbox.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)

        # Click duration
        ttk.Label(timer_frame, text="Auto-click Duration (seconds):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.click_duration_var = tk.IntVar(value=20)
        duration_spinbox = ttk.Spinbox(timer_frame, from_=5, to=60, width=5, textvariable=self.click_duration_var)
        duration_spinbox.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)

        # Debug Settings
        debug_frame = ttk.LabelFrame(scrollable_frame, text="🐛 Debug Settings")
        debug_frame.pack(fill=tk.X, padx=5, pady=5)

        self.debug_mode_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(debug_frame, text="Enable Debug Mode",
                       variable=self.debug_mode_var).pack(anchor=tk.W, padx=5, pady=2)

        self.save_screenshots_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(debug_frame, text="Save Debug Screenshots",
                       variable=self.save_screenshots_var).pack(anchor=tk.W, padx=5, pady=2)

        # Apply button
        ttk.Button(scrollable_frame, text="💾 Apply Advanced Settings",
                  command=self._apply_advanced_settings).pack(pady=10)

    def _apply_advanced_settings(self):
        """Apply advanced settings to configuration"""
        try:
            # Update template settings
            if 'template_settings' not in self.config_data:
                self.config_data['template_settings'] = {}

            self.config_data['template_settings']['default_confidence'] = self.default_confidence_var.get()
            self.config_data['template_settings']['multi_scale_detection'] = self.multi_scale_var.get()

            # Update OCR settings
            if 'ocr_settings' not in self.config_data:
                self.config_data['ocr_settings'] = {}

            self.config_data['ocr_settings']['engine'] = self.ocr_engine_var.get()
            self.config_data['ocr_settings']['psm_mode'] = self.psm_mode_var.get()

            # Save configuration
            self._save_configuration()

            messagebox.showinfo("Applied", "Advanced settings have been applied and saved")

        except Exception as e:
            messagebox.showerror("Error", f"Could not apply settings: {e}")

    def _create_bottom_controls(self):
        """Create bottom control panel"""
        control_frame = ttk.Frame(self.window)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        # Left side - Action buttons
        left_frame = ttk.Frame(control_frame)
        left_frame.pack(side=tk.LEFT)

        ttk.Button(left_frame, text="🚀 Start Dig Module",
                  command=self._manual_start_dig).pack(side=tk.LEFT, padx=5)
        ttk.Button(left_frame, text="⏹️ Stop Dig Module",
                  command=self._manual_stop_dig).pack(side=tk.LEFT, padx=5)
        ttk.Button(left_frame, text="🔄 Apply Changes",
                  command=self._apply_all_changes).pack(side=tk.LEFT, padx=5)

        # Right side - Close button
        ttk.Button(control_frame, text="❌ Close",
                  command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

    def _manual_start_dig(self):
        """Manually start dig module"""
        try:
            if self.dig_module:
                # Create mock screen data for testing
                mock_screen_data = {
                    'templates_found': {'dig_icon': [[100, 200, 50, 30, 0.8]]},
                    'templates_detected': {'dig_icon': True}
                }

                can_execute = self.dig_module.custom_can_execute(mock_screen_data)
                if can_execute:
                    result = self.dig_module.custom_execute(mock_screen_data)
                    messagebox.showinfo("Started", f"Dig module started: {result}")
                else:
                    messagebox.showwarning("Cannot Start", "Dig module cannot execute (conditions not met)")
            else:
                messagebox.showwarning("Not Available", "Dig module not available")

        except Exception as e:
            messagebox.showerror("Error", f"Could not start dig module: {e}")

    def _manual_stop_dig(self):
        """Manually stop dig module"""
        try:
            if self.dig_module and hasattr(self.dig_module, 'dig_in_progress'):
                self.dig_module.dig_in_progress = False
                self.dig_module.current_step = 0
                messagebox.showinfo("Stopped", "Dig module stopped")
            else:
                messagebox.showinfo("Not Running", "Dig module is not currently running")

        except Exception as e:
            messagebox.showerror("Error", f"Could not stop dig module: {e}")

    def _apply_all_changes(self):
        """Apply all configuration changes"""
        try:
            # Update coordinates from GUI
            for var_name, var_obj in self.coordinate_vars.items():
                if '_x' in var_name:
                    step_key = var_name.replace('_x', '')
                    if step_key in self.config_data.get('execution_steps', {}):
                        if 'click_coordinates' not in self.config_data['execution_steps'][step_key]:
                            self.config_data['execution_steps'][step_key]['click_coordinates'] = {}
                        self.config_data['execution_steps'][step_key]['click_coordinates']['x'] = var_obj.get()
                elif '_y' in var_name:
                    step_key = var_name.replace('_y', '')
                    if step_key in self.config_data.get('execution_steps', {}):
                        if 'click_coordinates' not in self.config_data['execution_steps'][step_key]:
                            self.config_data['execution_steps'][step_key]['click_coordinates'] = {}
                        self.config_data['execution_steps'][step_key]['click_coordinates']['y'] = var_obj.get()

            # Save configuration
            self._save_configuration()

            messagebox.showinfo("Applied", "All changes have been applied and saved")

        except Exception as e:
            messagebox.showerror("Error", f"Could not apply changes: {e}")

    # Helper methods for step testing
    def _test_step_templates(self, step_key):
        """Test all templates for a specific step"""
        step_config = self.config_data.get('execution_steps', {}).get(step_key, {})
        templates = step_config.get('templates', [])

        if not templates:
            messagebox.showinfo("No Templates", f"No templates configured for {step_key}")
            return

        # Switch to template testing tab and test each template
        self.notebook.select(1)  # Template testing tab

        results = []
        for template_name in templates:
            confidence = self._quick_template_test(template_name)
            results.append((template_name, confidence if confidence else 0.0))

        # Display results
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, f"🔍 Testing templates for {step_key}\n")
        self.results_text.insert(tk.END, "=" * 50 + "\n\n")

        for template_name, confidence in results:
            if confidence > 0:
                quality = self._get_confidence_quality(confidence)
                self.results_text.insert(tk.END, f"✅ {template_name}: {confidence:.4f} ({quality})\n")
            else:
                self.results_text.insert(tk.END, f"❌ {template_name}: Not detected\n")

    def _start_coordinate_capture(self, step_key):
        """Start coordinate capture for a specific step"""
        self.capturing_mouse = True
        self.current_step = step_key

        messagebox.showinfo("Coordinate Capture",
                          f"Coordinate capture started for: {step_key}\n\n"
                          "1. Move mouse to desired click position\n"
                          "2. Press ENTER to capture coordinates\n"
                          "3. Coordinates will be automatically saved")

    def _test_individual_step(self, step_key):
        """Test individual step execution"""
        messagebox.showinfo("Step Test", f"Testing step: {step_key}\n\nThis would execute the step in isolation.")

    def _capture_step_screenshot(self, step_key):
        """Capture screenshot for step analysis"""
        try:
            screenshot = pyautogui.screenshot()
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"dig_step_{step_key}_{timestamp}.png"
            screenshot.save(filename)

            messagebox.showinfo("Screenshot", f"Screenshot saved as {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Could not capture screenshot: {e}")

    def _show_step_advanced_settings(self, step_key):
        """Show advanced settings for a specific step"""
        messagebox.showinfo("Advanced Settings", f"Advanced settings for {step_key}\n\nThis would open detailed step configuration.")

# Integration function for Config Helper
def show_enhanced_dig_control_panel(parent_window):
    """Show the enhanced dig control panel"""
    panel = EnhancedDigControlPanel(parent_window)
    panel.show_control_panel()
    return panel
