{"config_version": "1.0.0", "last_updated": "2025-10-09T15:18:23.056669", "description": "Comprehensive dig module configuration - Single source of truth for all dig module settings", "general_settings": {"enabled": true, "priority": -1, "cooldown": 0.0, "debug_mode": true, "execution_timeout": 300, "max_retry_attempts": 3, "step_delay_multiplier": 1.0}, "trigger_templates": {"dig_icon": {"enabled": true, "threshold": 0.7, "scan_region": {"x": 0, "y": 0, "width": 2560, "height": 1440, "description": "Full screen scan for dig icon"}, "execution_path": "full_sequence", "starting_step": 1}, "test_flight_treasure": {"enabled": true, "threshold": 0.5, "scan_region": {"x": 879, "y": 218, "width": 787, "height": 1046, "description": "Chat panel region"}, "execution_path": "chat_trigger", "starting_step": 3}, "dig_up_treasure": {"enabled": true, "threshold": 0.7, "scan_region": {"x": 878, "y": 219, "width": 789, "height": 1045, "description": "Chat panel region"}, "execution_path": "chat_trigger", "starting_step": 3}, "timer_1": {"enabled": true, "threshold": 0.6, "scan_region": {"x": 1100, "y": 500, "width": 400, "height": 200, "description": "Timer detection region"}, "execution_path": "timer_trigger", "starting_step": 6}}, "execution_steps": {"step_1_open_chat": {"name": "Open Alliance Chat", "enabled": true, "timeout": 10, "max_attempts": 3, "actions": [{"type": "click", "coordinates": [1267, 1353], "delay_after": 1.0, "description": "Click alliance chat button"}, {"type": "click", "coordinates": [1176, 170], "delay_after": 1.5, "description": "Click alliance tab"}], "verification_templates": [{"name": "alliance_chat_is_on", "threshold": 0.7, "required": true, "scan_region": {"x": 800, "y": 100, "width": 900, "height": 1200, "description": "Chat window area"}}]}, "step_2_find_treasure": {"name": "Find Treasure Link", "enabled": true, "timeout": 30, "max_attempts": 10, "scan_templates": [{"name": "dig_up_treasure", "threshold": 0.5, "scan_region": {"x": 879, "y": 218, "width": 787, "height": 1046, "description": "Chat panel region"}, "click_adjustment": {"x_offset_percent": 30, "y_offset_percent": 50, "description": "Click 30% from left edge for text precision"}}, {"name": "test_flight_treasure", "threshold": 0.5, "scan_region": {"x": 879, "y": 218, "width": 787, "height": 1046, "description": "Chat panel region"}, "click_adjustment": {"x_offset_percent": 30, "y_offset_percent": 50, "description": "Click 30% from left edge for text precision"}}], "fallback_action": {"type": "click", "coordinates": [1073, 1327], "delay_after": 1.0, "description": "Fallback chat click if templates not found"}, "robust_detection": {"enabled": true, "ultra_low_threshold": 0.3, "multiple_methods": true, "color_validation": true}}, "step_3_verify_navigation": {"name": "Verify Chat State & Navigation", "enabled": true, "timeout": 15, "max_attempts": 5, "verification_templates": [{"name": "Base", "threshold": 0.7, "required": true, "scan_region": {"x": 0, "y": 0, "width": 2560, "height": 1440, "description": "Full screen scan for Base template"}, "description": "Confirms successful navigation from chat to dig location"}], "send_to_dig_templates": [{"name": "Send_to_dig", "threshold": 0.7, "coordinates": [1279, 646]}, {"name": "send_to_dig_1", "threshold": 0.7, "coordinates": [1279, 646]}, {"name": "send_to_dig_2", "threshold": 0.7, "coordinates": [1279, 646]}, {"name": "Send_to_dig_3", "threshold": 0.7, "coordinates": [1279, 646]}, {"name": "send_to_dig_flight", "threshold": 0.7, "coordinates": [1279, 646]}, {"name": "send_to_dig_flight_2", "threshold": 0.7, "coordinates": [1279, 646]}, {"name": "timer_1", "threshold": 0.7, "coordinates": [1279, 646]}], "scan_region": {"x": 0, "y": 0, "width": 2560, "height": 1440, "description": "Full screen scan for send_to_dig templates"}}, "step_4_deploy_squad": {"name": "Deploy Squad", "enabled": true, "timeout": 20, "max_attempts": 3, "actions": [{"type": "template_click", "template": "march_to_dig", "threshold": 0.7, "fallback_coordinates": [1279, 875], "delay_after": 1.5, "description": "Click march to dig or send squad button"}, {"type": "template_click", "template": "select_tile", "threshold": 0.7, "fallback_coordinates": [1279, 1101], "delay_after": 1.5, "description": "Click select tile or confirm deployment"}, {"type": "click", "coordinates": [1279, 646], "delay_after": 1.0, "description": "Click to open timer window"}]}, "step_5_timer_management": {"name": "Timer Management & Rapid Clicking", "enabled": true, "timeout": 600, "timer_detection": {"regions": [{"name": "green_circle", "coordinates": [1200, 600, 200, 100], "description": "Green circle timer - white text on green background", "preprocessing": {"color_inversion": true, "threshold": 200, "morphology": true}}, {"name": "white_panel", "coordinates": [1200, 200, 200, 80], "description": "White panel timer - white text on gray background", "preprocessing": {"color_inversion": true, "threshold": 150, "morphology": true}}], "ocr_settings": {"psm_modes": [6, 8], "whitelist": "0123456789:", "confidence_threshold": 0.5}, "parsing": {"formats": ["MM:SS", "SS"], "max_seconds": 3600, "min_seconds": 0}}, "rapid_clicking": {"trigger_threshold": 10, "click_speed": 0.005, "duration": 20.0, "coordinates": [1279, 646], "description": "Rapid click when timer <= 10 seconds"}}, "step_6_exit_menu": {"name": "Exit to Main Menu", "enabled": true, "timeout": 30, "max_attempts": 10, "esc_recovery": {"enabled": true, "max_esc_presses": 5, "delay_between_esc": 0.5, "target_template": "quit_game_dialog", "final_esc_after_dialog": true}}}, "completion_settings": {"thank_you_message": {"enabled": true, "message": "Thank you for the dig!", "chat_coordinates": [1073, 1327], "send_key": "Return"}, "return_to_scanner": {"enabled": true, "final_esc_sequence": true, "resume_main_scanner": true}}}