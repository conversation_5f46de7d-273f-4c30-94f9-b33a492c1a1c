"""
Main controller for Last War automation
Manages all modules, priorities, and the main execution loop
Enhanced with centralized template scanner and unified configuration
"""
import time
import threading
import logging
import configparser
from typing import List, Dict, Any, Optional
from datetime import datetime
import os

from base_module import BaseModule
from screen_scanner import ScreenScanner
from centralized_template_scanner import CentralizedTemplateScanner
from unified_config_manager import UnifiedConfigManager



class MainController:
    """Main controller that orchestrates all automation modules"""
    
    def __init__(self, config_file: str = "config.ini"):
        # Initialize unified configuration manager
        self.unified_config = UnifiedConfigManager()
        self.config_data = self.unified_config.load_config()

        # Keep legacy config for backward compatibility during transition
        self.config = configparser.ConfigParser()
        self.config.read(config_file)

        # Setup logging
        self._setup_logging()
        self.logger = logging.getLogger("LastWar.MainController")

        self.logger.info("MainController initialized with unified configuration system")

        # Initialize centralized template scanner with controller reference for mouse override control
        self.centralized_scanner = CentralizedTemplateScanner(controller=self)

        # Keep legacy screen scanner for backward compatibility
        templates_path = self.config_data.get('system_settings', {}).get('templates_path', 'templates/')
        screenshots_path = self.config_data.get('system_settings', {}).get('screenshots_path', 'screenshots/')

        self.screen_scanner = ScreenScanner(
            templates_path=templates_path,
            screenshots_path=screenshots_path
        )

        # Clean up any old screenshot files (cache system doesn't save to disk)
        self.screen_scanner.cleanup_old_screenshots()

        # Module management
        self.modules: List[BaseModule] = []
        self.running = False
        self.paused = False
        self.main_thread = None

        # Centralized scanner mode (new architecture)
        self.use_centralized_scanner = self.config_data.get('system_settings', {}).get('centralized_scanner_enabled', True)
        
        # Timing configuration from unified config
        system_settings = self.config_data.get('system_settings', {})
        self.scan_interval = system_settings.get('scan_interval', self.config.getfloat('GENERAL', 'scan_interval', fallback=1.0))
        self.priority_check_interval = system_settings.get('priority_check_interval', self.config.getfloat('GENERAL', 'priority_check_interval', fallback=1.0))
        
        # Statistics
        self.start_time = None
        self.total_scans = 0
        self.total_executions = 0

        # Memory management for overnight stability
        self.memory_cleanup_interval = 300  # Clean memory every 5 minutes
        self.last_memory_cleanup = time.time()
        self.max_memory_mb = 500  # Restart if memory exceeds 500MB


        self.movement_tolerance = 15  # Pixels tolerance for programmatic movements
        self.movement_timeout = 3.0  # Seconds to keep tracking programmatic movements

        # UI recovery skip tracking - avoid ESC spam after module execution
        self.last_module_execution_time = 0
        self.ui_recovery_skip_duration = 8.0  # Skip UI recovery for 8 seconds after module execution

        # Debug ESC timer - only send ESC after events button missing for X seconds
        self.events_button_missing_since = 0
        self.debug_esc_delay = 5.0  # Wait 5 seconds before first ESC attempt
        self.debug_esc_extended_delay = 10.0  # Wait 10 seconds if 5 seconds wasn't enough



        # Initialize crash recovery system
        from crash_recovery_system import get_crash_recovery_system
        self.crash_recovery = get_crash_recovery_system(self.logger)
        self.crash_recovery.set_main_controller(self)
        self.crash_recovery.set_centralized_scanner(self.centralized_scanner)

        self.logger.info("MainController initialized")
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)
        
        # Create log filename with timestamp
        log_filename = os.path.join(log_dir, f"lastwar_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename),
                logging.StreamHandler()  # Also log to console
            ]
        )
    
    def register_module(self, module: BaseModule):
        """Register a new automation module with unified configuration support"""
        # Give module a reference to the controller so it can check running status
        module.controller = self

        # Load module configuration from unified config
        module_config = self.unified_config.get_module_config(module.name)
        if module_config:
            # Apply unified configuration to module
            module.enabled = module_config.get('enabled', module.enabled)
            module.priority = module_config.get('priority', module.priority)
            if hasattr(module, 'cooldown'):
                module.cooldown = module_config.get('cooldown', getattr(module, 'cooldown', 300))

            # Apply module-specific configuration
            if hasattr(module, 'apply_unified_config'):
                module.apply_unified_config(module_config)

            self.logger.info(f"Applied unified configuration to module: {module.name}")
        else:
            self.logger.warning(f"No unified configuration found for module: {module.name}")

        self.modules.append(module)
        self.modules.sort(key=lambda x: x.priority)  # Sort by priority

        # Register with centralized scanner
        self.centralized_scanner.register_module(module.name, module)

        self.logger.info(f"Registered module: {module.name} (priority: {module.priority}, enabled: {module.enabled})")

    def get_module_by_name(self, module_name: str):
        """Get a module by name"""
        for module in self.modules:
            if module.name == module_name:
                return module
        self.logger.warning(f"Module not found: {module_name}")
        return None
    
    def unregister_module(self, module_name: str):
        """Unregister a module by name"""
        self.modules = [m for m in self.modules if m.name != module_name]
        self.logger.info(f"Unregistered module: {module_name}")
    
    def get_module(self, module_name: str) -> Optional[BaseModule]:
        """Get a module by name"""
        for module in self.modules:
            if module.name == module_name:
                return module
        return None
    
    def enable_module(self, module_name: str):
        """Enable a specific module"""
        module = self.get_module(module_name)
        if module:
            module.enabled = True
            self.logger.info(f"Enabled module: {module_name}")
        else:
            self.logger.warning(f"Module not found: {module_name}")
    
    def disable_module(self, module_name: str):
        """Disable a specific module"""
        module = self.get_module(module_name)
        if module:
            module.enabled = False
            self.logger.info(f"Disabled module: {module_name}")
        else:
            self.logger.warning(f"Module not found: {module_name}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the controller and all modules"""
        return {
            'running': self.running,
            'start_time': self.start_time,
            'uptime': time.time() - self.start_time if self.start_time else 0,
            'total_scans': self.total_scans,
            'total_executions': self.total_executions,
            'modules': [module.get_status() for module in self.modules],
            'scan_interval': self.scan_interval
        }

    def _check_ui_state_during_scan(self, screen_data):
        """
        Efficient UI state check during main scan
        Checks for events button presence and quit game dialog, recovers with ESC if needed
        """
        try:
            # Check templates detected in current scan
            templates_detected = screen_data.get('templates_detected', {})
            events_detected = templates_detected.get('events_button', False)
            quit_detected = templates_detected.get('quit_game_dialog', False)

            # Priority 1: Check for quit game dialog
            if quit_detected:
                self.logger.warning("[QUIT_RECOVERY] Quit game dialog detected - pressing ESC to return to game")



                try:
                    import pyautogui
                    pyautogui.press('escape')
                    time.sleep(1.5)  # Longer wait for quit dialog to close

                    # Verify recovery worked
                    events_detected = self._quick_events_button_check()
                    if events_detected:
                        self.logger.info("[QUIT_RECOVERY] ESC successful - returned to game from quit dialog")
                        return True
                    else:
                        self.logger.warning("[QUIT_RECOVERY] ESC may not have worked - will try standard recovery")
                        # Continue to standard recovery below
                finally:
                    pass  # Mouse override system removed

            # Priority 2: Check for events button (normal UI state)
            if events_detected:
                # UI state OK - events button detected
                self.events_button_missing_since = 0  # Reset timer
                return True

            # Priority 3: Check if we should skip UI recovery (recent module execution)
            current_time = time.time()
            time_since_module = current_time - self.last_module_execution_time
            if time_since_module < self.ui_recovery_skip_duration:
                # Skipping ESC recovery - recent module execution
                return True

            # Priority 4: Timer-based ESC recovery - only after events button missing for X seconds
            if self.events_button_missing_since == 0:
                self.events_button_missing_since = current_time
                # Events button missing - starting recovery timer
                return True

            time_missing = current_time - self.events_button_missing_since
            if time_missing < self.debug_esc_delay:
                # Waiting for ESC recovery timer
                return True

            # Events button missing for enough time - attempt recovery
            self.logger.warning(f"[UI_RECOVERY] Events button missing for {time_missing:.1f}s - attempting ESC recovery")



            try:
                # First ESC attempt
                import pyautogui
                pyautogui.press('escape')
                time.sleep(1.0)

                # Quick check if recovery worked
                events_detected = self._quick_events_button_check()
                if events_detected:
                    self.logger.info("[UI_RECOVERY] First ESC successful - events button restored")
                    return True

                # Second ESC attempt
                self.logger.warning("[UI_RECOVERY] First ESC failed - trying second ESC")
                pyautogui.press('escape')
                time.sleep(1.0)

                # Final check
                events_detected = self._quick_events_button_check()
                if events_detected:
                    self.logger.info("[UI_RECOVERY] Second ESC successful - events button restored")
                    return True
                else:
                    self.logger.error("[UI_RECOVERY] Both ESC attempts failed - UI may be stuck")
                    return False

            finally:
                pass  # Mouse override system removed

        except Exception as e:
            self.logger.error(f"[UI_RECOVERY] Error during UI state check: {str(e)}")
            return False

    def _quick_events_button_check(self) -> bool:
        """
        Quick check for events button presence
        Returns True if events button is detected
        """
        try:
            # Use the screen scanner to quickly check for events button
            if hasattr(self, 'screen_scanner') and self.screen_scanner:
                # Quick template match for events button only
                templates_to_check = ['events_button']
                screen_data = self.screen_scanner.scan_screen_cache_optimized(templates_to_check)

                if 'templates_detected' in screen_data:
                    return screen_data['templates_detected'].get('events_button', False)

            return False

        except Exception as e:
            self.logger.error(f"[UI_CHECK] Error during quick events button check: {str(e)}")
            return False




    
    def _main_loop(self):
        """Main execution loop"""
        self.logger.info("Starting main execution loop")
        
        while self.running:
            try:
                # Check if paused
                if self.paused:
                    time.sleep(0.5)  # Sleep while paused
                    continue

                # Memory management and crash prevention
                if not self._check_memory_usage():
                    self.logger.error("[MEMORY] Stopping due to memory issues")
                    break

                # CENTRALIZED SCANNER: New architecture with template-to-module mapping
                if self.use_centralized_scanner:
                    # Log first scan to confirm centralized scanner is active
                    if self.total_scans == 0:
                        self.logger.info("[SCANNER] Centralized scanner active - starting template detection")
                    scan_start = time.time()

                    # Use centralized template scanner
                    result = self.centralized_scanner.scan_and_execute()
                    scan_time = time.time() - scan_start

                    if result:
                        template_name, module_name, success = result
                        self.total_executions += 1
                        status = "SUCCESS" if success else "FAILED"
                        self.logger.info(f"[{status}] Centralized: {template_name} -> {module_name} ({scan_time:.2f}s)")
                    else:
                        # Show scanning activity every 10 scans to confirm it's working
                        self.total_scans += 1
                        if self.total_scans % 10 == 0:
                            self.logger.info(f"[SCAN] Centralized scan: {self.total_scans} scans completed, no actions needed ({scan_time:.2f}s)")

                    # Continue to next iteration
                    time.sleep(self.scan_interval)
                    continue

                # LEGACY SCANNING: Original architecture (fallback)
                scan_start = time.time()

                # Get list of enabled modules sorted by priority
                enabled_modules = [m for m in self.modules if m.enabled]
                enabled_modules.sort(key=lambda x: x.priority)

                if not enabled_modules:
                    # No modules enabled - minimal scan
                    screen_data = self.screen_scanner.scan_screen_fast()
                    scan_time = time.time() - scan_start
                    # Minimal scan completed
                else:
                    # Collect required templates from all enabled modules
                    required_templates = set()

                    # Always include events_button for UI state checking
                    required_templates.add('events_button')

                    # Always include quit_game_dialog for quit detection
                    required_templates.add('quit_game_dialog')

                    for module in enabled_modules:
                        # Get templates needed by this module
                        if hasattr(module, 'get_required_templates'):
                            module_templates = module.get_required_templates()
                            required_templates.update(module_templates)
                        else:
                            # Default templates for modules that don't specify
                            if module.name == "zombie_invasion":
                                required_templates.update(['squad_0_4', 'squad_1_4', 'squad_2_4', 'squad_3_4', 'squad_4_4', 'events_button'])
                            elif module.name == "alliance_donation":
                                required_templates.update(['events_button'])
                            elif module.name == "daily_tasks":
                                required_templates.update(['events_button'])
                            elif module.name == "help_click":
                                # help_click needs templates to detect help requests!
                                required_templates.update(['Help_template', 'Help_chat'])

                    # Special case: help_click gets ultra-fast path
                    help_click_enabled = any(m.name == "help_click" and m.enabled and m.priority == 0 for m in enabled_modules)

                    if help_click_enabled and len(required_templates) == 0:
                        # ULTRA-FAST PATH: help_click only, no templates needed
                        screen_data = {
                            'timestamp': time.time(),
                            'screenshot': None,
                            'templates_detected': {},  # No templates to detect
                            'templates_found': {'events_button_detected': True},
                            'text_regions': {},
                            'screen_size': (1920, 1080)
                        }
                        scan_time = time.time() - scan_start
                        # Ultra-fast cache scan
                    else:
                        # CACHE-OPTIMIZED PATH: One screenshot to memory, analyze all templates
                        screen_data = self.screen_scanner.scan_screen_cache_optimized(list(required_templates))
                        scan_time = time.time() - scan_start
                        # Cache-optimized scan completed
                
                self.total_scans += 1
                
                if 'error' in screen_data:
                    self.logger.error(f"Screen scan failed: {screen_data['error']}")
                    time.sleep(self.scan_interval)
                    continue
                
                # Screen scan completed

                # EFFICIENT DEBUG: Check UI state during main scan (not after module execution)
                self._check_ui_state_during_scan(screen_data)

                # REVOLUTIONARY PRIORITY EXECUTION: Your brilliant cache-based approach!
                executed_module = False
                templates_detected = screen_data.get('templates_detected', {})

                # Sort enabled modules by priority (0 = highest priority)
                enabled_modules = [m for m in self.modules if m.enabled]
                enabled_modules.sort(key=lambda x: x.priority)

                # Priority execution check

                # Execute highest priority module that can run based on cache results
                for module in enabled_modules:
                    # Check if module can run based on cached template detection
                    if module.can_run(screen_data):


                        execution_start = time.time()

                        # Log which templates this module detected
                        if hasattr(module, 'get_required_templates'):
                            required = module.get_required_templates()
                            detected_templates = [t for t in required if templates_detected.get(t, False)]
                            # Module template check

                        try:
                            success = module.run(screen_data)
                            execution_time = time.time() - execution_start



                            if success:
                                self.total_executions += 1
                                executed_module = True
                                self.last_module_execution_time = time.time()  # Track for UI recovery skip
                                self.logger.info(f"[SUCCESS] Module {module.name} (priority {module.priority}) executed in {execution_time:.4f}s")

                                # Reset failure count on successful execution
                                self.crash_recovery.reset_module_failure_count(module.name)

                                # Update GUI statistics counter
                                if hasattr(self, 'gui') and self.gui and hasattr(self.gui, 'update_module_stats'):
                                    self.gui.update_module_stats(module.name)

                                break  # Execute highest priority module only
                            else:
                                self.logger.warning(f"[FAILED] Module {module.name} execution failed")

                                # Handle module failure through crash recovery system
                                should_continue = self.crash_recovery.handle_module_failure(
                                    module.name,
                                    Exception("Module execution returned False"),
                                    f"Module {module.name} returned False from run() method"
                                )

                                if not should_continue:
                                    self.logger.critical(f"[CRASH_RECOVERY] Automation paused due to repeated failures of module {module.name}")
                                    self.pause()
                                    break

                        except Exception as module_error:
                            execution_time = time.time() - execution_start



                            self.logger.error(f"[MODULE_ERROR] Module {module.name} crashed after {execution_time:.4f}s: {str(module_error)}")

                            # Handle module crash through crash recovery system
                            should_continue = self.crash_recovery.handle_module_failure(
                                module.name,
                                module_error,
                                f"Module {module.name} crashed during execution with {type(module_error).__name__}"
                            )

                            if not should_continue:
                                self.logger.critical(f"[CRASH_RECOVERY] Automation paused due to repeated crashes of module {module.name}")
                                self.pause()
                                break
                
                if not executed_module:
                    # No modules executed this cycle
                
                # Wait before next scan
                time.sleep(self.scan_interval)
                
            except KeyboardInterrupt:
                self.logger.info("Received keyboard interrupt, stopping...")
                self.stop()
                break
            except Exception as e:
                self.logger.error(f"[ERROR] Error in main loop: {str(e)}")
                self.logger.error(f"[ERROR] Error type: {type(e).__name__}")

                # Enhanced error recovery for overnight stability
                try:
                    import traceback
                    self.logger.error(f"[ERROR] Full traceback: {traceback.format_exc()}")
                except:
                    pass

                # Force garbage collection after error
                try:
                    import gc
                    collected = gc.collect()
                    self.logger.info(f"[CLEANUP] Emergency cleanup: {collected} objects collected")
                except:
                    pass

                # Longer wait for recovery
                self.logger.info("[RECOVERY] Waiting 10 seconds for recovery...")
                time.sleep(10)
    
    def start(self):
        """Start the main controller"""
        if self.running:
            self.logger.warning("Controller is already running")
            return
        
        self.running = True
        self.start_time = time.time()
        self.main_thread = threading.Thread(target=self._main_loop, daemon=True)
        self.main_thread.start()
        
        self.logger.info("MainController started")

    def pause(self):
        """Pause the main controller"""
        if not self.running:
            self.logger.warning("Controller is not running")
            return

        self.paused = True
        self.logger.info("MainController paused")

    def resume(self):
        """Resume the main controller"""
        if not self.running:
            self.logger.warning("Controller is not running")
            return

        self.paused = False
        self.logger.info("MainController resumed")

    def _check_memory_usage(self) -> bool:
        """Check memory usage and perform cleanup if needed"""
        try:
            import psutil
            import gc

            # Get current memory usage
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024

            # Check if cleanup is needed
            current_time = time.time()
            if current_time - self.last_memory_cleanup > self.memory_cleanup_interval:
                self.logger.info(f"[CLEANUP] Performing memory cleanup - Current usage: {memory_mb:.1f}MB")

                # Force garbage collection
                collected = gc.collect()

                # Clear screen scanner template cache periodically
                if hasattr(self.screen_scanner, 'templates'):
                    template_count = len(self.screen_scanner.templates)
                    # Don't clear all templates, just reload them to free fragmented memory
                    self.screen_scanner.templates = self.screen_scanner._load_templates()
                    self.logger.info(f"[MEMORY] Reloaded {template_count} templates to defragment memory")

                # Clean up any old screenshot files
                if hasattr(self.screen_scanner, 'cleanup_old_screenshots'):
                    self.screen_scanner.cleanup_old_screenshots()

                # Clean up mouse override controller memory
                if hasattr(self, 'mouse_controller') and self.mouse_controller:
                    self.mouse_controller.cleanup_memory()

                # Update cleanup time
                self.last_memory_cleanup = current_time

                # Check memory after cleanup
                new_memory_mb = process.memory_info().rss / 1024 / 1024
                self.logger.info(f"[SUCCESS] Memory cleanup complete - {collected} objects collected, "
                               f"Memory: {memory_mb:.1f}MB -> {new_memory_mb:.1f}MB")

                memory_mb = new_memory_mb

            # Check if memory is too high (crash prevention)
            if memory_mb > self.max_memory_mb:
                self.logger.error(f"[MEMORY] MEMORY CRITICAL: {memory_mb:.1f}MB > {self.max_memory_mb}MB limit!")
                self.logger.error("[MEMORY] Stopping automation to prevent crash - please restart application")
                return False

            return True

        except ImportError:
            # psutil not available, skip memory checks
            return True
        except Exception as e:
            self.logger.error(f"Error checking memory usage: {str(e)}")
            return True

    def stop(self):
        """Stop the main controller"""
        if not self.running:
            self.logger.warning("Controller is not running")
            return
        
        self.running = False
        self.paused = False

        if self.main_thread and self.main_thread.is_alive():
            self.main_thread.join(timeout=5.0)

        self.logger.info("MainController stopped")





    def reload_config(self):
        """Reload configuration from unified config and legacy files"""
        # Reload unified configuration
        self.config_data = self.unified_config.load_config()

        # Reload legacy configuration for backward compatibility
        self.config.read("config.ini")

        # Update timing settings
        system_settings = self.config_data.get('system_settings', {})
        self.scan_interval = system_settings.get('scan_interval', self.config.getfloat('GENERAL', 'scan_interval', fallback=1.0))
        self.priority_check_interval = system_settings.get('priority_check_interval', self.config.getfloat('GENERAL', 'priority_check_interval', fallback=1.0))

        # Update centralized scanner setting
        self.use_centralized_scanner = system_settings.get('centralized_scanner_enabled', True)

        # Reload module configurations
        for module in self.modules:
            module_config = self.unified_config.get_module_config(module.name)
            if module_config:
                module.enabled = module_config.get('enabled', module.enabled)
                module.priority = module_config.get('priority', module.priority)
                if hasattr(module, 'cooldown'):
                    module.cooldown = module_config.get('cooldown', getattr(module, 'cooldown', 300))

                if hasattr(module, 'apply_unified_config'):
                    module.apply_unified_config(module_config)

        # Re-sort modules by priority
        self.modules.sort(key=lambda x: x.priority)

        self.logger.info("Unified configuration reloaded for all modules")

    def get_module_by_name(self, module_name):
        """Get a module by its name"""
        for module in self.modules:
            if hasattr(module, 'name') and module.name == module_name:
                return module
        return None
    
    def save_config(self):
        """Save current configuration to unified config and legacy files"""
        # Save unified configuration
        self.unified_config.save_config()

        # Save legacy configuration for backward compatibility
        with open("config.ini", 'w') as configfile:
            self.config.write(configfile)

        self.logger.info("Unified and legacy configurations saved")

    def get_unified_config(self):
        """Get the unified configuration data"""
        return self.config_data

    def update_module_config(self, module_name: str, config_updates: Dict[str, Any]):
        """Update configuration for a specific module"""
        self.unified_config.update_module_config(module_name, config_updates)
        self.config_data = self.unified_config.load_config()

        # Apply updates to the module if it's registered
        module = self.get_module_by_name(module_name)
        if module:
            module_config = self.unified_config.get_module_config(module_name)
            if module_config:
                module.enabled = module_config.get('enabled', module.enabled)
                module.priority = module_config.get('priority', module.priority)
                if hasattr(module, 'cooldown'):
                    module.cooldown = module_config.get('cooldown', getattr(module, 'cooldown', 300))

                if hasattr(module, 'apply_unified_config'):
                    module.apply_unified_config(module_config)

        self.logger.info(f"Updated configuration for module: {module_name}")

    def get_module_config(self, module_name: str) -> Optional[Dict[str, Any]]:
        """Get configuration for a specific module"""
        return self.unified_config.get_module_config(module_name)

    def get_global_coordinates(self) -> Dict[str, Any]:
        """Get global coordinates from unified config"""
        return self.config_data.get('global_coordinates', {})

    def get_system_settings(self) -> Dict[str, Any]:
        """Get system settings from unified config"""
        return self.config_data.get('system_settings', {})

    def get_status_info(self) -> Dict[str, Any]:
        """Get current status information for GUI display"""
        status = {
            'running': self.running,
            'paused': self.paused,
            'total_scans': self.total_scans,
            'total_executions': self.total_executions,
            'uptime': time.time() - self.start_time if self.start_time else 0,
            'scanner_mode': 'Centralized' if self.use_centralized_scanner else 'Legacy',
            'modules': [
                {
                    'name': module.name,
                    'enabled': module.enabled,
                    'priority': module.priority,
                    'last_execution': getattr(module, 'last_execution_time', 0),
                    'total_executions': getattr(module, 'total_executions', 0),
                    'success_rate': getattr(module, 'success_rate', 0),
                    'current_activity': getattr(module, 'current_activity', 'Idle'),
                    'waiting_for': getattr(module, 'waiting_for', 'N/A')
                }
                for module in self.modules
            ]
        }

        # Add centralized scanner status if enabled
        if self.use_centralized_scanner:
            status['centralized_scanner'] = self.centralized_scanner.get_status_info()

        # Add crash recovery status
        if hasattr(self, 'crash_recovery') and self.crash_recovery:
            status['crash_recovery'] = self.crash_recovery.get_status()

        return status

    def toggle_scanner_mode(self):
        """Toggle between centralized and legacy scanner modes"""
        self.use_centralized_scanner = not self.use_centralized_scanner
        mode = "Centralized" if self.use_centralized_scanner else "Legacy"
        self.logger.info(f"[SCANNER] Switched to {mode} scanner mode")
        return self.use_centralized_scanner

    def get_centralized_scanner(self):
        """Get the centralized scanner instance for GUI configuration"""
        return self.centralized_scanner

    def get_module_by_name(self, name: str) -> Optional[BaseModule]:
        """Get module by name"""
        for module in self.modules:
            if module.name == name:
                return module
        return None

    def emergency_stop(self):
        """Emergency stop - immediately halt all operations"""
        self.logger.warning("🚨 EMERGENCY STOP ACTIVATED!")
        self.running = False

        # Force stop all modules if they have a stop method
        for module in self.modules:
            if hasattr(module, 'emergency_stop'):
                try:
                    self.logger.info(f"[EMERGENCY] Stopping module: {module.name}")
                    module.emergency_stop()
                    self.logger.info(f"[EMERGENCY] Module {module.name} stopped successfully")
                except Exception as e:
                    self.logger.error(f"[EMERGENCY] Error during emergency stop of {module.name}: {str(e)}")
            else:
                # For modules without emergency_stop method, set a flag if they have is_executing
                if hasattr(module, 'is_executing'):
                    module.is_executing = False
                    self.logger.info(f"[EMERGENCY] Set is_executing=False for module: {module.name}")



        # Stop centralized scanner
        if hasattr(self, 'centralized_scanner') and self.centralized_scanner:
            try:
                self.centralized_scanner.stop()
                self.logger.info("[EMERGENCY] Centralized scanner stopped")
            except Exception as e:
                self.logger.error(f"[EMERGENCY] Error stopping centralized scanner: {str(e)}")

        self.logger.warning("🚨 EMERGENCY STOP COMPLETED - All systems halted")
