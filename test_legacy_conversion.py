#!/usr/bin/env python3
"""
Test script to verify legacy module conversion is working properly
"""
import json
import os

def test_legacy_conversion():
    """Test that legacy modules are properly converted"""
    print("🔍 Testing Legacy Module Conversion")
    print("=" * 50)
    
    # Load legacy config
    if not os.path.exists("module_configs.json"):
        print("❌ module_configs.json not found")
        return False
    
    with open("module_configs.json", 'r') as f:
        legacy_config = json.load(f)
    
    print(f"📁 Found {len(legacy_config)} legacy modules:")
    for module_name in legacy_config.keys():
        print(f"  - {module_name}")
    
    # Test daily_tasks conversion specifically
    if "daily_tasks" in legacy_config:
        daily_tasks = legacy_config["daily_tasks"]
        print(f"\n🔍 Testing daily_tasks conversion:")
        print(f"  - Enabled: {daily_tasks.get('enabled', 'N/A')}")
        print(f"  - Priority: {daily_tasks.get('priority', 'N/A')}")
        print(f"  - Cooldown: {daily_tasks.get('cooldown', 'N/A')}")
        print(f"  - Manual trigger: {daily_tasks.get('manual_trigger', 'N/A')}")
        print(f"  - Templates: {len(daily_tasks.get('templates', []))}")
        print(f"  - Click coordinates: {len(daily_tasks.get('click_coordinates', []))}")
        print(f"  - Text patterns: {len(daily_tasks.get('text_patterns', []))}")
        
        # Show first few coordinates
        coords = daily_tasks.get('click_coordinates', [])
        if coords:
            print(f"\n📍 First 3 coordinates:")
            for i, coord in enumerate(coords[:3]):
                print(f"  {i+1}. {coord.get('name', 'unknown')}: ({coord.get('x', 0)}, {coord.get('y', 0)}) - {coord.get('description', 'No description')}")
        
        return True
    else:
        print("❌ daily_tasks not found in legacy config")
        return False

def simulate_conversion():
    """Simulate the conversion process"""
    print("\n🔄 Simulating Conversion Process")
    print("=" * 40)
    
    # This mimics the _convert_legacy_module function
    with open("module_configs.json", 'r') as f:
        legacy_config = json.load(f)
    
    if "daily_tasks" not in legacy_config:
        print("❌ daily_tasks not found")
        return False
    
    daily_tasks = legacy_config["daily_tasks"]
    
    # Convert using same logic as the actual function
    unified = {
        'enabled': daily_tasks.get('enabled', True),
        'priority': daily_tasks.get('priority', 0),
        'cooldown': daily_tasks.get('cooldown', 0.0),
        'manual_trigger': daily_tasks.get('manual_trigger', False),
        'settings': {},
        'templates': {},
        'coordinates': {},
        'scan_regions': {}
    }
    
    # Convert templates
    for template in daily_tasks.get('templates', []):
        template_name = template.get('name', 'unknown')
        unified['templates'][template_name] = {
            'threshold': template.get('threshold', 0.8),
            'required': template.get('required', False),
            'description': template.get('description', ''),
            'scanner_priority': 0,
            'scanner_enabled': True,
            'scanner_action': 'default'
        }
    
    # Convert coordinates
    for coord in daily_tasks.get('click_coordinates', []):
        coord_name = coord.get('name', 'unknown')
        unified['coordinates'][coord_name] = {
            'x': coord.get('x', 0),
            'y': coord.get('y', 0),
            'delay': coord.get('delay', 1.0),
            'repeat': coord.get('repeat', 1),
            'use_esc_key': coord.get('use_esc_key', False),
            'description': coord.get('description', ''),
            'enabled': coord.get('enabled', True)
        }
    
    # Convert text patterns to scan regions
    for i, pattern in enumerate(daily_tasks.get('text_patterns', [])):
        region_name = f"text_pattern_{i+1}"
        unified['scan_regions'][region_name] = {
            'x': 0,
            'y': 0,
            'width': 200,
            'height': 50,
            'description': f"Text pattern: {pattern.get('text', 'unknown')}"
        }
    
    # Add module-specific settings
    if 'manual_trigger' in daily_tasks:
        unified['settings']['manual_trigger'] = daily_tasks['manual_trigger']
    
    print("✅ Conversion completed successfully!")
    print(f"📊 Converted data summary:")
    print(f"  - Templates: {len(unified['templates'])}")
    print(f"  - Coordinates: {len(unified['coordinates'])}")
    print(f"  - Scan regions: {len(unified['scan_regions'])}")
    print(f"  - Settings: {len(unified['settings'])}")
    
    if unified['coordinates']:
        print(f"\n📍 Converted coordinates:")
        for name, coord in list(unified['coordinates'].items())[:5]:  # Show first 5
            print(f"  - {name}: ({coord['x']}, {coord['y']}) delay={coord['delay']}s")
    
    return True

def main():
    """Run all tests"""
    success1 = test_legacy_conversion()
    success2 = simulate_conversion()
    
    if success1 and success2:
        print(f"\n🎉 All tests passed!")
        print(f"✅ Legacy modules should now appear with full data in Configuration Helper")
        return True
    else:
        print(f"\n❌ Some tests failed")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
