#!/usr/bin/env python3
"""Debug import test"""

try:
    print("Importing MapTradeModule...")
    from modules.map_trade import MapTradeModule
    print("✅ Import successful")
    
    # Check if the methods exist
    print("Checking methods...")
    module_class = MapTradeModule
    
    if hasattr(module_class, '_load_good_setting_map_coordinates'):
        print("✅ _load_good_setting_map_coordinates method exists")
    else:
        print("❌ _load_good_setting_map_coordinates method missing")
    
    if hasattr(module_class, '_initialize_fast_map_ocr'):
        print("✅ _initialize_fast_map_ocr method exists")
    else:
        print("❌ _initialize_fast_map_ocr method missing")
    
    print("Creating instance...")
    # This might fail, but let's see where
    instance = MapTradeModule()
    print("✅ Instance created successfully")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
