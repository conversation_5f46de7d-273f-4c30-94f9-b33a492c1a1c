#!/usr/bin/env python3
"""
Test script to verify the dig module and map trade ESC recovery fixes
"""

import logging
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dig_module_improvements():
    """Test that dig module properly continues scanning after opening chat"""
    try:
        logger.info("🧪 Testing Dig Module Improvements...")
        
        from modules.dig import DigModule
        from main_controller import MainController
        from centralized_template_scanner import CentralizedTemplateScanner
        
        # Create controller and scanner
        main_controller = MainController()
        centralized_scanner = CentralizedTemplateScanner()
        
        # Create dig module
        dig_module = DigModule()
        dig_module.controller = main_controller
        main_controller.centralized_scanner = centralized_scanner
        
        logger.info("✅ Dig module initialized with controller references")
        
        # Test that the new methods exist
        if hasattr(dig_module, '_trigger_esc_recovery'):
            logger.info("✅ _trigger_esc_recovery method exists")
        else:
            logger.error("❌ _trigger_esc_recovery method missing")
            return False
            
        # Test that centralized scanner has new methods
        if hasattr(centralized_scanner, 'get_current_screen_data'):
            logger.info("✅ get_current_screen_data method exists")
        else:
            logger.error("❌ get_current_screen_data method missing")
            return False
            
        if hasattr(centralized_scanner, 'trigger_esc_recovery'):
            logger.info("✅ trigger_esc_recovery method exists")
        else:
            logger.error("❌ trigger_esc_recovery method missing")
            return False
        
        logger.info("✅ All dig module improvements verified")
        return True
        
    except Exception as e:
        logger.error(f"❌ Dig module test failed: {e}")
        return False

def test_map_trade_esc_recovery():
    """Test that map trade module has ESC recovery after completion"""
    try:
        logger.info("🧪 Testing Map Trade ESC Recovery...")
        
        from modules.map_trade import MapTradeModule
        
        # Create map trade module
        map_trade = MapTradeModule()
        
        # Test that the new method exists
        if hasattr(map_trade, '_execute_esc_recovery_after_completion'):
            logger.info("✅ _execute_esc_recovery_after_completion method exists")
        else:
            logger.error("❌ _execute_esc_recovery_after_completion method missing")
            return False
        
        logger.info("✅ Map trade ESC recovery method verified")
        return True
        
    except Exception as e:
        logger.error(f"❌ Map trade test failed: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("🚀 Testing Dig Module and Map Trade Fixes...")
    logger.info("=" * 60)
    
    # Test dig module improvements
    dig_test_ok = test_dig_module_improvements()
    
    # Test map trade ESC recovery
    map_trade_test_ok = test_map_trade_esc_recovery()
    
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST RESULTS:")
    logger.info(f"Dig Module Improvements: {'✅ PASSED' if dig_test_ok else '❌ FAILED'}")
    logger.info(f"Map Trade ESC Recovery: {'✅ PASSED' if map_trade_test_ok else '❌ FAILED'}")
    
    if dig_test_ok and map_trade_test_ok:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("💡 Expected behavior:")
        logger.info("   • Dig module will now scan 10 times for treasure templates")
        logger.info("   • If no treasure found, it will trigger ESC recovery")
        logger.info("   • Map trade will execute ESC recovery after completion")
        logger.info("   • Both modules use centralized ESC recovery system")
    else:
        logger.warning("\n⚠️ Some tests failed - manual investigation needed")
    
    return dig_test_ok and map_trade_test_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
