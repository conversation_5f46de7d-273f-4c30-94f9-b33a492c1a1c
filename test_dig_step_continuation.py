#!/usr/bin/env python3
"""
Test script to verify dig module step continuation logic
"""

import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dig_step_continuation():
    """Test that dig module continues through steps correctly"""
    try:
        logger.info("🧪 Testing Dig Module Step Continuation...")
        
        from modules.dig import DigModule
        
        # Create dig module
        dig_module = DigModule()
        
        # Mock the _safe_click method to avoid actual clicking
        def mock_safe_click(x, y, desc=""):
            logger.info(f"Mock click at ({x}, {y}) - {desc}")
            return True
        
        dig_module._safe_click = mock_safe_click
        
        # Test scenario: Initial dig trigger
        logger.info("\n--- Test 1: Initial Dig Trigger ---")
        mock_screen_data = {
            'templates_found': {
                'dig_icon': [{'x': 100, 'y': 100, 'w': 50, 'h': 50}]
            }
        }
        
        # Check if dig can execute (should be True due to dig_icon)
        can_execute = dig_module.custom_can_execute(mock_screen_data)
        logger.info(f"Can execute with dig_icon: {can_execute}")
        
        if not can_execute:
            logger.error("❌ Dig module should be able to execute with dig_icon")
            return False
        
        # Execute first time (should start dig and do step 2)
        result1 = dig_module.custom_execute(mock_screen_data)
        logger.info(f"First execution result: {result1}")
        logger.info(f"Dig in progress: {dig_module.dig_in_progress}")
        logger.info(f"Current step: {dig_module.current_step}")
        
        if not result1:
            logger.error("❌ First execution should return True")
            return False
        
        if not dig_module.dig_in_progress:
            logger.error("❌ Dig should be in progress after first execution")
            return False
        
        if dig_module.current_step != dig_module.STEP_FIND_TREASURE:
            logger.error(f"❌ Should be at STEP_FIND_TREASURE, but at step {dig_module.current_step}")
            return False
        
        # Test scenario: Second call (dig in progress, no templates)
        logger.info("\n--- Test 2: Dig In Progress, No Treasure Templates ---")
        mock_screen_data_no_treasure = {
            'templates_found': {}  # No treasure templates
        }
        
        # Check if dig can still execute (should be True due to dig_in_progress)
        can_execute2 = dig_module.custom_can_execute(mock_screen_data_no_treasure)
        logger.info(f"Can execute while in progress: {can_execute2}")
        
        if not can_execute2:
            logger.error("❌ Dig module should be able to execute while in progress")
            return False
        
        # Execute second time (should continue treasure scanning)
        result2 = dig_module.custom_execute(mock_screen_data_no_treasure)
        logger.info(f"Second execution result: {result2}")
        logger.info(f"Treasure scan attempts: {dig_module.treasure_scan_attempts}")
        
        if not result2:
            logger.error("❌ Second execution should return True")
            return False
        
        if dig_module.treasure_scan_attempts != 1:
            logger.error(f"❌ Should have 1 treasure scan attempt, but has {dig_module.treasure_scan_attempts}")
            return False
        
        # Test scenario: After 10 failed scans
        logger.info("\n--- Test 3: After 10 Failed Treasure Scans ---")
        
        # Mock the ESC recovery method
        esc_recovery_called = False
        def mock_trigger_esc_recovery():
            nonlocal esc_recovery_called
            esc_recovery_called = True
            logger.info("✅ Mock ESC recovery triggered")
        
        dig_module._trigger_esc_recovery = mock_trigger_esc_recovery
        
        # Set scan attempts to 10
        dig_module.treasure_scan_attempts = 10
        
        # Execute with 10 failed attempts
        result3 = dig_module.custom_execute(mock_screen_data_no_treasure)
        logger.info(f"Third execution result (10 failed scans): {result3}")
        logger.info(f"Dig in progress: {dig_module.dig_in_progress}")
        logger.info(f"ESC recovery called: {esc_recovery_called}")
        
        if not result3:
            logger.error("❌ Third execution should return True")
            return False
        
        if dig_module.dig_in_progress:
            logger.error("❌ Dig should not be in progress after ESC recovery")
            return False
        
        if not esc_recovery_called:
            logger.error("❌ ESC recovery should have been called")
            return False
        
        logger.info("✅ All dig step continuation tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test"""
    logger.info("🚀 Testing Dig Module Step Continuation Logic...")
    logger.info("=" * 60)
    
    success = test_dig_step_continuation()
    
    logger.info("\n" + "=" * 60)
    if success:
        logger.info("🎉 ALL TESTS PASSED!")
        logger.info("💡 Expected behavior:")
        logger.info("   • Dig module returns True for successful step completion")
        logger.info("   • Centralized scanner will call dig module again while dig_in_progress=True")
        logger.info("   • Dig module continues through steps until completion or ESC recovery")
        logger.info("   • After 10 failed treasure scans, triggers ESC recovery and returns control")
    else:
        logger.warning("⚠️ Tests failed - manual investigation needed")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
