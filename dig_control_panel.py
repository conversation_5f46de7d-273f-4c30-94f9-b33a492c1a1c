"""
Dig Module Control Panel - Comprehensive configuration and debugging interface
Provides complete visibility and control over every aspect of dig module execution
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import time
import threading
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime

class DigControlPanel:
    def __init__(self, parent_window, main_controller=None, dig_module=None):
        self.parent = parent_window
        self.main_controller = main_controller
        self.dig_module = dig_module
        self.logger = logging.getLogger("DigControlPanel")

        # Configuration file path
        self.config_file = "dig_module_config.json"
        self.config_data = {}

        # Real-time monitoring
        self.monitoring_active = False
        self.execution_log = []

        # GUI components
        self.window = None
        self.notebook = None

        # Load configuration first
        self._load_configuration()

        # Initialize screen scanner for template testing
        self._initialize_screen_scanner()

        # Initialize or create dig module for testing
        self._initialize_dig_module()

    @property
    def config(self):
        """Get the current configuration data"""
        return self.config_data

        # Load configuration
        self._load_configuration()

    def _initialize_screen_scanner(self):
        """Initialize screen scanner for template testing"""
        try:
            # Try to import and initialize screen scanner
            from screen_scanner import ScreenScanner
            self.screen_scanner = ScreenScanner(
                templates_path='templates/',
                screenshots_path='screenshots/'
            )
            self.logger.info("✅ Screen scanner initialized for template testing")
        except ImportError:
            self.logger.warning("⚠️ Screen scanner not available - template testing will be limited")
            self.screen_scanner = None
        except Exception as e:
            self.logger.error(f"❌ Error initializing screen scanner: {e}")
            self.screen_scanner = None

    def _initialize_dig_module(self):
        """Initialize or create dig module for testing"""
        if not self.dig_module:
            try:
                # Try to create a dig module instance for testing
                from dig_module_refactored import DigModule
                self.dig_module = DigModule()
                self.logger.info("✅ Dig module instance created for testing")
            except ImportError:
                try:
                    # Fallback to original dig module
                    from modules.dig import DigModule
                    self.dig_module = DigModule()
                    self.logger.info("✅ Original dig module loaded for testing")
                except ImportError:
                    self.logger.warning("⚠️ No dig module available - some features will be limited")
                    self.dig_module = None
            except Exception as e:
                self.logger.error(f"❌ Error initializing dig module: {e}")
                self.dig_module = None

    def show_control_panel(self):
        """Show the dig control panel window"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self._create_control_panel_window()
        
    def _create_control_panel_window(self):
        """Create the main control panel window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🔧 Dig Module Control Panel - Complete Configuration & Debugging")
        self.window.geometry("1400x900")
        self.window.configure(bg='#2b2b2b')
        
        # Make window stay on top initially
        self.window.transient(self.parent)
        
        # Create main notebook for tabs
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create all tabs
        self._create_overview_tab()
        self._create_step_configuration_tab()
        self._create_template_testing_tab()
        self._create_real_time_monitoring_tab()
        self._create_advanced_settings_tab()
        
        # Bottom control buttons
        self._create_bottom_controls()
        
    def _create_overview_tab(self):
        """Create overview and quick status tab"""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="📊 Overview")
        
        # Current status section
        status_frame = ttk.LabelFrame(overview_frame, text="Current Status", padding=10)
        status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Status indicators
        self.status_vars = {
            'module_enabled': tk.BooleanVar(value=self.config_data.get('general_settings', {}).get('enabled', True)),
            'current_step': tk.StringVar(value="Not Running"),
            'execution_time': tk.StringVar(value="0s"),
            'last_template_detected': tk.StringVar(value="None"),
            'confidence_score': tk.StringVar(value="0.0")
        }
        
        # Status display
        ttk.Label(status_frame, text="Module Enabled:").grid(row=0, column=0, sticky=tk.W, padx=5)
        ttk.Checkbutton(status_frame, variable=self.status_vars['module_enabled'], 
                       command=self._toggle_module_enabled).grid(row=0, column=1, sticky=tk.W, padx=5)
        
        ttk.Label(status_frame, text="Current Step:").grid(row=1, column=0, sticky=tk.W, padx=5)
        ttk.Label(status_frame, textvariable=self.status_vars['current_step']).grid(row=1, column=1, sticky=tk.W, padx=5)
        
        ttk.Label(status_frame, text="Execution Time:").grid(row=2, column=0, sticky=tk.W, padx=5)
        ttk.Label(status_frame, textvariable=self.status_vars['execution_time']).grid(row=2, column=1, sticky=tk.W, padx=5)
        
        # Quick actions section
        actions_frame = ttk.LabelFrame(overview_frame, text="Quick Actions", padding=10)
        actions_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(actions_frame, text="🔄 Reload Configuration", 
                  command=self._reload_configuration).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="💾 Save Configuration", 
                  command=self._save_configuration).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="📤 Export Config", 
                  command=self._export_configuration).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="📥 Import Config", 
                  command=self._import_configuration).pack(side=tk.LEFT, padx=5)
        
        # Execution log preview
        log_frame = ttk.LabelFrame(overview_frame, text="Recent Execution Log", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Log text widget with scrollbar
        log_scroll_frame = tk.Frame(log_frame)
        log_scroll_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = tk.Text(log_scroll_frame, height=15, bg='#1e1e1e', fg='#ffffff', 
                               font=('Consolas', 9), wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_scroll_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def _create_step_configuration_tab(self):
        """Create step-by-step configuration tab"""
        step_frame = ttk.Frame(self.notebook)
        self.notebook.add(step_frame, text="⚙️ Step Configuration")
        
        # Create scrollable frame
        canvas = tk.Canvas(step_frame, bg='#2b2b2b')
        scrollbar = ttk.Scrollbar(step_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Create configuration sections for each step
        self._create_step_configurations(scrollable_frame)
        
    def _create_step_configurations(self, parent):
        """Create configuration sections for each execution step"""
        steps = self.config_data.get('execution_steps', {})
        
        for step_key, step_config in steps.items():
            step_frame = ttk.LabelFrame(parent, text=f"{step_config.get('name', step_key)}", padding=10)
            step_frame.pack(fill=tk.X, padx=10, pady=5)
            
            # Step enabled checkbox
            enabled_var = tk.BooleanVar(value=step_config.get('enabled', True))
            ttk.Checkbutton(step_frame, text="Enabled", variable=enabled_var).pack(anchor=tk.W)
            
            # Timeout and attempts
            settings_frame = ttk.Frame(step_frame)
            settings_frame.pack(fill=tk.X, pady=5)
            
            ttk.Label(settings_frame, text="Timeout (s):").grid(row=0, column=0, sticky=tk.W, padx=5)
            timeout_var = tk.IntVar(value=step_config.get('timeout', 10))
            ttk.Spinbox(settings_frame, from_=1, to=600, textvariable=timeout_var, width=10).grid(row=0, column=1, padx=5)
            
            ttk.Label(settings_frame, text="Max Attempts:").grid(row=0, column=2, sticky=tk.W, padx=5)
            attempts_var = tk.IntVar(value=step_config.get('max_attempts', 3))
            ttk.Spinbox(settings_frame, from_=1, to=20, textvariable=attempts_var, width=10).grid(row=0, column=3, padx=5)
            
            # Test step button
            ttk.Button(step_frame, text=f"🧪 Test {step_config.get('name', 'Step')}", 
                      command=lambda sk=step_key: self._test_individual_step(sk)).pack(anchor=tk.E, pady=5)
            
    def _create_template_testing_tab(self):
        """Create template testing and debugging tab"""
        template_frame = ttk.Frame(self.notebook)
        self.notebook.add(template_frame, text="🔍 Template Testing")
        
        # Template selection
        selection_frame = ttk.LabelFrame(template_frame, text="Template Selection", padding=10)
        selection_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.selected_template = tk.StringVar()
        template_combo = ttk.Combobox(selection_frame, textvariable=self.selected_template, width=30)
        template_combo['values'] = self._get_all_template_names()
        template_combo.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(selection_frame, text="🔍 Test Template", 
                  command=self._test_template_detection).pack(side=tk.LEFT, padx=5)
        ttk.Button(selection_frame, text="📸 Capture Screenshot", 
                  command=self._capture_template_screenshot).pack(side=tk.LEFT, padx=5)
        
        # Template results
        results_frame = ttk.LabelFrame(template_frame, text="Detection Results", padding=10)
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Results display
        self.template_results = tk.Text(results_frame, height=20, bg='#1e1e1e', fg='#ffffff', 
                                       font=('Consolas', 9), wrap=tk.WORD)
        results_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.template_results.yview)
        self.template_results.configure(yscrollcommand=results_scrollbar.set)
        
        self.template_results.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def _create_real_time_monitoring_tab(self):
        """Create real-time execution monitoring tab"""
        monitor_frame = ttk.Frame(self.notebook)
        self.notebook.add(monitor_frame, text="📡 Real-Time Monitor")
        
        # Monitoring controls
        control_frame = ttk.LabelFrame(monitor_frame, text="Monitoring Controls", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.monitoring_var = tk.BooleanVar()
        ttk.Checkbutton(control_frame, text="Enable Real-Time Monitoring", 
                       variable=self.monitoring_var, command=self._toggle_monitoring).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(control_frame, text="🗑️ Clear Log", 
                  command=self._clear_monitoring_log).pack(side=tk.LEFT, padx=5)
        
        # Live execution display
        execution_frame = ttk.LabelFrame(monitor_frame, text="Live Execution Data", padding=10)
        execution_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.monitor_text = tk.Text(execution_frame, bg='#1e1e1e', fg='#00ff00', 
                                   font=('Consolas', 9), wrap=tk.WORD)
        monitor_scrollbar = ttk.Scrollbar(execution_frame, orient=tk.VERTICAL, command=self.monitor_text.yview)
        self.monitor_text.configure(yscrollcommand=monitor_scrollbar.set)
        
        self.monitor_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        monitor_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def _create_advanced_settings_tab(self):
        """Create advanced settings and debugging tab"""
        advanced_frame = ttk.Frame(self.notebook)
        self.notebook.add(advanced_frame, text="🔧 Advanced Settings")
        
        # OCR Settings
        ocr_frame = ttk.LabelFrame(advanced_frame, text="OCR Timer Detection Settings", padding=10)
        ocr_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Timer regions configuration
        regions_frame = ttk.Frame(ocr_frame)
        regions_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(regions_frame, text="Timer Detection Regions:").pack(anchor=tk.W)
        
        # Add region configuration controls here
        self._create_timer_region_controls(regions_frame)
        
    def _create_timer_region_controls(self, parent):
        """Create timer region configuration controls"""
        timer_settings = self.config_data.get('execution_steps', {}).get('step_5_timer_management', {}).get('timer_detection', {})
        regions = timer_settings.get('regions', [])
        
        for i, region in enumerate(regions):
            region_frame = ttk.LabelFrame(parent, text=f"Region {i+1}: {region.get('name', 'Unknown')}", padding=5)
            region_frame.pack(fill=tk.X, pady=2)
            
            # Coordinates
            coords_frame = ttk.Frame(region_frame)
            coords_frame.pack(fill=tk.X)
            
            coords = region.get('coordinates', [0, 0, 100, 100])
            ttk.Label(coords_frame, text="X:").grid(row=0, column=0, padx=2)
            ttk.Spinbox(coords_frame, from_=0, to=2560, width=8, value=coords[0]).grid(row=0, column=1, padx=2)
            ttk.Label(coords_frame, text="Y:").grid(row=0, column=2, padx=2)
            ttk.Spinbox(coords_frame, from_=0, to=1440, width=8, value=coords[1]).grid(row=0, column=3, padx=2)
            ttk.Label(coords_frame, text="W:").grid(row=0, column=4, padx=2)
            ttk.Spinbox(coords_frame, from_=50, to=500, width=8, value=coords[2]).grid(row=0, column=5, padx=2)
            ttk.Label(coords_frame, text="H:").grid(row=0, column=6, padx=2)
            ttk.Spinbox(coords_frame, from_=50, to=300, width=8, value=coords[3]).grid(row=0, column=7, padx=2)
            
            ttk.Button(coords_frame, text="📸 Test Region",
                      command=lambda r=region: self._test_timer_region(r)).grid(row=0, column=8, padx=5)

    def _create_bottom_controls(self):
        """Create bottom control buttons"""
        control_frame = ttk.Frame(self.window)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(control_frame, text="🚀 Start Dig Module",
                  command=self._manual_start_dig).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="⏹️ Stop Dig Module",
                  command=self._manual_stop_dig).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="🔄 Apply Changes",
                  command=self._apply_configuration_changes).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="❌ Close",
                  command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

    def _load_configuration(self):
        """Load dig module configuration from file"""
        try:
            with open(self.config_file, 'r') as f:
                self.config_data = json.load(f)
            self.logger.info(f"Loaded dig configuration from {self.config_file}")
        except FileNotFoundError:
            self.logger.warning(f"Configuration file {self.config_file} not found, using defaults")
            self.config_data = self._get_default_configuration()
        except Exception as e:
            self.logger.error(f"Error loading configuration: {e}")
            self.config_data = self._get_default_configuration()

    def _save_configuration(self):
        """Save current configuration to file"""
        try:
            self.config_data['last_updated'] = datetime.now().isoformat()
            with open(self.config_file, 'w') as f:
                json.dump(self.config_data, f, indent=2)
            self.logger.info(f"Configuration saved to {self.config_file}")
            messagebox.showinfo("Success", "Configuration saved successfully!")
        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
            messagebox.showerror("Error", f"Failed to save configuration: {str(e)}")

    def _reload_configuration(self):
        """Reload configuration from file"""
        self._load_configuration()
        if self.dig_module:
            self.dig_module.reload_configuration(self.config_data)
        self._update_gui_from_config()
        messagebox.showinfo("Success", "Configuration reloaded successfully!")

    def _export_configuration(self):
        """Export configuration to file"""
        try:
            filename = filedialog.asksaveasfilename(
                title="Export Dig Configuration",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if filename:
                with open(filename, 'w') as f:
                    json.dump(self.config_data, f, indent=2)
                messagebox.showinfo("Success", f"Configuration exported to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export configuration: {str(e)}")

    def _import_configuration(self):
        """Import configuration from file"""
        try:
            filename = filedialog.askopenfilename(
                title="Import Dig Configuration",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if filename:
                with open(filename, 'r') as f:
                    imported_config = json.load(f)
                self.config_data = imported_config
                self._save_configuration()
                self._update_gui_from_config()
                messagebox.showinfo("Success", f"Configuration imported from {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to import configuration: {str(e)}")

    def _get_all_template_names(self) -> List[str]:
        """Get all template names from configuration"""
        templates = []

        # Trigger templates
        for template_name in self.config_data.get('trigger_templates', {}):
            templates.append(template_name)

        # Step templates
        for step_config in self.config_data.get('execution_steps', {}).values():
            # Verification templates
            for template in step_config.get('verification_templates', []):
                if template['name'] not in templates:
                    templates.append(template['name'])

            # Scan templates
            for template in step_config.get('scan_templates', []):
                if template['name'] not in templates:
                    templates.append(template['name'])

            # Send to dig templates
            for template in step_config.get('send_to_dig_templates', []):
                if template['name'] not in templates:
                    templates.append(template['name'])

        return sorted(templates)

    def _test_template_detection(self):
        """Test template detection for selected template"""
        template_name = self.selected_template.get()
        if not template_name:
            messagebox.showwarning("Warning", "Please select a template to test")
            return

        try:
            # Display initial message
            self.template_results.delete(1.0, tk.END)
            self.template_results.insert(tk.END, f"Template Detection Test: {template_name}\n")
            self.template_results.insert(tk.END, f"Timestamp: {datetime.now().strftime('%H:%M:%S')}\n")
            self.template_results.insert(tk.END, "="*50 + "\n\n")

            # Test with available screen scanner
            if self.screen_scanner:
                try:
                    # Use screen scanner for detection
                    screen_data = self.screen_scanner.scan_screen_cache_optimized(
                        required_templates=[template_name],
                        enhanced_detection=True,
                        debug_screenshots=True
                    )

                    templates_found = screen_data.get('templates_found', {})
                    templates_detected = screen_data.get('templates_detected', {})

                    if template_name in templates_found:
                        matches = templates_found[template_name]
                        self.template_results.insert(tk.END, f"✅ FOUND: {len(matches)} matches\n")
                        for i, match in enumerate(matches):
                            x, y, w, h = match[:4]
                            confidence = match[4] if len(match) > 4 else "N/A"
                            self.template_results.insert(tk.END, f"  Match {i+1}: ({x}, {y}) size=({w}x{h}) confidence={confidence}\n")
                    else:
                        self.template_results.insert(tk.END, f"❌ NOT FOUND in templates_found\n")

                    if template_name in templates_detected:
                        detected_value = templates_detected[template_name]
                        self.template_results.insert(tk.END, f"Detection Status: {detected_value}\n")
                    else:
                        self.template_results.insert(tk.END, f"Not in templates_detected\n")

                except Exception as scanner_error:
                    self.template_results.insert(tk.END, f"Screen scanner error: {str(scanner_error)}\n")
                    # Fallback to basic template matching
                    self._fallback_template_test(template_name)
            else:
                # No screen scanner available, use fallback method
                self.template_results.insert(tk.END, "⚠️ Screen scanner not available, using fallback method\n\n")
                self._fallback_template_test(template_name)

        except Exception as e:
            self.template_results.delete(1.0, tk.END)
            self.template_results.insert(tk.END, f"Error testing template: {str(e)}\n")

    def _fallback_template_test(self, template_name):
        """Fallback template testing using basic OpenCV"""
        try:
            import pyautogui
            import cv2
            import numpy as np

            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            # Try to load template
            template_path = f"templates/{template_name}.png"
            if os.path.exists(template_path):
                template = cv2.imread(template_path)
                if template is not None:
                    # Perform template matching
                    result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
                    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                    self.template_results.insert(tk.END, f"Template file: {template_path}\n")
                    self.template_results.insert(tk.END, f"Template size: {template.shape[:2]}\n")
                    self.template_results.insert(tk.END, f"Best match confidence: {max_val:.3f}\n")
                    self.template_results.insert(tk.END, f"Best match location: {max_loc}\n")

                    if max_val > 0.7:
                        self.template_results.insert(tk.END, f"✅ Template likely detected (confidence > 0.7)\n")
                    else:
                        self.template_results.insert(tk.END, f"❌ Template not detected (confidence < 0.7)\n")
                else:
                    self.template_results.insert(tk.END, f"❌ Could not load template image: {template_path}\n")
            else:
                self.template_results.insert(tk.END, f"❌ Template file not found: {template_path}\n")

        except Exception as e:
            self.template_results.insert(tk.END, f"Fallback test error: {str(e)}\n")

    def _capture_template_screenshot(self):
        """Capture screenshot for template analysis"""
        try:
            import pyautogui
            screenshot = pyautogui.screenshot()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshots/dig_template_test_{timestamp}.png"
            screenshot.save(filename)
            messagebox.showinfo("Success", f"Screenshot saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to capture screenshot: {str(e)}")

    def _test_individual_step(self, step_key: str):
        """Test individual execution step"""
        if not self.dig_module:
            messagebox.showerror("Error", "Dig module not available")
            return

        try:
            self.log_text.insert(tk.END, f"\n🧪 Testing step: {step_key}\n")
            self.log_text.insert(tk.END, f"Timestamp: {datetime.now().strftime('%H:%M:%S')}\n")
            self.log_text.see(tk.END)

            # This would call a test method on the dig module
            # Implementation depends on dig module structure
            messagebox.showinfo("Test", f"Step test initiated for {step_key}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to test step: {str(e)}")

    def _test_timer_region(self, region_config: Dict[str, Any]):
        """Test timer detection in specific region"""
        try:
            self.template_results.delete(1.0, tk.END)
            self.template_results.insert(tk.END, f"Timer Region Test: {region_config.get('name', 'Unknown')}\n")
            self.template_results.insert(tk.END, f"Timestamp: {datetime.now().strftime('%H:%M:%S')}\n")
            self.template_results.insert(tk.END, "="*50 + "\n\n")

            # This would test the timer detection logic
            # Implementation depends on dig module timer detection
            self.template_results.insert(tk.END, f"Region: {region_config.get('coordinates', [])}\n")
            self.template_results.insert(tk.END, f"Description: {region_config.get('description', 'N/A')}\n")
            self.template_results.insert(tk.END, "Timer detection test initiated...\n")

        except Exception as e:
            self.template_results.insert(tk.END, f"Error testing timer region: {str(e)}\n")

    def _toggle_module_enabled(self):
        """Toggle dig module enabled state"""
        enabled = self.status_vars['module_enabled'].get()
        self.config_data.setdefault('general_settings', {})['enabled'] = enabled
        if self.dig_module:
            self.dig_module.enabled = enabled
        self.logger.info(f"Dig module {'enabled' if enabled else 'disabled'}")

    def _toggle_monitoring(self):
        """Toggle real-time monitoring"""
        self.monitoring_active = self.monitoring_var.get()
        if self.monitoring_active:
            self._start_monitoring_thread()
        self.logger.info(f"Real-time monitoring {'enabled' if self.monitoring_active else 'disabled'}")

    def _start_monitoring_thread(self):
        """Start monitoring thread"""
        def monitor():
            while self.monitoring_active:
                try:
                    # Update monitoring display
                    self._update_monitoring_display()
                    time.sleep(0.5)  # Update every 500ms
                except Exception as e:
                    self.logger.error(f"Monitoring error: {e}")
                    break

        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()

    def _update_monitoring_display(self):
        """Update real-time monitoring display"""
        if not self.monitoring_active or not hasattr(self, 'monitor_text'):
            return

        try:
            # Get current dig module status
            if self.dig_module:
                current_step = getattr(self.dig_module, 'current_step', 0)
                dig_in_progress = getattr(self.dig_module, 'dig_in_progress', False)

                # Update status variables
                self.status_vars['current_step'].set(f"Step {current_step}" if dig_in_progress else "Not Running")

                # Add to monitoring log
                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                status_line = f"[{timestamp}] Step: {current_step}, Active: {dig_in_progress}\n"

                self.monitor_text.insert(tk.END, status_line)
                self.monitor_text.see(tk.END)

                # Keep only last 100 lines
                lines = self.monitor_text.get(1.0, tk.END).split('\n')
                if len(lines) > 100:
                    self.monitor_text.delete(1.0, f"{len(lines)-100}.0")

        except Exception as e:
            self.logger.error(f"Error updating monitoring display: {e}")

    def _clear_monitoring_log(self):
        """Clear monitoring log"""
        if hasattr(self, 'monitor_text'):
            self.monitor_text.delete(1.0, tk.END)

    def _manual_start_dig(self):
        """Manually start dig module"""
        if self.dig_module:
            try:
                # Create mock screen data for testing
                mock_screen_data = {
                    'templates_found': {
                        'dig_icon': [[100, 200, 50, 30, 0.8]],
                        'test_flight_treasure': [[500, 600, 100, 20, 0.9]]
                    },
                    'templates_detected': {
                        'dig_icon': True,
                        'test_flight_treasure': True
                    }
                }

                # Check if dig can execute
                can_execute = self.dig_module.custom_can_execute(mock_screen_data)

                if can_execute:
                    # Start execution
                    result = self.dig_module.custom_execute(mock_screen_data)
                    self._log_execution("🚀 Manual dig execution started")
                    messagebox.showinfo("Started", "Dig module started manually - check execution log for details")
                else:
                    messagebox.showwarning("Cannot Start", "Dig module cannot execute - check trigger templates")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to start dig module: {str(e)}")
        else:
            messagebox.showerror("Error", "Dig module not available")

    def _manual_stop_dig(self):
        """Manually stop dig module"""
        if self.dig_module:
            try:
                if hasattr(self.dig_module, 'force_stop'):
                    self.dig_module.force_stop()
                elif hasattr(self.dig_module, 'dig_in_progress'):
                    self.dig_module.dig_in_progress = False

                self._log_execution("🛑 Manual dig execution stopped")
                messagebox.showinfo("Stopped", "Dig module stopped")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to stop dig module: {str(e)}")
        else:
            messagebox.showerror("Error", "Dig module not available")

    def _apply_configuration_changes(self):
        """Apply configuration changes to dig module"""
        try:
            self._save_configuration()
            if self.dig_module:
                self.dig_module.reload_configuration(self.config_data)
            messagebox.showinfo("Success", "Configuration changes applied successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply changes: {str(e)}")

    def _update_gui_from_config(self):
        """Update GUI elements from current configuration"""
        # Update status variables
        general_settings = self.config_data.get('general_settings', {})
        self.status_vars['module_enabled'].set(general_settings.get('enabled', True))

    def _get_default_configuration(self) -> Dict[str, Any]:
        """Get default configuration if file doesn't exist"""
        return {
            "config_version": "1.0.0",
            "general_settings": {
                "enabled": True,
                "priority": -1,
                "debug_mode": True
            },
            "trigger_templates": {},
            "execution_steps": {}
        }

    def _log_execution(self, message: str):
        """Log execution message with timestamp"""
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        log_entry = f"[{timestamp}] {message}"
        self.execution_log.append(log_entry)

        # Keep only last 100 log entries
        if len(self.execution_log) > 100:
            self.execution_log = self.execution_log[-100:]

        # Update log display if available
        if hasattr(self, 'log_text') and self.log_text:
            try:
                self.log_text.insert(tk.END, log_entry + "\n")
                self.log_text.see(tk.END)

                # Keep only last 100 lines in display
                lines = self.log_text.get(1.0, tk.END).split('\n')
                if len(lines) > 100:
                    self.log_text.delete(1.0, f"{len(lines)-100}.0")
            except:
                pass  # GUI might not be ready yet
