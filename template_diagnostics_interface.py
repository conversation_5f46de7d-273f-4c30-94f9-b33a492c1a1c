"""
Advanced Template Detection Diagnostics Interface
Comprehensive template testing tools with live detection, multiple OCR engines,
confidence scoring, preprocessing options, and detailed diagnostic logging
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import cv2
import numpy as np
import pyautogui
import os
import time
import json
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
import threading
from PIL import Image, ImageTk

# Template and debug folders
TEMPLATE_FOLDER = "templates"
DEBUG_FOLDER = "debug_images"

class TemplateDetectionDiagnostics:
    """Advanced template detection diagnostics interface"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.window = None
        
        # Detection state
        self.live_detection = False
        self.current_template = None
        self.current_screenshot = None
        self.detection_results = {}
        
        # Configuration variables
        self.template_threshold = tk.DoubleVar(value=0.8)
        self.multi_scale_enabled = tk.BooleanVar(value=True)
        self.feature_detection_enabled = tk.BooleanVar(value=True)
        self.preprocessing_enabled = tk.BooleanVar(value=False)
        self.brightness_adjustment = tk.DoubleVar(value=1.0)
        self.contrast_adjustment = tk.DoubleVar(value=1.0)
        self.gaussian_blur = tk.IntVar(value=0)
        self.edge_detection = tk.BooleanVar(value=False)
        self.rotation_tolerance = tk.IntVar(value=0)
        self.scale_min = tk.DoubleVar(value=0.5)
        self.scale_max = tk.DoubleVar(value=2.0)
        self.scale_step = tk.DoubleVar(value=0.1)
        
        # OCR configuration
        self.ocr_enabled = tk.BooleanVar(value=False)
        self.tesseract_enabled = tk.BooleanVar(value=True)
        self.easyocr_enabled = tk.BooleanVar(value=True)
        self.paddleocr_enabled = tk.BooleanVar(value=True)
        self.ocr_confidence_threshold = tk.DoubleVar(value=0.7)
        
        # Results storage
        self.detection_history = []
        self.performance_metrics = {}
        
        # Create debug folder if it doesn't exist
        os.makedirs(DEBUG_FOLDER, exist_ok=True)
        
    def show_diagnostics_window(self):
        """Show the template diagnostics window"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("Advanced Template Detection Diagnostics")
        self.window.geometry("1400x900")
        self.window.resizable(True, True)
        
        self._create_interface()
        
        # Center window
        if self.parent:
            self.window.transient(self.parent)
            self.window.grab_set()
    
    def _create_interface(self):
        """Create the diagnostics interface"""
        # Main container with paned window
        main_paned = ttk.PanedWindow(self.window, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Controls and Configuration
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=1)
        
        # Right panel - Results and Visualization
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=2)
        
        self._create_control_panel(left_frame)
        self._create_results_panel(right_frame)
        
    def _create_control_panel(self, parent):
        """Create the control panel with all configuration options"""
        # Template Selection
        template_frame = ttk.LabelFrame(parent, text="Template Selection", padding="5")
        template_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(template_frame, text="Template:").pack(anchor=tk.W)
        self.template_var = tk.StringVar()
        template_combo = ttk.Combobox(template_frame, textvariable=self.template_var, width=40)
        template_combo.pack(fill=tk.X, pady=(2, 5))
        
        # Load available templates
        self._load_template_list(template_combo)
        
        # Template actions
        template_actions = ttk.Frame(template_frame)
        template_actions.pack(fill=tk.X)
        
        ttk.Button(template_actions, text="Load Template", 
                  command=self._load_selected_template).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(template_actions, text="Browse...", 
                  command=self._browse_template).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(template_actions, text="Refresh List", 
                  command=lambda: self._load_template_list(template_combo)).pack(side=tk.LEFT)
        
        # Detection Configuration
        config_frame = ttk.LabelFrame(parent, text="Detection Configuration", padding="5")
        config_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Basic settings
        ttk.Label(config_frame, text="Confidence Threshold:").pack(anchor=tk.W)
        threshold_frame = ttk.Frame(config_frame)
        threshold_frame.pack(fill=tk.X, pady=(2, 5))
        
        ttk.Scale(threshold_frame, from_=0.1, to=1.0, variable=self.template_threshold,
                 orient=tk.HORIZONTAL).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(threshold_frame, textvariable=self.template_threshold,
                 width=6).pack(side=tk.RIGHT)
        
        # Multi-scale detection
        ttk.Checkbutton(config_frame, text="Multi-Scale Detection", 
                       variable=self.multi_scale_enabled).pack(anchor=tk.W)
        
        # Scale range
        scale_frame = ttk.Frame(config_frame)
        scale_frame.pack(fill=tk.X, pady=(2, 5))
        
        ttk.Label(scale_frame, text="Scale Range:").pack(side=tk.LEFT)
        ttk.Spinbox(scale_frame, from_=0.1, to=2.0, increment=0.1, width=6,
                   textvariable=self.scale_min).pack(side=tk.LEFT, padx=(5, 2))
        ttk.Label(scale_frame, text="to").pack(side=tk.LEFT, padx=2)
        ttk.Spinbox(scale_frame, from_=0.1, to=3.0, increment=0.1, width=6,
                   textvariable=self.scale_max).pack(side=tk.LEFT, padx=(2, 5))
        ttk.Label(scale_frame, text="step").pack(side=tk.LEFT, padx=2)
        ttk.Spinbox(scale_frame, from_=0.01, to=0.5, increment=0.01, width=6,
                   textvariable=self.scale_step).pack(side=tk.LEFT, padx=2)
        
        # Feature detection
        ttk.Checkbutton(config_frame, text="Feature-Based Detection", 
                       variable=self.feature_detection_enabled).pack(anchor=tk.W)
        
        # Rotation tolerance
        rotation_frame = ttk.Frame(config_frame)
        rotation_frame.pack(fill=tk.X, pady=(2, 5))
        
        ttk.Label(rotation_frame, text="Rotation Tolerance (degrees):").pack(side=tk.LEFT)
        ttk.Spinbox(rotation_frame, from_=0, to=45, increment=1, width=6,
                   textvariable=self.rotation_tolerance).pack(side=tk.RIGHT)
        
        # Image Preprocessing
        preprocess_frame = ttk.LabelFrame(parent, text="Image Preprocessing", padding="5")
        preprocess_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Checkbutton(preprocess_frame, text="Enable Preprocessing", 
                       variable=self.preprocessing_enabled).pack(anchor=tk.W)
        
        # Brightness and contrast
        brightness_frame = ttk.Frame(preprocess_frame)
        brightness_frame.pack(fill=tk.X, pady=(2, 2))
        
        ttk.Label(brightness_frame, text="Brightness:").pack(side=tk.LEFT)
        ttk.Scale(brightness_frame, from_=0.1, to=3.0, variable=self.brightness_adjustment,
                 orient=tk.HORIZONTAL, length=150).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(brightness_frame, textvariable=self.brightness_adjustment,
                 width=6).pack(side=tk.RIGHT)
        
        contrast_frame = ttk.Frame(preprocess_frame)
        contrast_frame.pack(fill=tk.X, pady=(2, 2))
        
        ttk.Label(contrast_frame, text="Contrast:").pack(side=tk.LEFT)
        ttk.Scale(contrast_frame, from_=0.1, to=3.0, variable=self.contrast_adjustment,
                 orient=tk.HORIZONTAL, length=150).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(contrast_frame, textvariable=self.contrast_adjustment,
                 width=6).pack(side=tk.RIGHT)
        
        # Gaussian blur
        blur_frame = ttk.Frame(preprocess_frame)
        blur_frame.pack(fill=tk.X, pady=(2, 2))
        
        ttk.Label(blur_frame, text="Gaussian Blur:").pack(side=tk.LEFT)
        ttk.Spinbox(blur_frame, from_=0, to=15, increment=2, width=6,
                   textvariable=self.gaussian_blur).pack(side=tk.RIGHT)
        
        # Edge detection
        ttk.Checkbutton(preprocess_frame, text="Edge Detection", 
                       variable=self.edge_detection).pack(anchor=tk.W)
        
        # OCR Configuration
        ocr_frame = ttk.LabelFrame(parent, text="OCR Configuration", padding="5")
        ocr_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Checkbutton(ocr_frame, text="Enable OCR Analysis", 
                       variable=self.ocr_enabled).pack(anchor=tk.W)
        
        # OCR engines
        engines_frame = ttk.Frame(ocr_frame)
        engines_frame.pack(fill=tk.X, pady=(2, 5))
        
        ttk.Checkbutton(engines_frame, text="Tesseract", 
                       variable=self.tesseract_enabled).pack(side=tk.LEFT)
        ttk.Checkbutton(engines_frame, text="EasyOCR", 
                       variable=self.easyocr_enabled).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Checkbutton(engines_frame, text="PaddleOCR", 
                       variable=self.paddleocr_enabled).pack(side=tk.LEFT, padx=(10, 0))
        
        # OCR confidence
        ocr_conf_frame = ttk.Frame(ocr_frame)
        ocr_conf_frame.pack(fill=tk.X, pady=(2, 5))
        
        ttk.Label(ocr_conf_frame, text="OCR Confidence:").pack(side=tk.LEFT)
        ttk.Scale(ocr_conf_frame, from_=0.1, to=1.0, variable=self.ocr_confidence_threshold,
                 orient=tk.HORIZONTAL, length=150).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(ocr_conf_frame, textvariable=self.ocr_confidence_threshold,
                 width=6).pack(side=tk.RIGHT)
        
        # Action Buttons
        action_frame = ttk.LabelFrame(parent, text="Actions", padding="5")
        action_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(action_frame, text="Single Test", 
                  command=self._run_single_test).pack(fill=tk.X, pady=(0, 2))
        ttk.Button(action_frame, text="Start Live Detection", 
                  command=self._start_live_detection).pack(fill=tk.X, pady=(0, 2))
        ttk.Button(action_frame, text="Stop Live Detection", 
                  command=self._stop_live_detection).pack(fill=tk.X, pady=(0, 2))
        ttk.Button(action_frame, text="Comprehensive Test", 
                  command=self._run_comprehensive_test).pack(fill=tk.X, pady=(0, 2))
        ttk.Button(action_frame, text="Save Results", 
                  command=self._save_results).pack(fill=tk.X, pady=(0, 2))
        ttk.Button(action_frame, text="Clear Results", 
                  command=self._clear_results).pack(fill=tk.X)
    
    def _create_results_panel(self, parent):
        """Create the results and visualization panel"""
        # Create notebook for different result views
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Detection Results Tab
        results_frame = ttk.Frame(notebook)
        notebook.add(results_frame, text="Detection Results")
        
        # Results text with scrollbar
        results_container = ttk.Frame(results_frame)
        results_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.results_text = tk.Text(results_container, wrap=tk.WORD, font=('Consolas', 10))
        results_scrollbar = ttk.Scrollbar(results_container, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=results_scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Visual Comparison Tab
        visual_frame = ttk.Frame(notebook)
        notebook.add(visual_frame, text="Visual Comparison")
        
        # Image display area
        image_paned = ttk.PanedWindow(visual_frame, orient=tk.HORIZONTAL)
        image_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Template image
        template_frame = ttk.LabelFrame(image_paned, text="Template", padding="5")
        image_paned.add(template_frame, weight=1)
        
        self.template_label = ttk.Label(template_frame, text="No template loaded")
        self.template_label.pack(expand=True)
        
        # Screenshot with detections
        screenshot_frame = ttk.LabelFrame(image_paned, text="Screenshot with Detections", padding="5")
        image_paned.add(screenshot_frame, weight=2)

        # Add legend for detection colors
        legend_frame = ttk.Frame(screenshot_frame)
        legend_frame.pack(fill=tk.X, pady=(0, 5))

        legend_text = "Legend: 🟢 Standard  🔵 Multi-Scale  🟣 Feature-Based  🟡 OCR  🟠 Below Threshold"
        ttk.Label(legend_frame, text=legend_text, font=('Consolas', 8)).pack()

        self.screenshot_label = ttk.Label(screenshot_frame, text="No screenshot captured")
        self.screenshot_label.pack(expand=True)
        
        # Performance Metrics Tab
        metrics_frame = ttk.Frame(notebook)
        notebook.add(metrics_frame, text="Performance Metrics")
        
        # Metrics display
        self.metrics_text = tk.Text(metrics_frame, wrap=tk.WORD, font=('Consolas', 10))
        metrics_scrollbar = ttk.Scrollbar(metrics_frame, orient=tk.VERTICAL, command=self.metrics_text.yview)
        self.metrics_text.configure(yscrollcommand=metrics_scrollbar.set)
        
        self.metrics_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        metrics_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

    def _load_template_list(self, combo_widget):
        """Load available templates into the combobox"""
        try:
            templates = []
            if os.path.exists(TEMPLATE_FOLDER):
                for file in os.listdir(TEMPLATE_FOLDER):
                    if file.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif')):
                        templates.append(os.path.splitext(file)[0])

            combo_widget['values'] = sorted(templates)
            if templates:
                combo_widget.set(templates[0])
        except Exception as e:
            print(f"Error loading template list: {e}")

    def _browse_template(self):
        """Browse for a template file"""
        file_path = filedialog.askopenfilename(
            title="Select Template Image",
            initialdir=TEMPLATE_FOLDER,
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.bmp *.gif"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            template_name = os.path.splitext(os.path.basename(file_path))[0]
            self.template_var.set(template_name)
            self._load_selected_template()

    def _load_selected_template(self):
        """Load the selected template"""
        template_name = self.template_var.get()
        if not template_name:
            return

        # Find template file
        template_path = None
        for ext in ['.png', '.jpg', '.jpeg', '.bmp', '.gif']:
            path = os.path.join(TEMPLATE_FOLDER, f"{template_name}{ext}")
            if os.path.exists(path):
                template_path = path
                break

        if not template_path:
            messagebox.showerror("Error", f"Template file not found: {template_name}")
            return

        # Load template
        try:
            self.current_template = cv2.imread(template_path)
            if self.current_template is None:
                messagebox.showerror("Error", f"Failed to load template: {template_path}")
                return

            # Display template in GUI
            self._display_template_image()

            # Update results
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, f"✅ Template loaded: {template_name}\n")
            self.results_text.insert(tk.END, f"   File: {os.path.basename(template_path)}\n")
            self.results_text.insert(tk.END, f"   Size: {self.current_template.shape[1]}x{self.current_template.shape[0]}\n")
            self.results_text.insert(tk.END, f"   Channels: {self.current_template.shape[2] if len(self.current_template.shape) > 2 else 1}\n\n")

        except Exception as e:
            messagebox.showerror("Error", f"Error loading template: {str(e)}")

    def _display_template_image(self):
        """Display the template image in the GUI with enhanced preview"""
        if self.current_template is None:
            return

        try:
            # Convert BGR to RGB for display
            template_rgb = cv2.cvtColor(self.current_template, cv2.COLOR_BGR2RGB)
            original_height, original_width = template_rgb.shape[:2]

            # Create enhanced template display
            display_template = template_rgb.copy()

            # Add border for better visibility
            border_size = 5
            border_color = [100, 100, 100]  # Gray border
            display_template = cv2.copyMakeBorder(
                display_template, border_size, border_size, border_size, border_size,
                cv2.BORDER_CONSTANT, value=border_color
            )

            # Resize for display while maintaining aspect ratio
            height, width = display_template.shape[:2]
            max_size = 300  # Increased size for better visibility

            if width > max_size or height > max_size:
                scale = min(max_size / width, max_size / height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                display_template = cv2.resize(display_template, (new_width, new_height), interpolation=cv2.INTER_AREA)
            else:
                # If template is small, scale it up for better visibility
                if width < 100 and height < 100:
                    scale = min(200 / width, 200 / height)
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    display_template = cv2.resize(display_template, (new_width, new_height), interpolation=cv2.INTER_NEAREST)

            # Add template information overlay
            info_height = 60
            info_image = np.zeros((info_height, display_template.shape[1], 3), dtype=np.uint8)
            info_image.fill(40)  # Dark gray background

            # Add template info text
            template_name = self.template_var.get()
            info_text = f"Template: {template_name}"
            size_text = f"Size: {original_width}x{original_height}px"

            # Draw info text
            cv2.putText(info_image, info_text, (10, 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(info_image, size_text, (10, 40),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)

            # Combine template and info
            final_image = np.vstack([info_image, display_template])

            # Convert to PIL Image and then to PhotoImage
            pil_image = Image.fromarray(final_image)
            photo = ImageTk.PhotoImage(pil_image)

            # Update label
            self.template_label.configure(image=photo, text="")
            self.template_label.image = photo  # Keep a reference

        except Exception as e:
            print(f"Error displaying template image: {e}")
            self.template_label.configure(text=f"Error displaying template: {str(e)}")

    def _run_single_test(self):
        """Run a single detection test"""
        if self.current_template is None:
            messagebox.showwarning("Warning", "Please load a template first")
            return

        try:
            # Capture screenshot
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            self.current_screenshot = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            # Run detection
            results = self._perform_detection(self.current_screenshot, self.current_template)

            # Display results
            self._display_detection_results(results)

            # Update visual comparison
            self._update_visual_comparison(results)

        except Exception as e:
            messagebox.showerror("Error", f"Detection test failed: {str(e)}")

    def _perform_detection(self, screenshot, template):
        """Perform comprehensive template detection"""
        results = {
            'timestamp': datetime.now(),
            'template_size': template.shape,
            'screenshot_size': screenshot.shape,
            'methods': {},
            'preprocessing_applied': [],
            'best_match': None,
            'all_matches': []
        }

        # Apply preprocessing if enabled
        processed_screenshot = screenshot.copy()
        processed_template = template.copy()

        if self.preprocessing_enabled.get():
            processed_screenshot, processed_template = self._apply_preprocessing(
                processed_screenshot, processed_template, results
            )

        # Method 1: Standard Template Matching
        if True:  # Always run standard matching
            standard_results = self._standard_template_matching(
                processed_screenshot, processed_template
            )
            results['methods']['standard'] = standard_results

        # Method 2: Multi-Scale Template Matching
        if self.multi_scale_enabled.get():
            multiscale_results = self._multiscale_template_matching(
                processed_screenshot, processed_template
            )
            results['methods']['multiscale'] = multiscale_results

        # Method 3: Feature-Based Detection
        if self.feature_detection_enabled.get():
            feature_results = self._feature_based_detection(
                processed_screenshot, processed_template
            )
            results['methods']['feature_based'] = feature_results

        # Method 4: OCR Analysis (if enabled and applicable)
        if self.ocr_enabled.get():
            ocr_results = self._ocr_analysis(processed_screenshot, processed_template)
            results['methods']['ocr'] = ocr_results

        # Determine best match
        results['best_match'] = self._determine_best_match(results['methods'])

        # Collect all matches above threshold
        threshold = self.template_threshold.get()
        for method_name, method_results in results['methods'].items():
            if method_results and method_results.get('confidence', 0) >= threshold:
                match_info = {
                    'method': method_name,
                    'confidence': method_results['confidence'],
                    'location': method_results.get('location'),
                    'scale': method_results.get('scale', 1.0),
                    'processing_time': method_results.get('processing_time', 0)
                }
                results['all_matches'].append(match_info)

        # Sort matches by confidence
        results['all_matches'].sort(key=lambda x: x['confidence'], reverse=True)

        return results

    def _apply_preprocessing(self, screenshot, template, results):
        """Apply image preprocessing"""
        processed_screenshot = screenshot.copy()
        processed_template = template.copy()

        # Brightness adjustment
        if self.brightness_adjustment.get() != 1.0:
            brightness = self.brightness_adjustment.get()
            processed_screenshot = cv2.convertScaleAbs(processed_screenshot, alpha=brightness, beta=0)
            processed_template = cv2.convertScaleAbs(processed_template, alpha=brightness, beta=0)
            results['preprocessing_applied'].append(f"Brightness: {brightness:.2f}")

        # Contrast adjustment
        if self.contrast_adjustment.get() != 1.0:
            contrast = self.contrast_adjustment.get()
            processed_screenshot = cv2.convertScaleAbs(processed_screenshot, alpha=contrast, beta=0)
            processed_template = cv2.convertScaleAbs(processed_template, alpha=contrast, beta=0)
            results['preprocessing_applied'].append(f"Contrast: {contrast:.2f}")

        # Gaussian blur
        if self.gaussian_blur.get() > 0:
            blur_size = self.gaussian_blur.get()
            if blur_size % 2 == 0:
                blur_size += 1  # Must be odd
            processed_screenshot = cv2.GaussianBlur(processed_screenshot, (blur_size, blur_size), 0)
            processed_template = cv2.GaussianBlur(processed_template, (blur_size, blur_size), 0)
            results['preprocessing_applied'].append(f"Gaussian Blur: {blur_size}")

        # Edge detection
        if self.edge_detection.get():
            processed_screenshot = cv2.Canny(processed_screenshot, 50, 150)
            processed_template = cv2.Canny(processed_template, 50, 150)
            results['preprocessing_applied'].append("Edge Detection: Canny")

        return processed_screenshot, processed_template

    def _standard_template_matching(self, screenshot, template):
        """Standard OpenCV template matching"""
        start_time = time.time()

        try:
            # Convert to grayscale if needed
            if len(screenshot.shape) == 3:
                screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            else:
                screenshot_gray = screenshot

            if len(template.shape) == 3:
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            else:
                template_gray = template

            # Perform template matching
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            processing_time = time.time() - start_time

            return {
                'confidence': max_val,
                'location': max_loc,
                'template_size': template.shape,
                'method': 'TM_CCOEFF_NORMED',
                'processing_time': processing_time,
                'scale': 1.0
            }

        except Exception as e:
            return {
                'error': str(e),
                'confidence': 0.0,
                'processing_time': time.time() - start_time
            }

    def _multiscale_template_matching(self, screenshot, template):
        """Multi-scale template matching"""
        start_time = time.time()

        try:
            best_match = None
            best_confidence = 0

            # Convert to grayscale
            if len(screenshot.shape) == 3:
                screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            else:
                screenshot_gray = screenshot

            if len(template.shape) == 3:
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            else:
                template_gray = template

            # Test different scales
            scale_min = self.scale_min.get()
            scale_max = self.scale_max.get()
            scale_step = self.scale_step.get()

            scale = scale_min
            while scale <= scale_max:
                # Resize template
                new_width = int(template_gray.shape[1] * scale)
                new_height = int(template_gray.shape[0] * scale)

                if new_width > 0 and new_height > 0:
                    scaled_template = cv2.resize(template_gray, (new_width, new_height))

                    # Skip if template is larger than screenshot
                    if (scaled_template.shape[0] <= screenshot_gray.shape[0] and
                        scaled_template.shape[1] <= screenshot_gray.shape[1]):

                        result = cv2.matchTemplate(screenshot_gray, scaled_template, cv2.TM_CCOEFF_NORMED)
                        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                        if max_val > best_confidence:
                            best_confidence = max_val
                            best_match = {
                                'confidence': max_val,
                                'location': max_loc,
                                'scale': scale,
                                'template_size': scaled_template.shape
                            }

                scale += scale_step

            processing_time = time.time() - start_time

            if best_match:
                best_match.update({
                    'method': 'Multi-Scale TM_CCOEFF_NORMED',
                    'processing_time': processing_time,
                    'scales_tested': int((scale_max - scale_min) / scale_step) + 1
                })
                return best_match
            else:
                return {
                    'confidence': 0.0,
                    'processing_time': processing_time,
                    'scales_tested': int((scale_max - scale_min) / scale_step) + 1,
                    'method': 'Multi-Scale TM_CCOEFF_NORMED'
                }

        except Exception as e:
            return {
                'error': str(e),
                'confidence': 0.0,
                'processing_time': time.time() - start_time
            }

    def _feature_based_detection(self, screenshot, template):
        """Feature-based detection using SIFT/ORB"""
        start_time = time.time()

        try:
            # Convert to grayscale
            if len(screenshot.shape) == 3:
                screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            else:
                screenshot_gray = screenshot

            if len(template.shape) == 3:
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            else:
                template_gray = template

            # Try SIFT first, fall back to ORB if not available
            try:
                detector = cv2.SIFT_create()
                detector_name = "SIFT"
            except:
                detector = cv2.ORB_create()
                detector_name = "ORB"

            # Find keypoints and descriptors
            kp1, des1 = detector.detectAndCompute(template_gray, None)
            kp2, des2 = detector.detectAndCompute(screenshot_gray, None)

            if des1 is None or des2 is None or len(des1) < 4 or len(des2) < 4:
                return {
                    'confidence': 0.0,
                    'processing_time': time.time() - start_time,
                    'method': f'Feature-Based ({detector_name})',
                    'error': 'Insufficient features detected'
                }

            # Match features
            if detector_name == "SIFT":
                matcher = cv2.FlannBasedMatcher()
                matches = matcher.knnMatch(des1, des2, k=2)

                # Apply Lowe's ratio test
                good_matches = []
                for match_pair in matches:
                    if len(match_pair) == 2:
                        m, n = match_pair
                        if m.distance < 0.7 * n.distance:
                            good_matches.append(m)
            else:
                matcher = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
                matches = matcher.match(des1, des2)
                good_matches = sorted(matches, key=lambda x: x.distance)[:50]

            if len(good_matches) < 4:
                return {
                    'confidence': 0.0,
                    'processing_time': time.time() - start_time,
                    'method': f'Feature-Based ({detector_name})',
                    'matches_found': len(good_matches),
                    'error': 'Insufficient good matches'
                }

            # Calculate confidence based on number of good matches
            confidence = min(len(good_matches) / 20.0, 1.0)  # Normalize to 0-1

            # Find homography if enough matches
            if len(good_matches) >= 4:
                src_pts = np.float32([kp1[m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
                dst_pts = np.float32([kp2[m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)

                try:
                    M, mask = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 5.0)
                    if M is not None:
                        # Get template corners
                        h, w = template_gray.shape
                        corners = np.float32([[0, 0], [w, 0], [w, h], [0, h]]).reshape(-1, 1, 2)
                        transformed_corners = cv2.perspectiveTransform(corners, M)

                        # Calculate center point
                        center_x = int(np.mean(transformed_corners[:, 0, 0]))
                        center_y = int(np.mean(transformed_corners[:, 0, 1]))

                        processing_time = time.time() - start_time

                        return {
                            'confidence': confidence,
                            'location': (center_x - w//2, center_y - h//2),  # Top-left corner
                            'center': (center_x, center_y),
                            'corners': transformed_corners.tolist(),
                            'matches_found': len(good_matches),
                            'inliers': int(mask.sum()) if mask is not None else 0,
                            'method': f'Feature-Based ({detector_name})',
                            'processing_time': processing_time
                        }
                except:
                    pass

            processing_time = time.time() - start_time

            return {
                'confidence': confidence,
                'matches_found': len(good_matches),
                'method': f'Feature-Based ({detector_name})',
                'processing_time': processing_time
            }

        except Exception as e:
            return {
                'error': str(e),
                'confidence': 0.0,
                'processing_time': time.time() - start_time
            }

    def _ocr_analysis(self, screenshot, template):
        """OCR analysis for text-based templates"""
        start_time = time.time()
        ocr_results = {
            'method': 'OCR Analysis',
            'processing_time': 0,
            'engines': {}
        }

        try:
            # Convert to grayscale for OCR
            if len(screenshot.shape) == 3:
                screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            else:
                screenshot_gray = screenshot

            # Test different OCR engines
            if self.tesseract_enabled.get():
                ocr_results['engines']['tesseract'] = self._test_tesseract_ocr(screenshot_gray)

            if self.easyocr_enabled.get():
                ocr_results['engines']['easyocr'] = self._test_easyocr_ocr(screenshot_gray)

            if self.paddleocr_enabled.get():
                ocr_results['engines']['paddleocr'] = self._test_paddleocr_ocr(screenshot_gray)

            # Find best OCR result
            best_confidence = 0
            best_result = None

            for engine_name, engine_result in ocr_results['engines'].items():
                if engine_result and engine_result.get('confidence', 0) > best_confidence:
                    best_confidence = engine_result['confidence']
                    best_result = engine_result
                    best_result['best_engine'] = engine_name

            ocr_results['processing_time'] = time.time() - start_time

            if best_result:
                ocr_results.update(best_result)
                ocr_results['confidence'] = best_confidence
            else:
                ocr_results['confidence'] = 0.0

            return ocr_results

        except Exception as e:
            ocr_results['error'] = str(e)
            ocr_results['confidence'] = 0.0
            ocr_results['processing_time'] = time.time() - start_time
            return ocr_results

    def _test_tesseract_ocr(self, image):
        """Test Tesseract OCR"""
        try:
            import pytesseract

            # Multiple PSM modes for different text layouts
            psm_modes = [6, 7, 8, 13]  # Different page segmentation modes
            best_result = None
            best_confidence = 0

            for psm in psm_modes:
                try:
                    config = f'--psm {psm} -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'

                    # Get text with confidence
                    data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT)

                    # Extract text and confidence
                    text_parts = []
                    confidences = []

                    for i, conf in enumerate(data['conf']):
                        if int(conf) > 0:
                            text = data['text'][i].strip()
                            if text:
                                text_parts.append(text)
                                confidences.append(int(conf))

                    if text_parts and confidences:
                        full_text = ' '.join(text_parts)
                        avg_confidence = sum(confidences) / len(confidences) / 100.0  # Normalize to 0-1

                        if avg_confidence > best_confidence:
                            best_confidence = avg_confidence
                            best_result = {
                                'text': full_text,
                                'confidence': avg_confidence,
                                'psm_mode': psm,
                                'word_count': len(text_parts)
                            }

                except Exception:
                    continue

            return best_result

        except ImportError:
            return {'error': 'Tesseract not available', 'confidence': 0.0}
        except Exception as e:
            return {'error': str(e), 'confidence': 0.0}

    def _test_easyocr_ocr(self, image):
        """Test EasyOCR"""
        try:
            import easyocr

            # Initialize reader (cache it for performance)
            if not hasattr(self, '_easyocr_reader'):
                self._easyocr_reader = easyocr.Reader(['en'])

            results = self._easyocr_reader.readtext(image)

            if results:
                # Combine all detected text
                text_parts = []
                confidences = []

                for (bbox, text, confidence) in results:
                    if confidence >= self.ocr_confidence_threshold.get():
                        text_parts.append(text)
                        confidences.append(confidence)

                if text_parts:
                    full_text = ' '.join(text_parts)
                    avg_confidence = sum(confidences) / len(confidences)

                    return {
                        'text': full_text,
                        'confidence': avg_confidence,
                        'detections': len(results),
                        'valid_detections': len(text_parts)
                    }

            return {'confidence': 0.0, 'text': '', 'detections': 0}

        except ImportError:
            return {'error': 'EasyOCR not available', 'confidence': 0.0}
        except Exception as e:
            return {'error': str(e), 'confidence': 0.0}

    def _test_paddleocr_ocr(self, image):
        """Test PaddleOCR"""
        try:
            from paddleocr import PaddleOCR

            # Initialize OCR (cache it for performance)
            if not hasattr(self, '_paddleocr_reader'):
                self._paddleocr_reader = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)

            results = self._paddleocr_reader.ocr(image, cls=True)

            if results and results[0]:
                text_parts = []
                confidences = []

                for line in results[0]:
                    if len(line) >= 2:
                        text = line[1][0]
                        confidence = line[1][1]

                        if confidence >= self.ocr_confidence_threshold.get():
                            text_parts.append(text)
                            confidences.append(confidence)

                if text_parts:
                    full_text = ' '.join(text_parts)
                    avg_confidence = sum(confidences) / len(confidences)

                    return {
                        'text': full_text,
                        'confidence': avg_confidence,
                        'detections': len(results[0]),
                        'valid_detections': len(text_parts)
                    }

            return {'confidence': 0.0, 'text': '', 'detections': 0}

        except ImportError:
            return {'error': 'PaddleOCR not available', 'confidence': 0.0}
        except Exception as e:
            return {'error': str(e), 'confidence': 0.0}

    def _determine_best_match(self, methods_results):
        """Determine the best match from all detection methods"""
        best_match = None
        best_confidence = 0

        for method_name, result in methods_results.items():
            if result and result.get('confidence', 0) > best_confidence:
                best_confidence = result['confidence']
                best_match = result.copy()
                best_match['method'] = method_name

        return best_match

    def _display_detection_results(self, results):
        """Display detection results in the results text area"""
        self.results_text.delete(1.0, tk.END)

        # Header
        timestamp = results['timestamp'].strftime("%H:%M:%S")
        self.results_text.insert(tk.END, f"🔍 TEMPLATE DETECTION RESULTS - {timestamp}\n")
        self.results_text.insert(tk.END, "=" * 60 + "\n\n")

        # Template info
        template_size = results['template_size']
        screenshot_size = results['screenshot_size']
        self.results_text.insert(tk.END, f"📋 Template: {template_size[1]}x{template_size[0]} pixels\n")
        self.results_text.insert(tk.END, f"📺 Screenshot: {screenshot_size[1]}x{screenshot_size[0]} pixels\n")

        # Preprocessing info
        if results['preprocessing_applied']:
            self.results_text.insert(tk.END, f"🔧 Preprocessing: {', '.join(results['preprocessing_applied'])}\n")

        self.results_text.insert(tk.END, f"🎯 Threshold: {self.template_threshold.get():.2f}\n\n")

        # Best match summary
        if results['best_match']:
            best = results['best_match']
            self.results_text.insert(tk.END, f"🏆 BEST MATCH:\n")
            self.results_text.insert(tk.END, f"   Method: {best.get('method', 'Unknown')}\n")
            self.results_text.insert(tk.END, f"   Confidence: {best.get('confidence', 0):.3f}\n")
            if 'location' in best:
                loc = best['location']
                self.results_text.insert(tk.END, f"   Location: ({loc[0]}, {loc[1]})\n")
            if 'scale' in best:
                self.results_text.insert(tk.END, f"   Scale: {best.get('scale', 1.0):.2f}\n")
            self.results_text.insert(tk.END, f"   Time: {best.get('processing_time', 0):.3f}s\n\n")
        else:
            self.results_text.insert(tk.END, f"❌ NO MATCHES FOUND above threshold {self.template_threshold.get():.2f}\n\n")

        # Detailed method results
        self.results_text.insert(tk.END, f"📊 DETAILED RESULTS:\n")
        self.results_text.insert(tk.END, "-" * 40 + "\n")

        for method_name, result in results['methods'].items():
            if result:
                self.results_text.insert(tk.END, f"\n🔸 {method_name.upper()}:\n")

                if 'error' in result:
                    self.results_text.insert(tk.END, f"   ❌ Error: {result['error']}\n")
                else:
                    confidence = result.get('confidence', 0)
                    status = "✅ PASS" if confidence >= self.template_threshold.get() else "❌ FAIL"
                    self.results_text.insert(tk.END, f"   {status} Confidence: {confidence:.3f}\n")

                    if 'location' in result:
                        loc = result['location']
                        self.results_text.insert(tk.END, f"   📍 Location: ({loc[0]}, {loc[1]})\n")

                    if 'scale' in result:
                        self.results_text.insert(tk.END, f"   📏 Scale: {result['scale']:.2f}\n")

                    if 'processing_time' in result:
                        self.results_text.insert(tk.END, f"   ⏱️  Time: {result['processing_time']:.3f}s\n")

                    # Method-specific details
                    if method_name == 'multiscale' and 'scales_tested' in result:
                        self.results_text.insert(tk.END, f"   🔍 Scales tested: {result['scales_tested']}\n")

                    if method_name == 'feature_based':
                        if 'matches_found' in result:
                            self.results_text.insert(tk.END, f"   🎯 Matches: {result['matches_found']}\n")
                        if 'inliers' in result:
                            self.results_text.insert(tk.END, f"   ✅ Inliers: {result['inliers']}\n")

                    if method_name == 'ocr':
                        if 'text' in result:
                            self.results_text.insert(tk.END, f"   📝 Text: '{result['text']}'\n")
                        if 'best_engine' in result:
                            self.results_text.insert(tk.END, f"   🔧 Engine: {result['best_engine']}\n")

        # All matches summary
        if results['all_matches']:
            self.results_text.insert(tk.END, f"\n🎯 ALL VALID MATCHES (above threshold):\n")
            self.results_text.insert(tk.END, "-" * 40 + "\n")

            for i, match in enumerate(results['all_matches'], 1):
                self.results_text.insert(tk.END, f"{i}. {match['method']}: {match['confidence']:.3f}")
                if 'location' in match:
                    loc = match['location']
                    self.results_text.insert(tk.END, f" at ({loc[0]}, {loc[1]})")
                self.results_text.insert(tk.END, f" ({match['processing_time']:.3f}s)\n")

        # Store results for history
        self.detection_history.append(results)

        # Update performance metrics
        self._update_performance_metrics(results)

    def _update_performance_metrics(self, results):
        """Update performance metrics"""
        template_name = self.template_var.get()

        if template_name not in self.performance_metrics:
            self.performance_metrics[template_name] = {
                'total_tests': 0,
                'successful_detections': 0,
                'method_performance': {},
                'average_processing_time': 0,
                'total_processing_time': 0
            }

        metrics = self.performance_metrics[template_name]
        metrics['total_tests'] += 1

        # Check if detection was successful
        if results['best_match'] and results['best_match'].get('confidence', 0) >= self.template_threshold.get():
            metrics['successful_detections'] += 1

        # Update method performance
        total_time = 0
        for method_name, result in results['methods'].items():
            if method_name not in metrics['method_performance']:
                metrics['method_performance'][method_name] = {
                    'tests': 0,
                    'successes': 0,
                    'total_time': 0,
                    'average_confidence': 0,
                    'total_confidence': 0
                }

            method_metrics = metrics['method_performance'][method_name]
            method_metrics['tests'] += 1

            if result and not result.get('error'):
                confidence = result.get('confidence', 0)
                processing_time = result.get('processing_time', 0)

                method_metrics['total_confidence'] += confidence
                method_metrics['average_confidence'] = method_metrics['total_confidence'] / method_metrics['tests']
                method_metrics['total_time'] += processing_time
                total_time += processing_time

                if confidence >= self.template_threshold.get():
                    method_metrics['successes'] += 1

        # Update overall timing
        metrics['total_processing_time'] += total_time
        metrics['average_processing_time'] = metrics['total_processing_time'] / metrics['total_tests']

        # Update metrics display
        self._display_performance_metrics()

    def _display_performance_metrics(self):
        """Display performance metrics"""
        self.metrics_text.delete(1.0, tk.END)

        if not self.performance_metrics:
            self.metrics_text.insert(tk.END, "No performance data available yet.\nRun some detection tests to see metrics.")
            return

        self.metrics_text.insert(tk.END, "📊 PERFORMANCE METRICS\n")
        self.metrics_text.insert(tk.END, "=" * 50 + "\n\n")

        for template_name, metrics in self.performance_metrics.items():
            success_rate = (metrics['successful_detections'] / metrics['total_tests']) * 100

            self.metrics_text.insert(tk.END, f"🎯 Template: {template_name}\n")
            self.metrics_text.insert(tk.END, f"   Tests: {metrics['total_tests']}\n")
            self.metrics_text.insert(tk.END, f"   Success Rate: {success_rate:.1f}%\n")
            self.metrics_text.insert(tk.END, f"   Avg Processing Time: {metrics['average_processing_time']:.3f}s\n\n")

            # Method breakdown
            self.metrics_text.insert(tk.END, f"   📋 Method Performance:\n")
            for method_name, method_metrics in metrics['method_performance'].items():
                method_success_rate = (method_metrics['successes'] / method_metrics['tests']) * 100
                avg_time = method_metrics['total_time'] / method_metrics['tests']

                self.metrics_text.insert(tk.END, f"      {method_name}: {method_success_rate:.1f}% ")
                self.metrics_text.insert(tk.END, f"(avg conf: {method_metrics['average_confidence']:.3f}, ")
                self.metrics_text.insert(tk.END, f"avg time: {avg_time:.3f}s)\n")

            self.metrics_text.insert(tk.END, "\n")

    def _start_live_detection(self):
        """Start live detection mode"""
        if self.current_template is None:
            messagebox.showwarning("Warning", "Please load a template first")
            return

        if self.live_detection:
            return

        self.live_detection = True
        self._live_detection_thread()

    def _stop_live_detection(self):
        """Stop live detection mode"""
        self.live_detection = False

    def _live_detection_thread(self):
        """Live detection thread"""
        def detection_loop():
            while self.live_detection:
                try:
                    # Capture screenshot
                    screenshot = pyautogui.screenshot()
                    screenshot_np = np.array(screenshot)
                    self.current_screenshot = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

                    # Run detection
                    results = self._perform_detection(self.current_screenshot, self.current_template)

                    # Update GUI in main thread
                    self.window.after(0, lambda: self._display_detection_results(results))
                    self.window.after(0, lambda: self._update_visual_comparison(results))

                    # Wait before next detection
                    time.sleep(1.0)  # 1 second interval

                except Exception as e:
                    print(f"Live detection error: {e}")
                    break

        # Start detection in separate thread
        detection_thread = threading.Thread(target=detection_loop, daemon=True)
        detection_thread.start()

    def _run_comprehensive_test(self):
        """Run comprehensive test with multiple configurations"""
        if self.current_template is None:
            messagebox.showwarning("Warning", "Please load a template first")
            return

        # Save current settings
        original_settings = self._save_current_settings()

        try:
            # Test configurations
            test_configs = [
                {"name": "Standard", "preprocessing": False, "multiscale": False, "features": False},
                {"name": "Multi-Scale", "preprocessing": False, "multiscale": True, "features": False},
                {"name": "Feature-Based", "preprocessing": False, "multiscale": False, "features": True},
                {"name": "All Methods", "preprocessing": False, "multiscale": True, "features": True},
                {"name": "Preprocessed", "preprocessing": True, "multiscale": True, "features": True},
            ]

            comprehensive_results = []

            # Capture screenshot once
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            self.current_screenshot = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            for config in test_configs:
                # Apply test configuration
                self.preprocessing_enabled.set(config["preprocessing"])
                self.multi_scale_enabled.set(config["multiscale"])
                self.feature_detection_enabled.set(config["features"])

                # Run detection
                results = self._perform_detection(self.current_screenshot, self.current_template)
                results['test_config'] = config
                comprehensive_results.append(results)

            # Display comprehensive results
            self._display_comprehensive_results(comprehensive_results)

        finally:
            # Restore original settings
            self._restore_settings(original_settings)

    def _save_current_settings(self):
        """Save current configuration settings"""
        return {
            'preprocessing_enabled': self.preprocessing_enabled.get(),
            'multi_scale_enabled': self.multi_scale_enabled.get(),
            'feature_detection_enabled': self.feature_detection_enabled.get(),
            'brightness_adjustment': self.brightness_adjustment.get(),
            'contrast_adjustment': self.contrast_adjustment.get(),
            'gaussian_blur': self.gaussian_blur.get(),
            'edge_detection': self.edge_detection.get()
        }

    def _restore_settings(self, settings):
        """Restore configuration settings"""
        self.preprocessing_enabled.set(settings['preprocessing_enabled'])
        self.multi_scale_enabled.set(settings['multi_scale_enabled'])
        self.feature_detection_enabled.set(settings['feature_detection_enabled'])
        self.brightness_adjustment.set(settings['brightness_adjustment'])
        self.contrast_adjustment.set(settings['contrast_adjustment'])
        self.gaussian_blur.set(settings['gaussian_blur'])
        self.edge_detection.set(settings['edge_detection'])

    def _display_comprehensive_results(self, comprehensive_results):
        """Display comprehensive test results"""
        self.results_text.delete(1.0, tk.END)

        self.results_text.insert(tk.END, f"🧪 COMPREHENSIVE TEMPLATE DETECTION TEST\n")
        self.results_text.insert(tk.END, "=" * 60 + "\n\n")

        # Summary table
        self.results_text.insert(tk.END, f"📊 SUMMARY:\n")
        self.results_text.insert(tk.END, f"{'Config':<15} {'Best Method':<15} {'Confidence':<12} {'Time':<8}\n")
        self.results_text.insert(tk.END, "-" * 60 + "\n")

        for results in comprehensive_results:
            config_name = results['test_config']['name']
            best_match = results['best_match']

            if best_match:
                method = best_match.get('method', 'Unknown')[:14]
                confidence = f"{best_match.get('confidence', 0):.3f}"
                time_taken = f"{best_match.get('processing_time', 0):.3f}s"
            else:
                method = "None"
                confidence = "0.000"
                time_taken = "N/A"

            self.results_text.insert(tk.END, f"{config_name:<15} {method:<15} {confidence:<12} {time_taken:<8}\n")

        self.results_text.insert(tk.END, "\n")

        # Detailed results for each configuration
        for results in comprehensive_results:
            config_name = results['test_config']['name']
            self.results_text.insert(tk.END, f"🔸 {config_name.upper()} CONFIGURATION:\n")

            if results['best_match']:
                best = results['best_match']
                self.results_text.insert(tk.END, f"   ✅ Success: {best.get('confidence', 0):.3f} confidence\n")
                self.results_text.insert(tk.END, f"   🏆 Best Method: {best.get('method', 'Unknown')}\n")
                if 'location' in best:
                    loc = best['location']
                    self.results_text.insert(tk.END, f"   📍 Location: ({loc[0]}, {loc[1]})\n")
            else:
                self.results_text.insert(tk.END, f"   ❌ No detection above threshold\n")

            # Method breakdown
            for method_name, result in results['methods'].items():
                if result and not result.get('error'):
                    confidence = result.get('confidence', 0)
                    status = "✅" if confidence >= self.template_threshold.get() else "❌"
                    self.results_text.insert(tk.END, f"      {status} {method_name}: {confidence:.3f}\n")

            self.results_text.insert(tk.END, "\n")

    def _update_visual_comparison(self, results):
        """Update visual comparison with detection results"""
        if self.current_screenshot is None:
            return

        try:
            # Create visualization image
            vis_image = self.current_screenshot.copy()

            # Draw all detections with enhanced visualization
            detection_count = 0
            for match in results.get('all_matches', []):
                if 'location' in match:
                    x, y = match['location']
                    confidence = match['confidence']
                    method = match['method']
                    detection_count += 1

                    # Determine template size (use original or scaled)
                    if 'template_size' in match:
                        w, h = match['template_size'][1], match['template_size'][0]
                    else:
                        w, h = self.current_template.shape[1], self.current_template.shape[0]
                        if 'scale' in match:
                            w = int(w * match['scale'])
                            h = int(h * match['scale'])

                    # Enhanced color coding based on confidence and method
                    if confidence >= self.template_threshold.get():
                        if method == 'standard':
                            color = (0, 255, 0)  # Green for standard matches
                        elif method == 'multiscale':
                            color = (0, 255, 255)  # Cyan for multi-scale
                        elif method == 'feature_based':
                            color = (255, 0, 255)  # Magenta for feature-based
                        elif method == 'ocr':
                            color = (255, 255, 0)  # Yellow for OCR
                        else:
                            color = (0, 255, 0)  # Default green
                    else:
                        color = (0, 165, 255)  # Orange for below threshold

                    # Draw thick rectangle for better visibility
                    cv2.rectangle(vis_image, (x, y), (x + w, y + h), color, 3)

                    # Draw corner markers for better visibility
                    corner_size = 10
                    cv2.line(vis_image, (x, y), (x + corner_size, y), color, 4)
                    cv2.line(vis_image, (x, y), (x, y + corner_size), color, 4)
                    cv2.line(vis_image, (x + w, y), (x + w - corner_size, y), color, 4)
                    cv2.line(vis_image, (x + w, y), (x + w, y + corner_size), color, 4)
                    cv2.line(vis_image, (x, y + h), (x + corner_size, y + h), color, 4)
                    cv2.line(vis_image, (x, y + h), (x, y + h - corner_size), color, 4)
                    cv2.line(vis_image, (x + w, y + h), (x + w - corner_size, y + h), color, 4)
                    cv2.line(vis_image, (x + w, y + h), (x + w, y + h - corner_size), color, 4)

                    # Enhanced label with background
                    label = f"#{detection_count} {method}: {confidence:.3f}"
                    if 'scale' in match and match['scale'] != 1.0:
                        label += f" (s:{match['scale']:.2f})"

                    # Calculate label size and position
                    font_scale = 0.6
                    font_thickness = 2
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, font_scale, font_thickness)[0]

                    # Position label above rectangle, or below if too close to top
                    label_y = y - 10 if y > label_size[1] + 20 else y + h + label_size[1] + 10
                    label_x = max(0, min(x, vis_image.shape[1] - label_size[0] - 10))

                    # Draw label background
                    cv2.rectangle(vis_image,
                                (label_x - 5, label_y - label_size[1] - 5),
                                (label_x + label_size[0] + 5, label_y + 5),
                                (0, 0, 0), -1)
                    cv2.rectangle(vis_image,
                                (label_x - 5, label_y - label_size[1] - 5),
                                (label_x + label_size[0] + 5, label_y + 5),
                                color, 2)

                    # Draw label text
                    cv2.putText(vis_image, label, (label_x, label_y),
                              cv2.FONT_HERSHEY_SIMPLEX, font_scale, (255, 255, 255), font_thickness)

            # Add detection summary overlay
            if detection_count > 0:
                summary = f"Detections: {detection_count} | Threshold: {self.template_threshold.get():.2f}"
                summary_size = cv2.getTextSize(summary, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]

                # Draw summary background
                cv2.rectangle(vis_image, (10, 10),
                            (20 + summary_size[0], 40 + summary_size[1]),
                            (0, 0, 0), -1)
                cv2.rectangle(vis_image, (10, 10),
                            (20 + summary_size[0], 40 + summary_size[1]),
                            (255, 255, 255), 2)

                # Draw summary text
                cv2.putText(vis_image, summary, (15, 35),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # Convert and resize for display
            vis_rgb = cv2.cvtColor(vis_image, cv2.COLOR_BGR2RGB)

            # Resize if too large while maintaining aspect ratio
            height, width = vis_rgb.shape[:2]
            max_size = 800  # Increased for better visibility

            if width > max_size or height > max_size:
                scale = min(max_size / width, max_size / height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                vis_rgb = cv2.resize(vis_rgb, (new_width, new_height), interpolation=cv2.INTER_AREA)

            # Convert to PhotoImage
            pil_image = Image.fromarray(vis_rgb)
            photo = ImageTk.PhotoImage(pil_image)

            # Update label
            self.screenshot_label.configure(image=photo, text="")
            self.screenshot_label.image = photo  # Keep reference

        except Exception as e:
            print(f"Error updating visual comparison: {e}")
            self.screenshot_label.configure(text=f"Error displaying screenshot: {str(e)}")

    def _save_results(self):
        """Save detection results to file"""
        if not self.detection_history:
            messagebox.showwarning("Warning", "No results to save")
            return

        file_path = filedialog.asksaveasfilename(
            title="Save Detection Results",
            defaultextension=".json",
            filetypes=[
                ("JSON files", "*.json"),
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                # Prepare data for saving
                save_data = {
                    'template': self.template_var.get(),
                    'timestamp': datetime.now().isoformat(),
                    'configuration': self._save_current_settings(),
                    'detection_history': [],
                    'performance_metrics': self.performance_metrics
                }

                # Convert detection history (handle datetime objects)
                for result in self.detection_history:
                    result_copy = result.copy()
                    result_copy['timestamp'] = result['timestamp'].isoformat()
                    save_data['detection_history'].append(result_copy)

                # Save to file
                with open(file_path, 'w') as f:
                    json.dump(save_data, f, indent=2, default=str)

                messagebox.showinfo("Success", f"Results saved to {file_path}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to save results: {str(e)}")

    def _clear_results(self):
        """Clear all results and metrics"""
        self.results_text.delete(1.0, tk.END)
        self.metrics_text.delete(1.0, tk.END)
        self.detection_history.clear()
        self.performance_metrics.clear()

        # Clear visual displays
        self.screenshot_label.configure(image="", text="No screenshot captured")
        if hasattr(self.screenshot_label, 'image'):
            del self.screenshot_label.image


def main():
    """Test the diagnostics interface"""
    root = tk.Tk()
    root.withdraw()  # Hide main window

    diagnostics = TemplateDetectionDiagnostics(root)
    diagnostics.show_diagnostics_window()

    root.mainloop()


if __name__ == "__main__":
    main()
