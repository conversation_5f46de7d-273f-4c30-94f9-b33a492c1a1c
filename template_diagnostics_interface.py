"""
Advanced Template Detection Diagnostics Interface
Comprehensive template testing tools with live detection, multiple OCR engines,
confidence scoring, preprocessing options, and detailed diagnostic logging
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
import cv2
import numpy as np
import pyautogui
import os
import time
import json
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
import threading
from PIL import Image, ImageTk, ImageEnhance, ImageFilter
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
from sklearn.cluster import KMeans
import colorsys
from collections import Counter

# Template and debug folders
TEMPLATE_FOLDER = "templates"
DEBUG_FOLDER = "debug_images"

class TemplateDetectionDiagnostics:
    """Advanced template detection diagnostics interface"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.window = None
        
        # Detection state
        self.live_detection = False
        self.current_template = None
        self.current_screenshot = None
        self.detection_results = {}
        
        # Configuration variables
        self.template_threshold = tk.DoubleVar(value=0.8)
        self.multi_scale_enabled = tk.BooleanVar(value=True)
        self.feature_detection_enabled = tk.BooleanVar(value=True)
        self.preprocessing_enabled = tk.BooleanVar(value=False)
        self.brightness_adjustment = tk.DoubleVar(value=1.0)
        self.contrast_adjustment = tk.DoubleVar(value=1.0)
        self.gaussian_blur = tk.IntVar(value=0)
        self.edge_detection = tk.BooleanVar(value=False)
        self.rotation_tolerance = tk.IntVar(value=0)
        self.scale_min = tk.DoubleVar(value=0.5)
        self.scale_max = tk.DoubleVar(value=2.0)
        self.scale_step = tk.DoubleVar(value=0.1)
        
        # OCR configuration
        self.ocr_enabled = tk.BooleanVar(value=False)
        self.tesseract_enabled = tk.BooleanVar(value=True)
        self.easyocr_enabled = tk.BooleanVar(value=True)
        self.paddleocr_enabled = tk.BooleanVar(value=True)
        self.ocr_confidence_threshold = tk.DoubleVar(value=0.7)

        # Advanced OCR configuration
        self.tesseract_psm = tk.IntVar(value=6)  # Page Segmentation Mode
        self.tesseract_oem = tk.IntVar(value=3)  # OCR Engine Mode
        self.tesseract_whitelist = tk.StringVar(value="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz")
        self.tesseract_blacklist = tk.StringVar(value="")
        self.ocr_preprocessing_enabled = tk.BooleanVar(value=True)
        self.ocr_contrast_boost = tk.DoubleVar(value=1.5)
        self.ocr_brightness_boost = tk.DoubleVar(value=1.2)
        self.ocr_noise_reduction = tk.BooleanVar(value=True)
        self.ocr_morphology_enabled = tk.BooleanVar(value=False)

        # Color analysis configuration
        self.color_analysis_enabled = tk.BooleanVar(value=True)
        self.dominant_colors_count = tk.IntVar(value=5)
        self.color_tolerance = tk.DoubleVar(value=30.0)
        self.hsv_analysis_enabled = tk.BooleanVar(value=True)

        # Enhanced template matching
        self.heatmap_enabled = tk.BooleanVar(value=True)
        self.multiple_threshold_test = tk.BooleanVar(value=True)
        self.template_scaling_test = tk.BooleanVar(value=True)
        self.rotation_test_enabled = tk.BooleanVar(value=False)
        self.rotation_test_angles = tk.StringVar(value="-5,-2,0,2,5")

        # False positive elimination
        self.negative_templates_enabled = tk.BooleanVar(value=False)
        self.negative_templates_list = []
        self.region_exclusion_enabled = tk.BooleanVar(value=False)
        self.exclusion_regions = []
        self.multi_template_validation = tk.BooleanVar(value=False)
        self.validation_templates = []
        self.time_based_validation = tk.BooleanVar(value=False)
        self.validation_duration = tk.DoubleVar(value=2.0)
        self.stability_threshold = tk.DoubleVar(value=0.95)
        
        # Results storage
        self.detection_history = []
        self.performance_metrics = {}
        
        # Create debug folder if it doesn't exist
        os.makedirs(DEBUG_FOLDER, exist_ok=True)
        
    def show_diagnostics_window(self):
        """Show the template diagnostics window"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("Advanced Template Detection Diagnostics")
        self.window.geometry("1400x900")
        self.window.resizable(True, True)
        
        self._create_interface()
        
        # Center window
        if self.parent:
            self.window.transient(self.parent)
            self.window.grab_set()
    
    def _create_interface(self):
        """Create the diagnostics interface"""
        # Main container with paned window
        main_paned = ttk.PanedWindow(self.window, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Controls and Configuration
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=1)
        
        # Right panel - Results and Visualization
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=2)
        
        self._create_control_panel(left_frame)
        self._create_results_panel(right_frame)
        
    def _create_control_panel(self, parent):
        """Create the control panel with all configuration options"""
        # Template Selection
        template_frame = ttk.LabelFrame(parent, text="Template Selection", padding="5")
        template_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(template_frame, text="Template:").pack(anchor=tk.W)
        self.template_var = tk.StringVar()
        template_combo = ttk.Combobox(template_frame, textvariable=self.template_var, width=40)
        template_combo.pack(fill=tk.X, pady=(2, 5))
        
        # Load available templates
        self._load_template_list(template_combo)
        
        # Template actions
        template_actions = ttk.Frame(template_frame)
        template_actions.pack(fill=tk.X)
        
        ttk.Button(template_actions, text="Load Template", 
                  command=self._load_selected_template).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(template_actions, text="Browse...", 
                  command=self._browse_template).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(template_actions, text="Refresh List", 
                  command=lambda: self._load_template_list(template_combo)).pack(side=tk.LEFT)
        
        # Detection Configuration
        config_frame = ttk.LabelFrame(parent, text="Detection Configuration", padding="5")
        config_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Basic settings
        ttk.Label(config_frame, text="Confidence Threshold:").pack(anchor=tk.W)
        threshold_frame = ttk.Frame(config_frame)
        threshold_frame.pack(fill=tk.X, pady=(2, 5))
        
        ttk.Scale(threshold_frame, from_=0.1, to=1.0, variable=self.template_threshold,
                 orient=tk.HORIZONTAL).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(threshold_frame, textvariable=self.template_threshold,
                 width=6).pack(side=tk.RIGHT)
        
        # Multi-scale detection
        ttk.Checkbutton(config_frame, text="Multi-Scale Detection", 
                       variable=self.multi_scale_enabled).pack(anchor=tk.W)
        
        # Scale range
        scale_frame = ttk.Frame(config_frame)
        scale_frame.pack(fill=tk.X, pady=(2, 5))
        
        ttk.Label(scale_frame, text="Scale Range:").pack(side=tk.LEFT)
        ttk.Spinbox(scale_frame, from_=0.1, to=2.0, increment=0.1, width=6,
                   textvariable=self.scale_min).pack(side=tk.LEFT, padx=(5, 2))
        ttk.Label(scale_frame, text="to").pack(side=tk.LEFT, padx=2)
        ttk.Spinbox(scale_frame, from_=0.1, to=3.0, increment=0.1, width=6,
                   textvariable=self.scale_max).pack(side=tk.LEFT, padx=(2, 5))
        ttk.Label(scale_frame, text="step").pack(side=tk.LEFT, padx=2)
        ttk.Spinbox(scale_frame, from_=0.01, to=0.5, increment=0.01, width=6,
                   textvariable=self.scale_step).pack(side=tk.LEFT, padx=2)
        
        # Feature detection
        ttk.Checkbutton(config_frame, text="Feature-Based Detection", 
                       variable=self.feature_detection_enabled).pack(anchor=tk.W)
        
        # Rotation tolerance
        rotation_frame = ttk.Frame(config_frame)
        rotation_frame.pack(fill=tk.X, pady=(2, 5))
        
        ttk.Label(rotation_frame, text="Rotation Tolerance (degrees):").pack(side=tk.LEFT)
        ttk.Spinbox(rotation_frame, from_=0, to=45, increment=1, width=6,
                   textvariable=self.rotation_tolerance).pack(side=tk.RIGHT)
        
        # Image Preprocessing
        preprocess_frame = ttk.LabelFrame(parent, text="Image Preprocessing", padding="5")
        preprocess_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Checkbutton(preprocess_frame, text="Enable Preprocessing", 
                       variable=self.preprocessing_enabled).pack(anchor=tk.W)
        
        # Brightness and contrast
        brightness_frame = ttk.Frame(preprocess_frame)
        brightness_frame.pack(fill=tk.X, pady=(2, 2))
        
        ttk.Label(brightness_frame, text="Brightness:").pack(side=tk.LEFT)
        ttk.Scale(brightness_frame, from_=0.1, to=3.0, variable=self.brightness_adjustment,
                 orient=tk.HORIZONTAL, length=150).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(brightness_frame, textvariable=self.brightness_adjustment,
                 width=6).pack(side=tk.RIGHT)
        
        contrast_frame = ttk.Frame(preprocess_frame)
        contrast_frame.pack(fill=tk.X, pady=(2, 2))
        
        ttk.Label(contrast_frame, text="Contrast:").pack(side=tk.LEFT)
        ttk.Scale(contrast_frame, from_=0.1, to=3.0, variable=self.contrast_adjustment,
                 orient=tk.HORIZONTAL, length=150).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(contrast_frame, textvariable=self.contrast_adjustment,
                 width=6).pack(side=tk.RIGHT)
        
        # Gaussian blur
        blur_frame = ttk.Frame(preprocess_frame)
        blur_frame.pack(fill=tk.X, pady=(2, 2))
        
        ttk.Label(blur_frame, text="Gaussian Blur:").pack(side=tk.LEFT)
        ttk.Spinbox(blur_frame, from_=0, to=15, increment=2, width=6,
                   textvariable=self.gaussian_blur).pack(side=tk.RIGHT)
        
        # Edge detection
        ttk.Checkbutton(preprocess_frame, text="Edge Detection", 
                       variable=self.edge_detection).pack(anchor=tk.W)
        
        # OCR Configuration
        ocr_frame = ttk.LabelFrame(parent, text="OCR Configuration", padding="5")
        ocr_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Checkbutton(ocr_frame, text="Enable OCR Analysis", 
                       variable=self.ocr_enabled).pack(anchor=tk.W)
        
        # OCR engines
        engines_frame = ttk.Frame(ocr_frame)
        engines_frame.pack(fill=tk.X, pady=(2, 5))
        
        ttk.Checkbutton(engines_frame, text="Tesseract", 
                       variable=self.tesseract_enabled).pack(side=tk.LEFT)
        ttk.Checkbutton(engines_frame, text="EasyOCR", 
                       variable=self.easyocr_enabled).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Checkbutton(engines_frame, text="PaddleOCR", 
                       variable=self.paddleocr_enabled).pack(side=tk.LEFT, padx=(10, 0))
        
        # OCR confidence
        ocr_conf_frame = ttk.Frame(ocr_frame)
        ocr_conf_frame.pack(fill=tk.X, pady=(2, 5))
        
        ttk.Label(ocr_conf_frame, text="OCR Confidence:").pack(side=tk.LEFT)
        ttk.Scale(ocr_conf_frame, from_=0.1, to=1.0, variable=self.ocr_confidence_threshold,
                 orient=tk.HORIZONTAL, length=150).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(ocr_conf_frame, textvariable=self.ocr_confidence_threshold,
                 width=6).pack(side=tk.RIGHT)

        # Advanced OCR Configuration
        adv_ocr_frame = ttk.LabelFrame(parent, text="Advanced OCR Configuration", padding="5")
        adv_ocr_frame.pack(fill=tk.X, pady=(0, 5))

        # Tesseract PSM and OEM
        tesseract_config_frame = ttk.Frame(adv_ocr_frame)
        tesseract_config_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Label(tesseract_config_frame, text="Tesseract PSM:").pack(side=tk.LEFT)
        psm_combo = ttk.Combobox(tesseract_config_frame, textvariable=self.tesseract_psm, width=8, state="readonly")
        psm_combo['values'] = ('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13')
        psm_combo.pack(side=tk.LEFT, padx=(5, 10))

        ttk.Label(tesseract_config_frame, text="OEM:").pack(side=tk.LEFT)
        oem_combo = ttk.Combobox(tesseract_config_frame, textvariable=self.tesseract_oem, width=8, state="readonly")
        oem_combo['values'] = ('0', '1', '2', '3')
        oem_combo.pack(side=tk.LEFT, padx=5)

        # Character whitelist/blacklist
        char_frame = ttk.Frame(adv_ocr_frame)
        char_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Label(char_frame, text="Whitelist:").pack(anchor=tk.W)
        ttk.Entry(char_frame, textvariable=self.tesseract_whitelist, width=50).pack(fill=tk.X, pady=(2, 5))

        ttk.Label(char_frame, text="Blacklist:").pack(anchor=tk.W)
        ttk.Entry(char_frame, textvariable=self.tesseract_blacklist, width=50).pack(fill=tk.X, pady=(2, 5))

        # OCR Preprocessing
        ttk.Checkbutton(adv_ocr_frame, text="OCR Preprocessing",
                       variable=self.ocr_preprocessing_enabled).pack(anchor=tk.W)

        # OCR-specific adjustments
        ocr_brightness_frame = ttk.Frame(adv_ocr_frame)
        ocr_brightness_frame.pack(fill=tk.X, pady=(2, 2))

        ttk.Label(ocr_brightness_frame, text="OCR Brightness:").pack(side=tk.LEFT)
        ttk.Scale(ocr_brightness_frame, from_=0.5, to=3.0, variable=self.ocr_brightness_boost,
                 orient=tk.HORIZONTAL, length=120).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(ocr_brightness_frame, textvariable=self.ocr_brightness_boost,
                 width=6).pack(side=tk.RIGHT)

        ocr_contrast_frame = ttk.Frame(adv_ocr_frame)
        ocr_contrast_frame.pack(fill=tk.X, pady=(2, 2))

        ttk.Label(ocr_contrast_frame, text="OCR Contrast:").pack(side=tk.LEFT)
        ttk.Scale(ocr_contrast_frame, from_=0.5, to=3.0, variable=self.ocr_contrast_boost,
                 orient=tk.HORIZONTAL, length=120).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(ocr_contrast_frame, textvariable=self.ocr_contrast_boost,
                 width=6).pack(side=tk.RIGHT)

        # OCR processing options
        ocr_options_frame = ttk.Frame(adv_ocr_frame)
        ocr_options_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Checkbutton(ocr_options_frame, text="Noise Reduction",
                       variable=self.ocr_noise_reduction).pack(side=tk.LEFT)
        ttk.Checkbutton(ocr_options_frame, text="Morphology",
                       variable=self.ocr_morphology_enabled).pack(side=tk.LEFT, padx=(10, 0))

        # Color Analysis Configuration
        color_frame = ttk.LabelFrame(parent, text="Color Analysis", padding="5")
        color_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Checkbutton(color_frame, text="Enable Color Analysis",
                       variable=self.color_analysis_enabled).pack(anchor=tk.W)

        # Dominant colors count
        dom_colors_frame = ttk.Frame(color_frame)
        dom_colors_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Label(dom_colors_frame, text="Dominant Colors:").pack(side=tk.LEFT)
        ttk.Spinbox(dom_colors_frame, from_=3, to=10, increment=1, width=6,
                   textvariable=self.dominant_colors_count).pack(side=tk.LEFT, padx=5)

        ttk.Label(dom_colors_frame, text="Color Tolerance:").pack(side=tk.LEFT, padx=(10, 5))
        ttk.Scale(dom_colors_frame, from_=5.0, to=100.0, variable=self.color_tolerance,
                 orient=tk.HORIZONTAL, length=100).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(dom_colors_frame, textvariable=self.color_tolerance,
                 width=6).pack(side=tk.RIGHT)

        ttk.Checkbutton(color_frame, text="HSV Analysis",
                       variable=self.hsv_analysis_enabled).pack(anchor=tk.W)

        # Enhanced Template Matching
        enhanced_matching_frame = ttk.LabelFrame(parent, text="Enhanced Template Matching", padding="5")
        enhanced_matching_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Checkbutton(enhanced_matching_frame, text="Show Heatmaps",
                       variable=self.heatmap_enabled).pack(anchor=tk.W)
        ttk.Checkbutton(enhanced_matching_frame, text="Multiple Threshold Test",
                       variable=self.multiple_threshold_test).pack(anchor=tk.W)
        ttk.Checkbutton(enhanced_matching_frame, text="Template Scaling Test",
                       variable=self.template_scaling_test).pack(anchor=tk.W)

        # Rotation testing
        rotation_test_frame = ttk.Frame(enhanced_matching_frame)
        rotation_test_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Checkbutton(rotation_test_frame, text="Rotation Test",
                       variable=self.rotation_test_enabled).pack(side=tk.LEFT)
        ttk.Label(rotation_test_frame, text="Angles:").pack(side=tk.LEFT, padx=(10, 5))
        ttk.Entry(rotation_test_frame, textvariable=self.rotation_test_angles, width=15).pack(side=tk.LEFT)

        # False Positive Elimination
        fp_elimination_frame = ttk.LabelFrame(parent, text="False Positive Elimination", padding="5")
        fp_elimination_frame.pack(fill=tk.X, pady=(0, 5))

        # Negative templates
        neg_template_frame = ttk.Frame(fp_elimination_frame)
        neg_template_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Checkbutton(neg_template_frame, text="Negative Templates",
                       variable=self.negative_templates_enabled).pack(side=tk.LEFT)
        ttk.Button(neg_template_frame, text="Manage",
                  command=self._manage_negative_templates).pack(side=tk.RIGHT)

        # Region exclusion
        region_exclusion_frame = ttk.Frame(fp_elimination_frame)
        region_exclusion_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Checkbutton(region_exclusion_frame, text="Region Exclusion",
                       variable=self.region_exclusion_enabled).pack(side=tk.LEFT)
        ttk.Button(region_exclusion_frame, text="Define Regions",
                  command=self._define_exclusion_regions).pack(side=tk.RIGHT)

        # Multi-template validation
        multi_template_frame = ttk.Frame(fp_elimination_frame)
        multi_template_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Checkbutton(multi_template_frame, text="Multi-Template Validation",
                       variable=self.multi_template_validation).pack(side=tk.LEFT)
        ttk.Button(multi_template_frame, text="Configure",
                  command=self._configure_validation_templates).pack(side=tk.RIGHT)

        # Time-based validation
        time_validation_frame = ttk.Frame(fp_elimination_frame)
        time_validation_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Checkbutton(time_validation_frame, text="Time-Based Validation",
                       variable=self.time_based_validation).pack(anchor=tk.W)

        duration_frame = ttk.Frame(fp_elimination_frame)
        duration_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Label(duration_frame, text="Duration (s):").pack(side=tk.LEFT)
        ttk.Spinbox(duration_frame, from_=0.5, to=10.0, increment=0.5, width=8,
                   textvariable=self.validation_duration).pack(side=tk.LEFT, padx=5)

        ttk.Label(duration_frame, text="Stability:").pack(side=tk.LEFT, padx=(10, 5))
        ttk.Scale(duration_frame, from_=0.8, to=1.0, variable=self.stability_threshold,
                 orient=tk.HORIZONTAL, length=100).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(duration_frame, textvariable=self.stability_threshold,
                 width=6).pack(side=tk.RIGHT)

        # Action Buttons
        action_frame = ttk.LabelFrame(parent, text="Actions", padding="5")
        action_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(action_frame, text="Single Test", 
                  command=self._run_single_test).pack(fill=tk.X, pady=(0, 2))
        ttk.Button(action_frame, text="Start Live Detection", 
                  command=self._start_live_detection).pack(fill=tk.X, pady=(0, 2))
        ttk.Button(action_frame, text="Stop Live Detection", 
                  command=self._stop_live_detection).pack(fill=tk.X, pady=(0, 2))
        ttk.Button(action_frame, text="Comprehensive Test", 
                  command=self._run_comprehensive_test).pack(fill=tk.X, pady=(0, 2))
        ttk.Button(action_frame, text="Save Results", 
                  command=self._save_results).pack(fill=tk.X, pady=(0, 2))
        ttk.Button(action_frame, text="Clear Results", 
                  command=self._clear_results).pack(fill=tk.X)
    
    def _create_results_panel(self, parent):
        """Create the results and visualization panel"""
        # Create notebook for different result views
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Detection Results Tab
        results_frame = ttk.Frame(notebook)
        notebook.add(results_frame, text="Detection Results")
        
        # Results text with scrollbar
        results_container = ttk.Frame(results_frame)
        results_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.results_text = tk.Text(results_container, wrap=tk.WORD, font=('Consolas', 10))
        results_scrollbar = ttk.Scrollbar(results_container, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=results_scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Visual Comparison Tab
        visual_frame = ttk.Frame(notebook)
        notebook.add(visual_frame, text="Visual Comparison")
        
        # Image display area
        image_paned = ttk.PanedWindow(visual_frame, orient=tk.HORIZONTAL)
        image_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Template image
        template_frame = ttk.LabelFrame(image_paned, text="Template", padding="5")
        image_paned.add(template_frame, weight=1)
        
        self.template_label = ttk.Label(template_frame, text="No template loaded")
        self.template_label.pack(expand=True)
        
        # Screenshot with detections
        screenshot_frame = ttk.LabelFrame(image_paned, text="Screenshot with Detections", padding="5")
        image_paned.add(screenshot_frame, weight=2)

        # Add legend for detection colors
        legend_frame = ttk.Frame(screenshot_frame)
        legend_frame.pack(fill=tk.X, pady=(0, 5))

        legend_text = "Legend: 🟢 Standard  🔵 Multi-Scale  🟣 Feature-Based  🟡 OCR  🟠 Below Threshold"
        ttk.Label(legend_frame, text=legend_text, font=('Consolas', 8)).pack()

        self.screenshot_label = ttk.Label(screenshot_frame, text="No screenshot captured")
        self.screenshot_label.pack(expand=True)
        
        # Performance Metrics Tab
        metrics_frame = ttk.Frame(notebook)
        notebook.add(metrics_frame, text="Performance Metrics")
        
        # Metrics display
        self.metrics_text = tk.Text(metrics_frame, wrap=tk.WORD, font=('Consolas', 10))
        metrics_scrollbar = ttk.Scrollbar(metrics_frame, orient=tk.VERTICAL, command=self.metrics_text.yview)
        self.metrics_text.configure(yscrollcommand=metrics_scrollbar.set)
        
        self.metrics_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        metrics_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

        # Color Analysis Tab
        color_analysis_frame = ttk.Frame(notebook)
        notebook.add(color_analysis_frame, text="Color Analysis")

        # Color analysis display with matplotlib
        self.color_analysis_container = ttk.Frame(color_analysis_frame)
        self.color_analysis_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # OCR Analysis Tab
        ocr_analysis_frame = ttk.Frame(notebook)
        notebook.add(ocr_analysis_frame, text="OCR Analysis")

        # OCR results display
        ocr_container = ttk.Frame(ocr_analysis_frame)
        ocr_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.ocr_results_text = tk.Text(ocr_container, wrap=tk.WORD, font=('Consolas', 10))
        ocr_scrollbar = ttk.Scrollbar(ocr_container, orient=tk.VERTICAL, command=self.ocr_results_text.yview)
        self.ocr_results_text.configure(yscrollcommand=ocr_scrollbar.set)

        self.ocr_results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        ocr_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Template Matching Heatmaps Tab
        heatmap_frame = ttk.Frame(notebook)
        notebook.add(heatmap_frame, text="Heatmaps")

        # Heatmap display container
        self.heatmap_container = ttk.Frame(heatmap_frame)
        self.heatmap_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # False Positive Analysis Tab
        fp_analysis_frame = ttk.Frame(notebook)
        notebook.add(fp_analysis_frame, text="False Positive Analysis")

        # FP analysis display
        fp_container = ttk.Frame(fp_analysis_frame)
        fp_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.fp_analysis_text = tk.Text(fp_container, wrap=tk.WORD, font=('Consolas', 10))
        fp_scrollbar = ttk.Scrollbar(fp_container, orient=tk.VERTICAL, command=self.fp_analysis_text.yview)
        self.fp_analysis_text.configure(yscrollcommand=fp_scrollbar.set)

        self.fp_analysis_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        fp_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def _load_template_list(self, combo_widget):
        """Load available templates into the combobox"""
        try:
            templates = []
            if os.path.exists(TEMPLATE_FOLDER):
                for file in os.listdir(TEMPLATE_FOLDER):
                    if file.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif')):
                        templates.append(os.path.splitext(file)[0])

            combo_widget['values'] = sorted(templates)
            if templates:
                combo_widget.set(templates[0])
        except Exception as e:
            print(f"Error loading template list: {e}")

    def _browse_template(self):
        """Browse for a template file"""
        file_path = filedialog.askopenfilename(
            title="Select Template Image",
            initialdir=TEMPLATE_FOLDER,
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.bmp *.gif"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            template_name = os.path.splitext(os.path.basename(file_path))[0]
            self.template_var.set(template_name)
            self._load_selected_template()

    def _load_selected_template(self):
        """Load the selected template"""
        template_name = self.template_var.get()
        if not template_name:
            return

        # Find template file
        template_path = None
        for ext in ['.png', '.jpg', '.jpeg', '.bmp', '.gif']:
            path = os.path.join(TEMPLATE_FOLDER, f"{template_name}{ext}")
            if os.path.exists(path):
                template_path = path
                break

        if not template_path:
            messagebox.showerror("Error", f"Template file not found: {template_name}")
            return

        # Load template
        try:
            self.current_template = cv2.imread(template_path)
            if self.current_template is None:
                messagebox.showerror("Error", f"Failed to load template: {template_path}")
                return

            # Display template in GUI
            self._display_template_image()

            # Update results
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, f"✅ Template loaded: {template_name}\n")
            self.results_text.insert(tk.END, f"   File: {os.path.basename(template_path)}\n")
            self.results_text.insert(tk.END, f"   Size: {self.current_template.shape[1]}x{self.current_template.shape[0]}\n")
            self.results_text.insert(tk.END, f"   Channels: {self.current_template.shape[2] if len(self.current_template.shape) > 2 else 1}\n\n")

        except Exception as e:
            messagebox.showerror("Error", f"Error loading template: {str(e)}")

    def _display_template_image(self):
        """Display the template image in the GUI with enhanced preview"""
        if self.current_template is None:
            return

        try:
            # Convert BGR to RGB for display
            template_rgb = cv2.cvtColor(self.current_template, cv2.COLOR_BGR2RGB)
            original_height, original_width = template_rgb.shape[:2]

            # Create enhanced template display
            display_template = template_rgb.copy()

            # Add border for better visibility
            border_size = 5
            border_color = [100, 100, 100]  # Gray border
            display_template = cv2.copyMakeBorder(
                display_template, border_size, border_size, border_size, border_size,
                cv2.BORDER_CONSTANT, value=border_color
            )

            # Resize for display while maintaining aspect ratio
            height, width = display_template.shape[:2]
            max_size = 300  # Increased size for better visibility

            if width > max_size or height > max_size:
                scale = min(max_size / width, max_size / height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                display_template = cv2.resize(display_template, (new_width, new_height), interpolation=cv2.INTER_AREA)
            else:
                # If template is small, scale it up for better visibility
                if width < 100 and height < 100:
                    scale = min(200 / width, 200 / height)
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    display_template = cv2.resize(display_template, (new_width, new_height), interpolation=cv2.INTER_NEAREST)

            # Add template information overlay
            info_height = 60
            info_image = np.zeros((info_height, display_template.shape[1], 3), dtype=np.uint8)
            info_image.fill(40)  # Dark gray background

            # Add template info text
            template_name = self.template_var.get()
            info_text = f"Template: {template_name}"
            size_text = f"Size: {original_width}x{original_height}px"

            # Draw info text
            cv2.putText(info_image, info_text, (10, 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(info_image, size_text, (10, 40),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)

            # Combine template and info
            final_image = np.vstack([info_image, display_template])

            # Convert to PIL Image and then to PhotoImage
            pil_image = Image.fromarray(final_image)
            photo = ImageTk.PhotoImage(pil_image)

            # Update label
            self.template_label.configure(image=photo, text="")
            self.template_label.image = photo  # Keep a reference

        except Exception as e:
            print(f"Error displaying template image: {e}")
            self.template_label.configure(text=f"Error displaying template: {str(e)}")

    def _run_single_test(self):
        """Run a single detection test"""
        if self.current_template is None:
            messagebox.showwarning("Warning", "Please load a template first")
            return

        try:
            # Capture screenshot
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            self.current_screenshot = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            # Run detection
            results = self._perform_detection(self.current_screenshot, self.current_template)

            # Display results
            self._display_detection_results(results)

            # Update visual comparison
            self._update_visual_comparison(results)

        except Exception as e:
            messagebox.showerror("Error", f"Detection test failed: {str(e)}")

    def _perform_detection(self, screenshot, template):
        """Perform comprehensive template detection"""
        results = {
            'timestamp': datetime.now(),
            'template_size': template.shape,
            'screenshot_size': screenshot.shape,
            'methods': {},
            'preprocessing_applied': [],
            'best_match': None,
            'all_matches': []
        }

        # Apply preprocessing if enabled
        processed_screenshot = screenshot.copy()
        processed_template = template.copy()

        if self.preprocessing_enabled.get():
            processed_screenshot, processed_template = self._apply_preprocessing(
                processed_screenshot, processed_template, results
            )

        # Method 1: Standard Template Matching
        if True:  # Always run standard matching
            standard_results = self._standard_template_matching(
                processed_screenshot, processed_template
            )
            results['methods']['standard'] = standard_results

        # Method 2: Multi-Scale Template Matching
        if self.multi_scale_enabled.get():
            multiscale_results = self._multiscale_template_matching(
                processed_screenshot, processed_template
            )
            results['methods']['multiscale'] = multiscale_results

        # Method 3: Feature-Based Detection
        if self.feature_detection_enabled.get():
            feature_results = self._feature_based_detection(
                processed_screenshot, processed_template
            )
            results['methods']['feature_based'] = feature_results

        # Method 4: OCR Analysis (if enabled and applicable)
        if self.ocr_enabled.get():
            ocr_results = self._ocr_analysis(processed_screenshot, processed_template)
            results['methods']['ocr'] = ocr_results

        # Enhanced Analysis Methods

        # Color Analysis
        if self.color_analysis_enabled.get():
            color_results = self._perform_color_analysis(processed_screenshot, processed_template)
            results['color_analysis'] = color_results

        # Template Matching Heatmaps
        if self.heatmap_enabled.get():
            heatmap_results = self._generate_template_heatmaps(processed_screenshot, processed_template)
            results['heatmaps'] = heatmap_results

        # Multiple Threshold Testing
        if self.multiple_threshold_test.get():
            threshold_results = self._test_multiple_thresholds(processed_screenshot, processed_template)
            results['threshold_analysis'] = threshold_results

        # Template Scaling Test
        if self.template_scaling_test.get():
            scaling_results = self._test_template_scaling(processed_screenshot, processed_template)
            results['scaling_analysis'] = scaling_results

        # Rotation Test
        if self.rotation_test_enabled.get():
            rotation_results = self._test_template_rotation(processed_screenshot, processed_template)
            results['rotation_analysis'] = rotation_results

        # False Positive Analysis
        fp_analysis = self._analyze_false_positives(processed_screenshot, processed_template, results)
        results['false_positive_analysis'] = fp_analysis

        # Determine best match
        results['best_match'] = self._determine_best_match(results['methods'])

        # Collect all matches above threshold
        threshold = self.template_threshold.get()
        for method_name, method_results in results['methods'].items():
            if method_results and method_results.get('confidence', 0) >= threshold:
                match_info = {
                    'method': method_name,
                    'confidence': method_results['confidence'],
                    'location': method_results.get('location'),
                    'scale': method_results.get('scale', 1.0),
                    'processing_time': method_results.get('processing_time', 0)
                }
                results['all_matches'].append(match_info)

        # Sort matches by confidence
        results['all_matches'].sort(key=lambda x: x['confidence'], reverse=True)

        return results

    def _apply_preprocessing(self, screenshot, template, results):
        """Apply image preprocessing"""
        processed_screenshot = screenshot.copy()
        processed_template = template.copy()

        # Brightness adjustment
        if self.brightness_adjustment.get() != 1.0:
            brightness = self.brightness_adjustment.get()
            processed_screenshot = cv2.convertScaleAbs(processed_screenshot, alpha=brightness, beta=0)
            processed_template = cv2.convertScaleAbs(processed_template, alpha=brightness, beta=0)
            results['preprocessing_applied'].append(f"Brightness: {brightness:.2f}")

        # Contrast adjustment
        if self.contrast_adjustment.get() != 1.0:
            contrast = self.contrast_adjustment.get()
            processed_screenshot = cv2.convertScaleAbs(processed_screenshot, alpha=contrast, beta=0)
            processed_template = cv2.convertScaleAbs(processed_template, alpha=contrast, beta=0)
            results['preprocessing_applied'].append(f"Contrast: {contrast:.2f}")

        # Gaussian blur
        if self.gaussian_blur.get() > 0:
            blur_size = self.gaussian_blur.get()
            if blur_size % 2 == 0:
                blur_size += 1  # Must be odd
            processed_screenshot = cv2.GaussianBlur(processed_screenshot, (blur_size, blur_size), 0)
            processed_template = cv2.GaussianBlur(processed_template, (blur_size, blur_size), 0)
            results['preprocessing_applied'].append(f"Gaussian Blur: {blur_size}")

        # Edge detection
        if self.edge_detection.get():
            processed_screenshot = cv2.Canny(processed_screenshot, 50, 150)
            processed_template = cv2.Canny(processed_template, 50, 150)
            results['preprocessing_applied'].append("Edge Detection: Canny")

        return processed_screenshot, processed_template

    def _standard_template_matching(self, screenshot, template):
        """Standard OpenCV template matching"""
        start_time = time.time()

        try:
            # Convert to grayscale if needed
            if len(screenshot.shape) == 3:
                screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            else:
                screenshot_gray = screenshot

            if len(template.shape) == 3:
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            else:
                template_gray = template

            # Perform template matching
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            processing_time = time.time() - start_time

            return {
                'confidence': max_val,
                'location': max_loc,
                'template_size': template.shape,
                'method': 'TM_CCOEFF_NORMED',
                'processing_time': processing_time,
                'scale': 1.0
            }

        except Exception as e:
            return {
                'error': str(e),
                'confidence': 0.0,
                'processing_time': time.time() - start_time
            }

    def _multiscale_template_matching(self, screenshot, template):
        """Multi-scale template matching"""
        start_time = time.time()

        try:
            best_match = None
            best_confidence = 0

            # Convert to grayscale
            if len(screenshot.shape) == 3:
                screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            else:
                screenshot_gray = screenshot

            if len(template.shape) == 3:
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            else:
                template_gray = template

            # Test different scales
            scale_min = self.scale_min.get()
            scale_max = self.scale_max.get()
            scale_step = self.scale_step.get()

            scale = scale_min
            while scale <= scale_max:
                # Resize template
                new_width = int(template_gray.shape[1] * scale)
                new_height = int(template_gray.shape[0] * scale)

                if new_width > 0 and new_height > 0:
                    scaled_template = cv2.resize(template_gray, (new_width, new_height))

                    # Skip if template is larger than screenshot
                    if (scaled_template.shape[0] <= screenshot_gray.shape[0] and
                        scaled_template.shape[1] <= screenshot_gray.shape[1]):

                        result = cv2.matchTemplate(screenshot_gray, scaled_template, cv2.TM_CCOEFF_NORMED)
                        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                        if max_val > best_confidence:
                            best_confidence = max_val
                            best_match = {
                                'confidence': max_val,
                                'location': max_loc,
                                'scale': scale,
                                'template_size': scaled_template.shape
                            }

                scale += scale_step

            processing_time = time.time() - start_time

            if best_match:
                best_match.update({
                    'method': 'Multi-Scale TM_CCOEFF_NORMED',
                    'processing_time': processing_time,
                    'scales_tested': int((scale_max - scale_min) / scale_step) + 1
                })
                return best_match
            else:
                return {
                    'confidence': 0.0,
                    'processing_time': processing_time,
                    'scales_tested': int((scale_max - scale_min) / scale_step) + 1,
                    'method': 'Multi-Scale TM_CCOEFF_NORMED'
                }

        except Exception as e:
            return {
                'error': str(e),
                'confidence': 0.0,
                'processing_time': time.time() - start_time
            }

    def _feature_based_detection(self, screenshot, template):
        """Feature-based detection using SIFT/ORB"""
        start_time = time.time()

        try:
            # Convert to grayscale
            if len(screenshot.shape) == 3:
                screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            else:
                screenshot_gray = screenshot

            if len(template.shape) == 3:
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            else:
                template_gray = template

            # Try SIFT first, fall back to ORB if not available
            try:
                detector = cv2.SIFT_create()
                detector_name = "SIFT"
            except:
                detector = cv2.ORB_create()
                detector_name = "ORB"

            # Find keypoints and descriptors
            kp1, des1 = detector.detectAndCompute(template_gray, None)
            kp2, des2 = detector.detectAndCompute(screenshot_gray, None)

            if des1 is None or des2 is None or len(des1) < 4 or len(des2) < 4:
                return {
                    'confidence': 0.0,
                    'processing_time': time.time() - start_time,
                    'method': f'Feature-Based ({detector_name})',
                    'error': 'Insufficient features detected'
                }

            # Match features
            if detector_name == "SIFT":
                matcher = cv2.FlannBasedMatcher()
                matches = matcher.knnMatch(des1, des2, k=2)

                # Apply Lowe's ratio test
                good_matches = []
                for match_pair in matches:
                    if len(match_pair) == 2:
                        m, n = match_pair
                        if m.distance < 0.7 * n.distance:
                            good_matches.append(m)
            else:
                matcher = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
                matches = matcher.match(des1, des2)
                good_matches = sorted(matches, key=lambda x: x.distance)[:50]

            if len(good_matches) < 4:
                return {
                    'confidence': 0.0,
                    'processing_time': time.time() - start_time,
                    'method': f'Feature-Based ({detector_name})',
                    'matches_found': len(good_matches),
                    'error': 'Insufficient good matches'
                }

            # Calculate confidence based on number of good matches
            confidence = min(len(good_matches) / 20.0, 1.0)  # Normalize to 0-1

            # Find homography if enough matches
            if len(good_matches) >= 4:
                src_pts = np.float32([kp1[m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
                dst_pts = np.float32([kp2[m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)

                try:
                    M, mask = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 5.0)
                    if M is not None:
                        # Get template corners
                        h, w = template_gray.shape
                        corners = np.float32([[0, 0], [w, 0], [w, h], [0, h]]).reshape(-1, 1, 2)
                        transformed_corners = cv2.perspectiveTransform(corners, M)

                        # Calculate center point
                        center_x = int(np.mean(transformed_corners[:, 0, 0]))
                        center_y = int(np.mean(transformed_corners[:, 0, 1]))

                        processing_time = time.time() - start_time

                        return {
                            'confidence': confidence,
                            'location': (center_x - w//2, center_y - h//2),  # Top-left corner
                            'center': (center_x, center_y),
                            'corners': transformed_corners.tolist(),
                            'matches_found': len(good_matches),
                            'inliers': int(mask.sum()) if mask is not None else 0,
                            'method': f'Feature-Based ({detector_name})',
                            'processing_time': processing_time
                        }
                except:
                    pass

            processing_time = time.time() - start_time

            return {
                'confidence': confidence,
                'matches_found': len(good_matches),
                'method': f'Feature-Based ({detector_name})',
                'processing_time': processing_time
            }

        except Exception as e:
            return {
                'error': str(e),
                'confidence': 0.0,
                'processing_time': time.time() - start_time
            }

    def _ocr_analysis(self, screenshot, template):
        """OCR analysis for text-based templates"""
        start_time = time.time()
        ocr_results = {
            'method': 'OCR Analysis',
            'processing_time': 0,
            'engines': {}
        }

        try:
            # Convert to grayscale for OCR
            if len(screenshot.shape) == 3:
                screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            else:
                screenshot_gray = screenshot

            # Test different OCR engines
            if self.tesseract_enabled.get():
                ocr_results['engines']['tesseract'] = self._test_tesseract_ocr(screenshot_gray)

            if self.easyocr_enabled.get():
                ocr_results['engines']['easyocr'] = self._test_easyocr_ocr(screenshot_gray)

            if self.paddleocr_enabled.get():
                ocr_results['engines']['paddleocr'] = self._test_paddleocr_ocr(screenshot_gray)

            # Find best OCR result
            best_confidence = 0
            best_result = None

            for engine_name, engine_result in ocr_results['engines'].items():
                if engine_result and engine_result.get('confidence', 0) > best_confidence:
                    best_confidence = engine_result['confidence']
                    best_result = engine_result
                    best_result['best_engine'] = engine_name

            ocr_results['processing_time'] = time.time() - start_time

            if best_result:
                ocr_results.update(best_result)
                ocr_results['confidence'] = best_confidence
            else:
                ocr_results['confidence'] = 0.0

            return ocr_results

        except Exception as e:
            ocr_results['error'] = str(e)
            ocr_results['confidence'] = 0.0
            ocr_results['processing_time'] = time.time() - start_time
            return ocr_results

    def _test_tesseract_ocr(self, image):
        """Test Tesseract OCR with advanced configuration"""
        try:
            import pytesseract

            # Apply OCR preprocessing if enabled
            processed_image = image.copy()
            if self.ocr_preprocessing_enabled.get():
                processed_image = self._apply_ocr_preprocessing(processed_image)

            # Use configured PSM and OEM
            psm = self.tesseract_psm.get()
            oem = self.tesseract_oem.get()

            # Build configuration string
            config_parts = [f'--psm {psm}', f'--oem {oem}']

            # Add character whitelist/blacklist
            whitelist = self.tesseract_whitelist.get().strip()
            blacklist = self.tesseract_blacklist.get().strip()

            if whitelist:
                config_parts.append(f'-c tessedit_char_whitelist={whitelist}')
            if blacklist:
                config_parts.append(f'-c tessedit_char_blacklist={blacklist}')

            config = ' '.join(config_parts)

            # Test multiple configurations for comparison
            test_configs = [
                {'name': 'Current Config', 'config': config, 'image': processed_image},
                {'name': 'Default Config', 'config': '--psm 6 --oem 3', 'image': image},
                {'name': 'Preprocessed Only', 'config': '--psm 6 --oem 3', 'image': processed_image}
            ]

            results = {}
            best_result = None
            best_confidence = 0

            for test_config in test_configs:
                try:
                    # Get text with confidence
                    data = pytesseract.image_to_data(test_config['image'],
                                                   config=test_config['config'],
                                                   output_type=pytesseract.Output.DICT)

                    # Extract text and confidence
                    text_parts = []
                    confidences = []

                    for i, conf in enumerate(data['conf']):
                        if int(conf) > 0:
                            text = data['text'][i].strip()
                            if text:
                                text_parts.append(text)
                                confidences.append(int(conf))

                    if text_parts and confidences:
                        full_text = ' '.join(text_parts)
                        avg_confidence = sum(confidences) / len(confidences) / 100.0  # Normalize to 0-1

                        result_data = {
                            'text': full_text,
                            'confidence': avg_confidence,
                            'config': test_config['config'],
                            'word_count': len(text_parts),
                            'individual_confidences': confidences
                        }

                        results[test_config['name']] = result_data

                        if avg_confidence > best_confidence:
                            best_confidence = avg_confidence
                            best_result = result_data
                    else:
                        results[test_config['name']] = {
                            'text': '',
                            'confidence': 0.0,
                            'config': test_config['config'],
                            'word_count': 0
                        }

                except Exception as e:
                    results[test_config['name']] = {
                        'error': str(e),
                        'confidence': 0.0,
                        'config': test_config['config']
                    }

            # Return comprehensive results
            return {
                'best_result': best_result,
                'all_results': results,
                'preprocessing_applied': self.ocr_preprocessing_enabled.get()
            }

        except ImportError:
            return {'error': 'Tesseract not available', 'confidence': 0.0}
        except Exception as e:
            return {'error': str(e), 'confidence': 0.0}

    def _test_easyocr_ocr(self, image):
        """Test EasyOCR"""
        try:
            import easyocr

            # Initialize reader (cache it for performance)
            if not hasattr(self, '_easyocr_reader'):
                self._easyocr_reader = easyocr.Reader(['en'])

            results = self._easyocr_reader.readtext(image)

            if results:
                # Combine all detected text
                text_parts = []
                confidences = []

                for (bbox, text, confidence) in results:
                    if confidence >= self.ocr_confidence_threshold.get():
                        text_parts.append(text)
                        confidences.append(confidence)

                if text_parts:
                    full_text = ' '.join(text_parts)
                    avg_confidence = sum(confidences) / len(confidences)

                    return {
                        'text': full_text,
                        'confidence': avg_confidence,
                        'detections': len(results),
                        'valid_detections': len(text_parts)
                    }

            return {'confidence': 0.0, 'text': '', 'detections': 0}

        except ImportError:
            return {'error': 'EasyOCR not available', 'confidence': 0.0}
        except Exception as e:
            return {'error': str(e), 'confidence': 0.0}

    def _test_paddleocr_ocr(self, image):
        """Test PaddleOCR"""
        try:
            from paddleocr import PaddleOCR

            # Initialize OCR (cache it for performance)
            if not hasattr(self, '_paddleocr_reader'):
                self._paddleocr_reader = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)

            results = self._paddleocr_reader.ocr(image, cls=True)

            if results and results[0]:
                text_parts = []
                confidences = []

                for line in results[0]:
                    if len(line) >= 2:
                        text = line[1][0]
                        confidence = line[1][1]

                        if confidence >= self.ocr_confidence_threshold.get():
                            text_parts.append(text)
                            confidences.append(confidence)

                if text_parts:
                    full_text = ' '.join(text_parts)
                    avg_confidence = sum(confidences) / len(confidences)

                    return {
                        'text': full_text,
                        'confidence': avg_confidence,
                        'detections': len(results[0]),
                        'valid_detections': len(text_parts)
                    }

            return {'confidence': 0.0, 'text': '', 'detections': 0}

        except ImportError:
            return {'error': 'PaddleOCR not available', 'confidence': 0.0}
        except Exception as e:
            return {'error': str(e), 'confidence': 0.0}

    def _determine_best_match(self, methods_results):
        """Determine the best match from all detection methods"""
        best_match = None
        best_confidence = 0

        for method_name, result in methods_results.items():
            if result and result.get('confidence', 0) > best_confidence:
                best_confidence = result['confidence']
                best_match = result.copy()
                best_match['method'] = method_name

        return best_match

    def _display_detection_results(self, results):
        """Display detection results in the results text area"""
        self.results_text.delete(1.0, tk.END)

        # Header
        timestamp = results['timestamp'].strftime("%H:%M:%S")
        self.results_text.insert(tk.END, f"🔍 TEMPLATE DETECTION RESULTS - {timestamp}\n")
        self.results_text.insert(tk.END, "=" * 60 + "\n\n")

        # Template info
        template_size = results['template_size']
        screenshot_size = results['screenshot_size']
        self.results_text.insert(tk.END, f"📋 Template: {template_size[1]}x{template_size[0]} pixels\n")
        self.results_text.insert(tk.END, f"📺 Screenshot: {screenshot_size[1]}x{screenshot_size[0]} pixels\n")

        # Preprocessing info
        if results['preprocessing_applied']:
            self.results_text.insert(tk.END, f"🔧 Preprocessing: {', '.join(results['preprocessing_applied'])}\n")

        self.results_text.insert(tk.END, f"🎯 Threshold: {self.template_threshold.get():.2f}\n\n")

        # Best match summary
        if results['best_match']:
            best = results['best_match']
            self.results_text.insert(tk.END, f"🏆 BEST MATCH:\n")
            self.results_text.insert(tk.END, f"   Method: {best.get('method', 'Unknown')}\n")
            self.results_text.insert(tk.END, f"   Confidence: {best.get('confidence', 0):.3f}\n")
            if 'location' in best:
                loc = best['location']
                self.results_text.insert(tk.END, f"   Location: ({loc[0]}, {loc[1]})\n")
            if 'scale' in best:
                self.results_text.insert(tk.END, f"   Scale: {best.get('scale', 1.0):.2f}\n")
            self.results_text.insert(tk.END, f"   Time: {best.get('processing_time', 0):.3f}s\n\n")
        else:
            self.results_text.insert(tk.END, f"❌ NO MATCHES FOUND above threshold {self.template_threshold.get():.2f}\n\n")

        # Detailed method results
        self.results_text.insert(tk.END, f"📊 DETAILED RESULTS:\n")
        self.results_text.insert(tk.END, "-" * 40 + "\n")

        for method_name, result in results['methods'].items():
            if result:
                self.results_text.insert(tk.END, f"\n🔸 {method_name.upper()}:\n")

                if 'error' in result:
                    self.results_text.insert(tk.END, f"   ❌ Error: {result['error']}\n")
                else:
                    confidence = result.get('confidence', 0)
                    status = "✅ PASS" if confidence >= self.template_threshold.get() else "❌ FAIL"
                    self.results_text.insert(tk.END, f"   {status} Confidence: {confidence:.3f}\n")

                    if 'location' in result:
                        loc = result['location']
                        self.results_text.insert(tk.END, f"   📍 Location: ({loc[0]}, {loc[1]})\n")

                    if 'scale' in result:
                        self.results_text.insert(tk.END, f"   📏 Scale: {result['scale']:.2f}\n")

                    if 'processing_time' in result:
                        self.results_text.insert(tk.END, f"   ⏱️  Time: {result['processing_time']:.3f}s\n")

                    # Method-specific details
                    if method_name == 'multiscale' and 'scales_tested' in result:
                        self.results_text.insert(tk.END, f"   🔍 Scales tested: {result['scales_tested']}\n")

                    if method_name == 'feature_based':
                        if 'matches_found' in result:
                            self.results_text.insert(tk.END, f"   🎯 Matches: {result['matches_found']}\n")
                        if 'inliers' in result:
                            self.results_text.insert(tk.END, f"   ✅ Inliers: {result['inliers']}\n")

                    if method_name == 'ocr':
                        if 'text' in result:
                            self.results_text.insert(tk.END, f"   📝 Text: '{result['text']}'\n")
                        if 'best_engine' in result:
                            self.results_text.insert(tk.END, f"   🔧 Engine: {result['best_engine']}\n")

        # All matches summary
        if results['all_matches']:
            self.results_text.insert(tk.END, f"\n🎯 ALL VALID MATCHES (above threshold):\n")
            self.results_text.insert(tk.END, "-" * 40 + "\n")

            for i, match in enumerate(results['all_matches'], 1):
                self.results_text.insert(tk.END, f"{i}. {match['method']}: {match['confidence']:.3f}")
                if 'location' in match:
                    loc = match['location']
                    self.results_text.insert(tk.END, f" at ({loc[0]}, {loc[1]})")
                self.results_text.insert(tk.END, f" ({match['processing_time']:.3f}s)\n")

        # Enhanced Analysis Results
        self._display_enhanced_analysis_results(results)

        # Store results for history
        self.detection_history.append(results)

        # Update performance metrics
        self._update_performance_metrics(results)

    def _update_performance_metrics(self, results):
        """Update performance metrics"""
        template_name = self.template_var.get()

        if template_name not in self.performance_metrics:
            self.performance_metrics[template_name] = {
                'total_tests': 0,
                'successful_detections': 0,
                'method_performance': {},
                'average_processing_time': 0,
                'total_processing_time': 0
            }

        metrics = self.performance_metrics[template_name]
        metrics['total_tests'] += 1

        # Check if detection was successful
        if results['best_match'] and results['best_match'].get('confidence', 0) >= self.template_threshold.get():
            metrics['successful_detections'] += 1

        # Update method performance
        total_time = 0
        for method_name, result in results['methods'].items():
            if method_name not in metrics['method_performance']:
                metrics['method_performance'][method_name] = {
                    'tests': 0,
                    'successes': 0,
                    'total_time': 0,
                    'average_confidence': 0,
                    'total_confidence': 0
                }

            method_metrics = metrics['method_performance'][method_name]
            method_metrics['tests'] += 1

            if result and not result.get('error'):
                confidence = result.get('confidence', 0)
                processing_time = result.get('processing_time', 0)

                method_metrics['total_confidence'] += confidence
                method_metrics['average_confidence'] = method_metrics['total_confidence'] / method_metrics['tests']
                method_metrics['total_time'] += processing_time
                total_time += processing_time

                if confidence >= self.template_threshold.get():
                    method_metrics['successes'] += 1

        # Update overall timing
        metrics['total_processing_time'] += total_time
        metrics['average_processing_time'] = metrics['total_processing_time'] / metrics['total_tests']

        # Update metrics display
        self._display_performance_metrics()

    def _display_performance_metrics(self):
        """Display performance metrics"""
        self.metrics_text.delete(1.0, tk.END)

        if not self.performance_metrics:
            self.metrics_text.insert(tk.END, "No performance data available yet.\nRun some detection tests to see metrics.")
            return

        self.metrics_text.insert(tk.END, "📊 PERFORMANCE METRICS\n")
        self.metrics_text.insert(tk.END, "=" * 50 + "\n\n")

        for template_name, metrics in self.performance_metrics.items():
            success_rate = (metrics['successful_detections'] / metrics['total_tests']) * 100

            self.metrics_text.insert(tk.END, f"🎯 Template: {template_name}\n")
            self.metrics_text.insert(tk.END, f"   Tests: {metrics['total_tests']}\n")
            self.metrics_text.insert(tk.END, f"   Success Rate: {success_rate:.1f}%\n")
            self.metrics_text.insert(tk.END, f"   Avg Processing Time: {metrics['average_processing_time']:.3f}s\n\n")

            # Method breakdown
            self.metrics_text.insert(tk.END, f"   📋 Method Performance:\n")
            for method_name, method_metrics in metrics['method_performance'].items():
                method_success_rate = (method_metrics['successes'] / method_metrics['tests']) * 100
                avg_time = method_metrics['total_time'] / method_metrics['tests']

                self.metrics_text.insert(tk.END, f"      {method_name}: {method_success_rate:.1f}% ")
                self.metrics_text.insert(tk.END, f"(avg conf: {method_metrics['average_confidence']:.3f}, ")
                self.metrics_text.insert(tk.END, f"avg time: {avg_time:.3f}s)\n")

            self.metrics_text.insert(tk.END, "\n")

    def _start_live_detection(self):
        """Start live detection mode"""
        if self.current_template is None:
            messagebox.showwarning("Warning", "Please load a template first")
            return

        if self.live_detection:
            return

        self.live_detection = True
        self._live_detection_thread()

    def _stop_live_detection(self):
        """Stop live detection mode"""
        self.live_detection = False

    def _live_detection_thread(self):
        """Live detection thread"""
        def detection_loop():
            while self.live_detection:
                try:
                    # Capture screenshot
                    screenshot = pyautogui.screenshot()
                    screenshot_np = np.array(screenshot)
                    self.current_screenshot = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

                    # Run detection
                    results = self._perform_detection(self.current_screenshot, self.current_template)

                    # Update GUI in main thread
                    self.window.after(0, lambda: self._display_detection_results(results))
                    self.window.after(0, lambda: self._update_visual_comparison(results))

                    # Wait before next detection
                    time.sleep(1.0)  # 1 second interval

                except Exception as e:
                    print(f"Live detection error: {e}")
                    break

        # Start detection in separate thread
        detection_thread = threading.Thread(target=detection_loop, daemon=True)
        detection_thread.start()

    def _run_comprehensive_test(self):
        """Run comprehensive test with multiple configurations"""
        if self.current_template is None:
            messagebox.showwarning("Warning", "Please load a template first")
            return

        # Save current settings
        original_settings = self._save_current_settings()

        try:
            # Test configurations
            test_configs = [
                {"name": "Standard", "preprocessing": False, "multiscale": False, "features": False},
                {"name": "Multi-Scale", "preprocessing": False, "multiscale": True, "features": False},
                {"name": "Feature-Based", "preprocessing": False, "multiscale": False, "features": True},
                {"name": "All Methods", "preprocessing": False, "multiscale": True, "features": True},
                {"name": "Preprocessed", "preprocessing": True, "multiscale": True, "features": True},
            ]

            comprehensive_results = []

            # Capture screenshot once
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            self.current_screenshot = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            for config in test_configs:
                # Apply test configuration
                self.preprocessing_enabled.set(config["preprocessing"])
                self.multi_scale_enabled.set(config["multiscale"])
                self.feature_detection_enabled.set(config["features"])

                # Run detection
                results = self._perform_detection(self.current_screenshot, self.current_template)
                results['test_config'] = config
                comprehensive_results.append(results)

            # Display comprehensive results
            self._display_comprehensive_results(comprehensive_results)

        finally:
            # Restore original settings
            self._restore_settings(original_settings)

    def _save_current_settings(self):
        """Save current configuration settings"""
        return {
            'preprocessing_enabled': self.preprocessing_enabled.get(),
            'multi_scale_enabled': self.multi_scale_enabled.get(),
            'feature_detection_enabled': self.feature_detection_enabled.get(),
            'brightness_adjustment': self.brightness_adjustment.get(),
            'contrast_adjustment': self.contrast_adjustment.get(),
            'gaussian_blur': self.gaussian_blur.get(),
            'edge_detection': self.edge_detection.get()
        }

    def _restore_settings(self, settings):
        """Restore configuration settings"""
        self.preprocessing_enabled.set(settings['preprocessing_enabled'])
        self.multi_scale_enabled.set(settings['multi_scale_enabled'])
        self.feature_detection_enabled.set(settings['feature_detection_enabled'])
        self.brightness_adjustment.set(settings['brightness_adjustment'])
        self.contrast_adjustment.set(settings['contrast_adjustment'])
        self.gaussian_blur.set(settings['gaussian_blur'])
        self.edge_detection.set(settings['edge_detection'])

    def _display_comprehensive_results(self, comprehensive_results):
        """Display comprehensive test results"""
        self.results_text.delete(1.0, tk.END)

        self.results_text.insert(tk.END, f"🧪 COMPREHENSIVE TEMPLATE DETECTION TEST\n")
        self.results_text.insert(tk.END, "=" * 60 + "\n\n")

        # Summary table
        self.results_text.insert(tk.END, f"📊 SUMMARY:\n")
        self.results_text.insert(tk.END, f"{'Config':<15} {'Best Method':<15} {'Confidence':<12} {'Time':<8}\n")
        self.results_text.insert(tk.END, "-" * 60 + "\n")

        for results in comprehensive_results:
            config_name = results['test_config']['name']
            best_match = results['best_match']

            if best_match:
                method = best_match.get('method', 'Unknown')[:14]
                confidence = f"{best_match.get('confidence', 0):.3f}"
                time_taken = f"{best_match.get('processing_time', 0):.3f}s"
            else:
                method = "None"
                confidence = "0.000"
                time_taken = "N/A"

            self.results_text.insert(tk.END, f"{config_name:<15} {method:<15} {confidence:<12} {time_taken:<8}\n")

        self.results_text.insert(tk.END, "\n")

        # Detailed results for each configuration
        for results in comprehensive_results:
            config_name = results['test_config']['name']
            self.results_text.insert(tk.END, f"🔸 {config_name.upper()} CONFIGURATION:\n")

            if results['best_match']:
                best = results['best_match']
                self.results_text.insert(tk.END, f"   ✅ Success: {best.get('confidence', 0):.3f} confidence\n")
                self.results_text.insert(tk.END, f"   🏆 Best Method: {best.get('method', 'Unknown')}\n")
                if 'location' in best:
                    loc = best['location']
                    self.results_text.insert(tk.END, f"   📍 Location: ({loc[0]}, {loc[1]})\n")
            else:
                self.results_text.insert(tk.END, f"   ❌ No detection above threshold\n")

            # Method breakdown
            for method_name, result in results['methods'].items():
                if result and not result.get('error'):
                    confidence = result.get('confidence', 0)
                    status = "✅" if confidence >= self.template_threshold.get() else "❌"
                    self.results_text.insert(tk.END, f"      {status} {method_name}: {confidence:.3f}\n")

            self.results_text.insert(tk.END, "\n")

    def _update_visual_comparison(self, results):
        """Update visual comparison with detection results"""
        if self.current_screenshot is None:
            return

        try:
            # Create visualization image
            vis_image = self.current_screenshot.copy()

            # Draw all detections with enhanced visualization
            detection_count = 0
            for match in results.get('all_matches', []):
                if 'location' in match:
                    x, y = match['location']
                    confidence = match['confidence']
                    method = match['method']
                    detection_count += 1

                    # Determine template size (use original or scaled)
                    if 'template_size' in match:
                        w, h = match['template_size'][1], match['template_size'][0]
                    else:
                        w, h = self.current_template.shape[1], self.current_template.shape[0]
                        if 'scale' in match:
                            w = int(w * match['scale'])
                            h = int(h * match['scale'])

                    # Enhanced color coding based on confidence and method
                    if confidence >= self.template_threshold.get():
                        if method == 'standard':
                            color = (0, 255, 0)  # Green for standard matches
                        elif method == 'multiscale':
                            color = (0, 255, 255)  # Cyan for multi-scale
                        elif method == 'feature_based':
                            color = (255, 0, 255)  # Magenta for feature-based
                        elif method == 'ocr':
                            color = (255, 255, 0)  # Yellow for OCR
                        else:
                            color = (0, 255, 0)  # Default green
                    else:
                        color = (0, 165, 255)  # Orange for below threshold

                    # Draw thick rectangle for better visibility
                    cv2.rectangle(vis_image, (x, y), (x + w, y + h), color, 3)

                    # Draw corner markers for better visibility
                    corner_size = 10
                    cv2.line(vis_image, (x, y), (x + corner_size, y), color, 4)
                    cv2.line(vis_image, (x, y), (x, y + corner_size), color, 4)
                    cv2.line(vis_image, (x + w, y), (x + w - corner_size, y), color, 4)
                    cv2.line(vis_image, (x + w, y), (x + w, y + corner_size), color, 4)
                    cv2.line(vis_image, (x, y + h), (x + corner_size, y + h), color, 4)
                    cv2.line(vis_image, (x, y + h), (x, y + h - corner_size), color, 4)
                    cv2.line(vis_image, (x + w, y + h), (x + w - corner_size, y + h), color, 4)
                    cv2.line(vis_image, (x + w, y + h), (x + w, y + h - corner_size), color, 4)

                    # Enhanced label with background
                    label = f"#{detection_count} {method}: {confidence:.3f}"
                    if 'scale' in match and match['scale'] != 1.0:
                        label += f" (s:{match['scale']:.2f})"

                    # Calculate label size and position
                    font_scale = 0.6
                    font_thickness = 2
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, font_scale, font_thickness)[0]

                    # Position label above rectangle, or below if too close to top
                    label_y = y - 10 if y > label_size[1] + 20 else y + h + label_size[1] + 10
                    label_x = max(0, min(x, vis_image.shape[1] - label_size[0] - 10))

                    # Draw label background
                    cv2.rectangle(vis_image,
                                (label_x - 5, label_y - label_size[1] - 5),
                                (label_x + label_size[0] + 5, label_y + 5),
                                (0, 0, 0), -1)
                    cv2.rectangle(vis_image,
                                (label_x - 5, label_y - label_size[1] - 5),
                                (label_x + label_size[0] + 5, label_y + 5),
                                color, 2)

                    # Draw label text
                    cv2.putText(vis_image, label, (label_x, label_y),
                              cv2.FONT_HERSHEY_SIMPLEX, font_scale, (255, 255, 255), font_thickness)

            # Add detection summary overlay
            if detection_count > 0:
                summary = f"Detections: {detection_count} | Threshold: {self.template_threshold.get():.2f}"
                summary_size = cv2.getTextSize(summary, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]

                # Draw summary background
                cv2.rectangle(vis_image, (10, 10),
                            (20 + summary_size[0], 40 + summary_size[1]),
                            (0, 0, 0), -1)
                cv2.rectangle(vis_image, (10, 10),
                            (20 + summary_size[0], 40 + summary_size[1]),
                            (255, 255, 255), 2)

                # Draw summary text
                cv2.putText(vis_image, summary, (15, 35),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # Convert and resize for display
            vis_rgb = cv2.cvtColor(vis_image, cv2.COLOR_BGR2RGB)

            # Resize if too large while maintaining aspect ratio
            height, width = vis_rgb.shape[:2]
            max_size = 800  # Increased for better visibility

            if width > max_size or height > max_size:
                scale = min(max_size / width, max_size / height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                vis_rgb = cv2.resize(vis_rgb, (new_width, new_height), interpolation=cv2.INTER_AREA)

            # Convert to PhotoImage
            pil_image = Image.fromarray(vis_rgb)
            photo = ImageTk.PhotoImage(pil_image)

            # Update label
            self.screenshot_label.configure(image=photo, text="")
            self.screenshot_label.image = photo  # Keep reference

        except Exception as e:
            print(f"Error updating visual comparison: {e}")
            self.screenshot_label.configure(text=f"Error displaying screenshot: {str(e)}")

    def _save_results(self):
        """Save detection results to file"""
        if not self.detection_history:
            messagebox.showwarning("Warning", "No results to save")
            return

        file_path = filedialog.asksaveasfilename(
            title="Save Detection Results",
            defaultextension=".json",
            filetypes=[
                ("JSON files", "*.json"),
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                # Prepare data for saving
                save_data = {
                    'template': self.template_var.get(),
                    'timestamp': datetime.now().isoformat(),
                    'configuration': self._save_current_settings(),
                    'detection_history': [],
                    'performance_metrics': self.performance_metrics
                }

                # Convert detection history (handle datetime objects)
                for result in self.detection_history:
                    result_copy = result.copy()
                    result_copy['timestamp'] = result['timestamp'].isoformat()
                    save_data['detection_history'].append(result_copy)

                # Save to file
                with open(file_path, 'w') as f:
                    json.dump(save_data, f, indent=2, default=str)

                messagebox.showinfo("Success", f"Results saved to {file_path}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to save results: {str(e)}")

    def _clear_results(self):
        """Clear all results and metrics"""
        self.results_text.delete(1.0, tk.END)
        self.metrics_text.delete(1.0, tk.END)
        self.detection_history.clear()
        self.performance_metrics.clear()

        # Clear visual displays
        self.screenshot_label.configure(image="", text="No screenshot captured")
        if hasattr(self.screenshot_label, 'image'):
            del self.screenshot_label.image

    def _manage_negative_templates(self):
        """Manage negative templates for false positive elimination"""
        dialog = tk.Toplevel(self.window)
        dialog.title("Manage Negative Templates")
        dialog.geometry("600x400")
        dialog.transient(self.window)
        dialog.grab_set()

        # Instructions
        instructions = ttk.Label(dialog, text="Negative templates are images that should NOT be detected.\nThey help eliminate false positives by excluding similar-looking regions.")
        instructions.pack(pady=10, padx=10)

        # Template list
        list_frame = ttk.Frame(dialog)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        ttk.Label(list_frame, text="Negative Templates:").pack(anchor=tk.W)

        listbox_frame = ttk.Frame(list_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        neg_listbox = tk.Listbox(listbox_frame)
        neg_scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=neg_listbox.yview)
        neg_listbox.configure(yscrollcommand=neg_scrollbar.set)

        neg_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        neg_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Populate current negative templates
        for template in self.negative_templates_list:
            neg_listbox.insert(tk.END, template)

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        def add_negative_template():
            file_path = filedialog.askopenfilename(
                title="Select Negative Template",
                initialdir=TEMPLATE_FOLDER,
                filetypes=[("Image files", "*.png *.jpg *.jpeg *.bmp *.gif")]
            )
            if file_path:
                template_name = os.path.basename(file_path)
                if template_name not in self.negative_templates_list:
                    self.negative_templates_list.append(template_name)
                    neg_listbox.insert(tk.END, template_name)

        def remove_negative_template():
            selection = neg_listbox.curselection()
            if selection:
                index = selection[0]
                template_name = neg_listbox.get(index)
                self.negative_templates_list.remove(template_name)
                neg_listbox.delete(index)

        ttk.Button(button_frame, text="Add Template", command=add_negative_template).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Remove Selected", command=remove_negative_template).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Close", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

    def _define_exclusion_regions(self):
        """Define regions to exclude from template matching"""
        dialog = tk.Toplevel(self.window)
        dialog.title("Define Exclusion Regions")
        dialog.geometry("500x300")
        dialog.transient(self.window)
        dialog.grab_set()

        instructions = ttk.Label(dialog, text="Exclusion regions are areas where template matching should be ignored.\nClick 'Capture Region' to select areas on screen to exclude.")
        instructions.pack(pady=10, padx=10)

        # Region list
        list_frame = ttk.Frame(dialog)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        ttk.Label(list_frame, text="Exclusion Regions:").pack(anchor=tk.W)

        region_listbox = tk.Listbox(list_frame)
        region_listbox.pack(fill=tk.BOTH, expand=True, pady=5)

        # Populate current exclusion regions
        for i, region in enumerate(self.exclusion_regions):
            region_listbox.insert(tk.END, f"Region {i+1}: ({region[0]}, {region[1]}, {region[2]}, {region[3]})")

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        def capture_region():
            dialog.withdraw()  # Hide dialog temporarily
            messagebox.showinfo("Capture Region", "Click and drag to select an exclusion region on the screen.")
            # This would need screen capture functionality - simplified for now
            x, y, w, h = 100, 100, 200, 150  # Placeholder values
            self.exclusion_regions.append((x, y, w, h))
            region_listbox.insert(tk.END, f"Region {len(self.exclusion_regions)}: ({x}, {y}, {w}, {h})")
            dialog.deiconify()  # Show dialog again

        def remove_region():
            selection = region_listbox.curselection()
            if selection:
                index = selection[0]
                del self.exclusion_regions[index]
                region_listbox.delete(index)
                # Update remaining items
                region_listbox.delete(0, tk.END)
                for i, region in enumerate(self.exclusion_regions):
                    region_listbox.insert(tk.END, f"Region {i+1}: ({region[0]}, {region[1]}, {region[2]}, {region[3]})")

        ttk.Button(button_frame, text="Capture Region", command=capture_region).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Remove Selected", command=remove_region).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Close", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

    def _configure_validation_templates(self):
        """Configure templates for multi-template validation"""
        dialog = tk.Toplevel(self.window)
        dialog.title("Configure Validation Templates")
        dialog.geometry("600x400")
        dialog.transient(self.window)
        dialog.grab_set()

        instructions = ttk.Label(dialog, text="Validation templates must ALL be detected for a positive match.\nThis helps ensure accuracy by requiring multiple confirming elements.")
        instructions.pack(pady=10, padx=10)

        # Template list
        list_frame = ttk.Frame(dialog)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        ttk.Label(list_frame, text="Validation Templates:").pack(anchor=tk.W)

        val_listbox = tk.Listbox(list_frame)
        val_listbox.pack(fill=tk.BOTH, expand=True, pady=5)

        # Populate current validation templates
        for template in self.validation_templates:
            val_listbox.insert(tk.END, template)

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        def add_validation_template():
            file_path = filedialog.askopenfilename(
                title="Select Validation Template",
                initialdir=TEMPLATE_FOLDER,
                filetypes=[("Image files", "*.png *.jpg *.jpeg *.bmp *.gif")]
            )
            if file_path:
                template_name = os.path.basename(file_path)
                if template_name not in self.validation_templates:
                    self.validation_templates.append(template_name)
                    val_listbox.insert(tk.END, template_name)

        def remove_validation_template():
            selection = val_listbox.curselection()
            if selection:
                index = selection[0]
                template_name = val_listbox.get(index)
                self.validation_templates.remove(template_name)
                val_listbox.delete(index)

        ttk.Button(button_frame, text="Add Template", command=add_validation_template).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Remove Selected", command=remove_validation_template).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Close", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

    def _perform_color_analysis(self, screenshot, template):
        """Perform comprehensive color analysis"""
        try:
            start_time = time.time()

            # Convert to RGB for analysis
            screenshot_rgb = cv2.cvtColor(screenshot, cv2.COLOR_BGR2RGB) if len(screenshot.shape) == 3 else screenshot
            template_rgb = cv2.cvtColor(template, cv2.COLOR_BGR2RGB) if len(template.shape) == 3 else template

            analysis = {
                'processing_time': 0,
                'screenshot_analysis': {},
                'template_analysis': {},
                'color_similarity': 0,
                'dominant_colors_match': False
            }

            # Analyze screenshot colors
            analysis['screenshot_analysis'] = self._analyze_image_colors(screenshot_rgb)

            # Analyze template colors
            analysis['template_analysis'] = self._analyze_image_colors(template_rgb)

            # Calculate color similarity
            analysis['color_similarity'] = self._calculate_color_similarity(
                analysis['screenshot_analysis'], analysis['template_analysis']
            )

            # Check dominant color matching
            analysis['dominant_colors_match'] = self._check_dominant_color_match(
                analysis['screenshot_analysis']['dominant_colors'],
                analysis['template_analysis']['dominant_colors']
            )

            analysis['processing_time'] = time.time() - start_time
            return analysis

        except Exception as e:
            return {'error': str(e), 'processing_time': time.time() - start_time}

    def _analyze_image_colors(self, image):
        """Analyze colors in an image"""
        try:
            # Flatten image for color analysis
            pixels = image.reshape(-1, 3)

            # Calculate dominant colors using K-means
            n_colors = min(self.dominant_colors_count.get(), len(pixels))
            if n_colors > 0:
                kmeans = KMeans(n_clusters=n_colors, random_state=42, n_init=10)
                kmeans.fit(pixels)
                dominant_colors = kmeans.cluster_centers_.astype(int)
                color_percentages = np.bincount(kmeans.labels_) / len(kmeans.labels_)
            else:
                dominant_colors = []
                color_percentages = []

            # Calculate color statistics
            mean_color = np.mean(pixels, axis=0)
            std_color = np.std(pixels, axis=0)

            # HSV analysis if enabled
            hsv_analysis = {}
            if self.hsv_analysis_enabled.get():
                hsv_image = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
                hsv_pixels = hsv_image.reshape(-1, 3)
                hsv_analysis = {
                    'mean_hue': np.mean(hsv_pixels[:, 0]),
                    'mean_saturation': np.mean(hsv_pixels[:, 1]),
                    'mean_value': np.mean(hsv_pixels[:, 2]),
                    'hue_range': (np.min(hsv_pixels[:, 0]), np.max(hsv_pixels[:, 0])),
                    'saturation_range': (np.min(hsv_pixels[:, 1]), np.max(hsv_pixels[:, 1])),
                    'value_range': (np.min(hsv_pixels[:, 2]), np.max(hsv_pixels[:, 2]))
                }

            return {
                'dominant_colors': dominant_colors.tolist() if len(dominant_colors) > 0 else [],
                'color_percentages': color_percentages.tolist() if len(color_percentages) > 0 else [],
                'mean_color': mean_color.tolist(),
                'std_color': std_color.tolist(),
                'hsv_analysis': hsv_analysis,
                'total_pixels': len(pixels)
            }

        except Exception as e:
            return {'error': str(e)}

    def _calculate_color_similarity(self, analysis1, analysis2):
        """Calculate color similarity between two analyses"""
        try:
            if 'error' in analysis1 or 'error' in analysis2:
                return 0.0

            # Compare mean colors
            mean1 = np.array(analysis1['mean_color'])
            mean2 = np.array(analysis2['mean_color'])
            mean_distance = np.linalg.norm(mean1 - mean2)
            mean_similarity = max(0, 1 - (mean_distance / (255 * np.sqrt(3))))

            # Compare dominant colors
            dom_similarity = 0.0
            if analysis1['dominant_colors'] and analysis2['dominant_colors']:
                dom1 = np.array(analysis1['dominant_colors'])
                dom2 = np.array(analysis2['dominant_colors'])

                # Find best matches between dominant colors
                similarities = []
                for color1 in dom1:
                    best_match = 0
                    for color2 in dom2:
                        distance = np.linalg.norm(color1 - color2)
                        similarity = max(0, 1 - (distance / (255 * np.sqrt(3))))
                        best_match = max(best_match, similarity)
                    similarities.append(best_match)

                dom_similarity = np.mean(similarities) if similarities else 0.0

            # Weighted average
            return 0.6 * mean_similarity + 0.4 * dom_similarity

        except Exception as e:
            return 0.0

    def _check_dominant_color_match(self, colors1, colors2):
        """Check if dominant colors match within tolerance"""
        try:
            if not colors1 or not colors2:
                return False

            tolerance = self.color_tolerance.get()

            for color1 in colors1:
                for color2 in colors2:
                    distance = np.linalg.norm(np.array(color1) - np.array(color2))
                    if distance <= tolerance:
                        return True

            return False

        except Exception as e:
            return False

    def _generate_template_heatmaps(self, screenshot, template):
        """Generate template matching heatmaps"""
        try:
            start_time = time.time()

            # Convert to grayscale for template matching
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY) if len(screenshot.shape) == 3 else screenshot
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY) if len(template.shape) == 3 else template

            heatmaps = {}

            # Different matching methods
            methods = {
                'TM_CCOEFF_NORMED': cv2.TM_CCOEFF_NORMED,
                'TM_CCORR_NORMED': cv2.TM_CCORR_NORMED,
                'TM_SQDIFF_NORMED': cv2.TM_SQDIFF_NORMED
            }

            for method_name, method in methods.items():
                try:
                    result = cv2.matchTemplate(screenshot_gray, template_gray, method)

                    # Normalize for visualization
                    if method == cv2.TM_SQDIFF_NORMED:
                        result = 1 - result  # Invert for consistency

                    heatmaps[method_name] = {
                        'heatmap': result.tolist(),
                        'max_val': float(np.max(result)),
                        'min_val': float(np.min(result)),
                        'mean_val': float(np.mean(result)),
                        'shape': result.shape
                    }

                except Exception as e:
                    heatmaps[method_name] = {'error': str(e)}

            return {
                'heatmaps': heatmaps,
                'processing_time': time.time() - start_time
            }

        except Exception as e:
            return {'error': str(e), 'processing_time': time.time() - start_time}

    def _test_multiple_thresholds(self, screenshot, template):
        """Test template matching with multiple confidence thresholds"""
        try:
            start_time = time.time()

            # Convert to grayscale
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY) if len(screenshot.shape) == 3 else screenshot
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY) if len(template.shape) == 3 else template

            # Test different thresholds
            thresholds = [0.5, 0.6, 0.7, 0.8, 0.85, 0.9, 0.95, 0.99]
            threshold_results = {}

            # Perform template matching
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)

            for threshold in thresholds:
                locations = np.where(result >= threshold)
                matches = list(zip(*locations[::-1]))  # Convert to (x, y) format

                threshold_results[str(threshold)] = {
                    'threshold': threshold,
                    'match_count': len(matches),
                    'matches': matches[:10],  # Limit to first 10 matches
                    'max_confidence': float(np.max(result)) if len(matches) > 0 else 0.0
                }

            return {
                'threshold_tests': threshold_results,
                'processing_time': time.time() - start_time,
                'overall_max_confidence': float(np.max(result))
            }

        except Exception as e:
            return {'error': str(e), 'processing_time': time.time() - start_time}

    def _test_template_scaling(self, screenshot, template):
        """Test template matching with different scales"""
        try:
            start_time = time.time()

            # Convert to grayscale
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY) if len(screenshot.shape) == 3 else screenshot
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY) if len(template.shape) == 3 else template

            # Test different scales
            scales = [0.5, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.5, 2.0]
            scaling_results = {}

            for scale in scales:
                try:
                    # Resize template
                    new_width = int(template_gray.shape[1] * scale)
                    new_height = int(template_gray.shape[0] * scale)

                    if new_width > 0 and new_height > 0 and new_width < screenshot_gray.shape[1] and new_height < screenshot_gray.shape[0]:
                        scaled_template = cv2.resize(template_gray, (new_width, new_height))

                        # Perform matching
                        result = cv2.matchTemplate(screenshot_gray, scaled_template, cv2.TM_CCOEFF_NORMED)
                        max_val = float(np.max(result))
                        max_loc = np.unravel_index(np.argmax(result), result.shape)

                        scaling_results[str(scale)] = {
                            'scale': scale,
                            'max_confidence': max_val,
                            'max_location': (int(max_loc[1]), int(max_loc[0])),  # (x, y) format
                            'template_size': (new_width, new_height)
                        }
                    else:
                        scaling_results[str(scale)] = {
                            'scale': scale,
                            'error': 'Invalid template size after scaling'
                        }

                except Exception as e:
                    scaling_results[str(scale)] = {
                        'scale': scale,
                        'error': str(e)
                    }

            return {
                'scaling_tests': scaling_results,
                'processing_time': time.time() - start_time
            }

        except Exception as e:
            return {'error': str(e), 'processing_time': time.time() - start_time}

    def _test_template_rotation(self, screenshot, template):
        """Test template matching with different rotation angles"""
        try:
            start_time = time.time()

            # Parse rotation angles
            angles_str = self.rotation_test_angles.get()
            try:
                angles = [float(angle.strip()) for angle in angles_str.split(',')]
            except:
                angles = [0]  # Default to no rotation if parsing fails

            # Convert to grayscale
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY) if len(screenshot.shape) == 3 else screenshot
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY) if len(template.shape) == 3 else template

            rotation_results = {}

            for angle in angles:
                try:
                    # Rotate template
                    center = (template_gray.shape[1] // 2, template_gray.shape[0] // 2)
                    rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
                    rotated_template = cv2.warpAffine(template_gray, rotation_matrix,
                                                    (template_gray.shape[1], template_gray.shape[0]))

                    # Perform matching
                    result = cv2.matchTemplate(screenshot_gray, rotated_template, cv2.TM_CCOEFF_NORMED)
                    max_val = float(np.max(result))
                    max_loc = np.unravel_index(np.argmax(result), result.shape)

                    rotation_results[str(angle)] = {
                        'angle': angle,
                        'max_confidence': max_val,
                        'max_location': (int(max_loc[1]), int(max_loc[0]))  # (x, y) format
                    }

                except Exception as e:
                    rotation_results[str(angle)] = {
                        'angle': angle,
                        'error': str(e)
                    }

            return {
                'rotation_tests': rotation_results,
                'processing_time': time.time() - start_time
            }

        except Exception as e:
            return {'error': str(e), 'processing_time': time.time() - start_time}

    def _analyze_false_positives(self, screenshot, template, detection_results):
        """Analyze potential false positives and suggest improvements"""
        try:
            start_time = time.time()

            analysis = {
                'potential_issues': [],
                'suggestions': [],
                'negative_template_check': None,
                'exclusion_region_check': None,
                'validation_template_check': None
            }

            # Check for low confidence detections
            best_match = detection_results.get('best_match')
            if best_match and best_match.get('confidence', 0) < 0.8:
                analysis['potential_issues'].append(f"Low confidence detection: {best_match['confidence']:.3f}")
                analysis['suggestions'].append("Consider adjusting template or using additional validation")

            # Check negative templates
            if self.negative_templates_enabled.get() and self.negative_templates_list:
                neg_results = self._check_negative_templates(screenshot)
                analysis['negative_template_check'] = neg_results
                if neg_results.get('matches_found', 0) > 0:
                    analysis['potential_issues'].append("Negative template matches found")
                    analysis['suggestions'].append("Detection may be a false positive due to negative template matches")

            # Check exclusion regions
            if self.region_exclusion_enabled.get() and self.exclusion_regions:
                exclusion_results = self._check_exclusion_regions(detection_results)
                analysis['exclusion_region_check'] = exclusion_results
                if exclusion_results.get('matches_in_exclusion', 0) > 0:
                    analysis['potential_issues'].append("Matches found in exclusion regions")
                    analysis['suggestions'].append("Consider refining exclusion regions or template")

            # Check validation templates
            if self.multi_template_validation.get() and self.validation_templates:
                validation_results = self._check_validation_templates(screenshot)
                analysis['validation_template_check'] = validation_results
                if not validation_results.get('all_validated', False):
                    analysis['potential_issues'].append("Not all validation templates detected")
                    analysis['suggestions'].append("Detection may be incomplete - validation templates missing")

            # Color analysis suggestions
            if 'color_analysis' in detection_results:
                color_sim = detection_results['color_analysis'].get('color_similarity', 0)
                if color_sim < 0.5:
                    analysis['potential_issues'].append(f"Low color similarity: {color_sim:.3f}")
                    analysis['suggestions'].append("Template and detection area have different color profiles")

            analysis['processing_time'] = time.time() - start_time
            return analysis

        except Exception as e:
            return {'error': str(e), 'processing_time': time.time() - start_time}

    def _check_negative_templates(self, screenshot):
        """Check if any negative templates are detected"""
        try:
            matches_found = 0
            negative_matches = []

            for neg_template_name in self.negative_templates_list:
                # Load negative template
                neg_template_path = os.path.join(TEMPLATE_FOLDER, neg_template_name)
                if os.path.exists(neg_template_path):
                    neg_template = cv2.imread(neg_template_path)
                    if neg_template is not None:
                        # Perform matching
                        result = cv2.matchTemplate(
                            cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY),
                            cv2.cvtColor(neg_template, cv2.COLOR_BGR2GRAY),
                            cv2.TM_CCOEFF_NORMED
                        )
                        max_val = float(np.max(result))

                        if max_val > 0.7:  # Threshold for negative template detection
                            matches_found += 1
                            max_loc = np.unravel_index(np.argmax(result), result.shape)
                            negative_matches.append({
                                'template': neg_template_name,
                                'confidence': max_val,
                                'location': (int(max_loc[1]), int(max_loc[0]))
                            })

            return {
                'matches_found': matches_found,
                'negative_matches': negative_matches
            }

        except Exception as e:
            return {'error': str(e)}

    def _check_exclusion_regions(self, detection_results):
        """Check if detections fall within exclusion regions"""
        try:
            matches_in_exclusion = 0
            excluded_matches = []

            # Check all matches against exclusion regions
            for match in detection_results.get('all_matches', []):
                location = match.get('location')
                if location:
                    x, y = location

                    for region in self.exclusion_regions:
                        rx, ry, rw, rh = region
                        if rx <= x <= rx + rw and ry <= y <= ry + rh:
                            matches_in_exclusion += 1
                            excluded_matches.append({
                                'match': match,
                                'exclusion_region': region
                            })
                            break

            return {
                'matches_in_exclusion': matches_in_exclusion,
                'excluded_matches': excluded_matches
            }

        except Exception as e:
            return {'error': str(e)}

    def _check_validation_templates(self, screenshot):
        """Check if all validation templates are detected"""
        try:
            validated_count = 0
            validation_results = []

            for val_template_name in self.validation_templates:
                # Load validation template
                val_template_path = os.path.join(TEMPLATE_FOLDER, val_template_name)
                if os.path.exists(val_template_path):
                    val_template = cv2.imread(val_template_path)
                    if val_template is not None:
                        # Perform matching
                        result = cv2.matchTemplate(
                            cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY),
                            cv2.cvtColor(val_template, cv2.COLOR_BGR2GRAY),
                            cv2.TM_CCOEFF_NORMED
                        )
                        max_val = float(np.max(result))

                        is_validated = max_val > 0.8  # Threshold for validation
                        if is_validated:
                            validated_count += 1

                        max_loc = np.unravel_index(np.argmax(result), result.shape)
                        validation_results.append({
                            'template': val_template_name,
                            'confidence': max_val,
                            'validated': is_validated,
                            'location': (int(max_loc[1]), int(max_loc[0]))
                        })

            all_validated = validated_count == len(self.validation_templates)

            return {
                'all_validated': all_validated,
                'validated_count': validated_count,
                'total_templates': len(self.validation_templates),
                'validation_results': validation_results
            }

        except Exception as e:
            return {'error': str(e)}

    def _apply_ocr_preprocessing(self, image):
        """Apply OCR-specific preprocessing to improve text recognition"""
        try:
            processed = image.copy()

            # Convert to PIL for easier processing
            if len(processed.shape) == 3:
                pil_image = Image.fromarray(cv2.cvtColor(processed, cv2.COLOR_BGR2RGB))
            else:
                pil_image = Image.fromarray(processed)

            # Apply brightness adjustment
            if self.ocr_brightness_boost.get() != 1.0:
                enhancer = ImageEnhance.Brightness(pil_image)
                pil_image = enhancer.enhance(self.ocr_brightness_boost.get())

            # Apply contrast adjustment
            if self.ocr_contrast_boost.get() != 1.0:
                enhancer = ImageEnhance.Contrast(pil_image)
                pil_image = enhancer.enhance(self.ocr_contrast_boost.get())

            # Convert back to OpenCV format
            processed = np.array(pil_image)
            if len(processed.shape) == 3:
                processed = cv2.cvtColor(processed, cv2.COLOR_RGB2BGR)

            # Apply noise reduction
            if self.ocr_noise_reduction.get():
                processed = cv2.medianBlur(processed, 3)
                processed = cv2.bilateralFilter(processed, 9, 75, 75)

            # Apply morphological operations
            if self.ocr_morphology_enabled.get():
                if len(processed.shape) == 3:
                    processed = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)

                # Morphological opening to remove noise
                kernel = np.ones((2, 2), np.uint8)
                processed = cv2.morphologyEx(processed, cv2.MORPH_OPEN, kernel)

                # Morphological closing to fill gaps
                kernel = np.ones((3, 3), np.uint8)
                processed = cv2.morphologyEx(processed, cv2.MORPH_CLOSE, kernel)

            return processed

        except Exception as e:
            print(f"OCR preprocessing error: {e}")
            return image  # Return original if preprocessing fails

    def _display_enhanced_analysis_results(self, results):
        """Display enhanced analysis results"""
        try:
            # Color Analysis Results
            if 'color_analysis' in results:
                self.results_text.insert(tk.END, f"\n🎨 COLOR ANALYSIS:\n")
                self.results_text.insert(tk.END, "-" * 40 + "\n")

                color_analysis = results['color_analysis']
                if 'error' not in color_analysis:
                    similarity = color_analysis.get('color_similarity', 0)
                    self.results_text.insert(tk.END, f"   🎯 Color Similarity: {similarity:.3f}\n")

                    dom_match = color_analysis.get('dominant_colors_match', False)
                    status = "✅ MATCH" if dom_match else "❌ NO MATCH"
                    self.results_text.insert(tk.END, f"   🎨 Dominant Colors: {status}\n")

                    # Display color analysis in separate tab
                    self._update_color_analysis_display(color_analysis)
                else:
                    self.results_text.insert(tk.END, f"   ❌ Error: {color_analysis['error']}\n")

            # Threshold Analysis Results
            if 'threshold_analysis' in results:
                self.results_text.insert(tk.END, f"\n📊 THRESHOLD ANALYSIS:\n")
                self.results_text.insert(tk.END, "-" * 40 + "\n")

                threshold_analysis = results['threshold_analysis']
                if 'error' not in threshold_analysis:
                    max_conf = threshold_analysis.get('overall_max_confidence', 0)
                    self.results_text.insert(tk.END, f"   🎯 Max Confidence: {max_conf:.3f}\n")

                    # Show threshold breakdown
                    threshold_tests = threshold_analysis.get('threshold_tests', {})
                    for threshold, test_result in threshold_tests.items():
                        count = test_result.get('match_count', 0)
                        if count > 0:
                            self.results_text.insert(tk.END, f"   📈 {threshold}: {count} matches\n")
                else:
                    self.results_text.insert(tk.END, f"   ❌ Error: {threshold_analysis['error']}\n")

            # Scaling Analysis Results
            if 'scaling_analysis' in results:
                self.results_text.insert(tk.END, f"\n📏 SCALING ANALYSIS:\n")
                self.results_text.insert(tk.END, "-" * 40 + "\n")

                scaling_analysis = results['scaling_analysis']
                if 'error' not in scaling_analysis:
                    scaling_tests = scaling_analysis.get('scaling_tests', {})
                    best_scale = None
                    best_confidence = 0

                    for scale, test_result in scaling_tests.items():
                        if 'error' not in test_result:
                            confidence = test_result.get('max_confidence', 0)
                            if confidence > best_confidence:
                                best_confidence = confidence
                                best_scale = scale

                    if best_scale:
                        self.results_text.insert(tk.END, f"   🎯 Best Scale: {best_scale} (conf: {best_confidence:.3f})\n")
                    else:
                        self.results_text.insert(tk.END, f"   ❌ No successful scaling matches\n")
                else:
                    self.results_text.insert(tk.END, f"   ❌ Error: {scaling_analysis['error']}\n")

            # Rotation Analysis Results
            if 'rotation_analysis' in results:
                self.results_text.insert(tk.END, f"\n🔄 ROTATION ANALYSIS:\n")
                self.results_text.insert(tk.END, "-" * 40 + "\n")

                rotation_analysis = results['rotation_analysis']
                if 'error' not in rotation_analysis:
                    rotation_tests = rotation_analysis.get('rotation_tests', {})
                    best_angle = None
                    best_confidence = 0

                    for angle, test_result in rotation_tests.items():
                        if 'error' not in test_result:
                            confidence = test_result.get('max_confidence', 0)
                            if confidence > best_confidence:
                                best_confidence = confidence
                                best_angle = angle

                    if best_angle:
                        self.results_text.insert(tk.END, f"   🎯 Best Angle: {best_angle}° (conf: {best_confidence:.3f})\n")
                    else:
                        self.results_text.insert(tk.END, f"   ❌ No successful rotation matches\n")
                else:
                    self.results_text.insert(tk.END, f"   ❌ Error: {rotation_analysis['error']}\n")

            # False Positive Analysis Results
            if 'false_positive_analysis' in results:
                self.results_text.insert(tk.END, f"\n🚨 FALSE POSITIVE ANALYSIS:\n")
                self.results_text.insert(tk.END, "-" * 40 + "\n")

                fp_analysis = results['false_positive_analysis']
                if 'error' not in fp_analysis:
                    issues = fp_analysis.get('potential_issues', [])
                    suggestions = fp_analysis.get('suggestions', [])

                    if issues:
                        self.results_text.insert(tk.END, f"   ⚠️  Potential Issues:\n")
                        for issue in issues:
                            self.results_text.insert(tk.END, f"      • {issue}\n")

                    if suggestions:
                        self.results_text.insert(tk.END, f"   💡 Suggestions:\n")
                        for suggestion in suggestions:
                            self.results_text.insert(tk.END, f"      • {suggestion}\n")

                    if not issues and not suggestions:
                        self.results_text.insert(tk.END, f"   ✅ No potential issues detected\n")

                    # Display detailed FP analysis in separate tab
                    self._update_fp_analysis_display(fp_analysis)
                else:
                    self.results_text.insert(tk.END, f"   ❌ Error: {fp_analysis['error']}\n")

            # Enhanced OCR Results
            if 'ocr' in results.get('methods', {}):
                ocr_result = results['methods']['ocr']
                if 'engines' in ocr_result:
                    self._update_ocr_analysis_display(ocr_result)

            # Heatmap Results
            if 'heatmaps' in results:
                self._update_heatmap_display(results['heatmaps'])

        except Exception as e:
            self.results_text.insert(tk.END, f"\n❌ Error displaying enhanced results: {str(e)}\n")

    def _update_color_analysis_display(self, color_analysis):
        """Update the color analysis tab with detailed results"""
        try:
            # Clear previous content
            for widget in self.color_analysis_container.winfo_children():
                widget.destroy()

            # Create matplotlib figure for color analysis
            fig = Figure(figsize=(12, 8), dpi=100)

            # Screenshot color analysis
            if 'screenshot_analysis' in color_analysis and 'error' not in color_analysis['screenshot_analysis']:
                screenshot_analysis = color_analysis['screenshot_analysis']

                # Dominant colors plot
                ax1 = fig.add_subplot(2, 2, 1)
                if screenshot_analysis.get('dominant_colors'):
                    colors = np.array(screenshot_analysis['dominant_colors']) / 255.0
                    percentages = screenshot_analysis.get('color_percentages', [])

                    bars = ax1.bar(range(len(colors)), percentages, color=colors)
                    ax1.set_title('Screenshot Dominant Colors')
                    ax1.set_xlabel('Color Index')
                    ax1.set_ylabel('Percentage')

                # HSV analysis if available
                if screenshot_analysis.get('hsv_analysis'):
                    ax2 = fig.add_subplot(2, 2, 2)
                    hsv = screenshot_analysis['hsv_analysis']

                    hsv_data = [hsv.get('mean_hue', 0), hsv.get('mean_saturation', 0), hsv.get('mean_value', 0)]
                    hsv_labels = ['Hue', 'Saturation', 'Value']

                    ax2.bar(hsv_labels, hsv_data)
                    ax2.set_title('Screenshot HSV Analysis')
                    ax2.set_ylabel('Mean Value')

            # Template color analysis
            if 'template_analysis' in color_analysis and 'error' not in color_analysis['template_analysis']:
                template_analysis = color_analysis['template_analysis']

                # Template dominant colors
                ax3 = fig.add_subplot(2, 2, 3)
                if template_analysis.get('dominant_colors'):
                    colors = np.array(template_analysis['dominant_colors']) / 255.0
                    percentages = template_analysis.get('color_percentages', [])

                    bars = ax3.bar(range(len(colors)), percentages, color=colors)
                    ax3.set_title('Template Dominant Colors')
                    ax3.set_xlabel('Color Index')
                    ax3.set_ylabel('Percentage')

                # Template HSV analysis
                if template_analysis.get('hsv_analysis'):
                    ax4 = fig.add_subplot(2, 2, 4)
                    hsv = template_analysis['hsv_analysis']

                    hsv_data = [hsv.get('mean_hue', 0), hsv.get('mean_saturation', 0), hsv.get('mean_value', 0)]
                    hsv_labels = ['Hue', 'Saturation', 'Value']

                    ax4.bar(hsv_labels, hsv_data)
                    ax4.set_title('Template HSV Analysis')
                    ax4.set_ylabel('Mean Value')

            plt.tight_layout()

            # Add to GUI
            canvas = FigureCanvasTkAgg(fig, self.color_analysis_container)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        except Exception as e:
            error_label = ttk.Label(self.color_analysis_container, text=f"Error displaying color analysis: {str(e)}")
            error_label.pack(pady=20)

    def _update_ocr_analysis_display(self, ocr_result):
        """Update the OCR analysis tab with detailed results"""
        try:
            self.ocr_results_text.delete(1.0, tk.END)

            self.ocr_results_text.insert(tk.END, "🔤 OCR ANALYSIS RESULTS\n")
            self.ocr_results_text.insert(tk.END, "=" * 50 + "\n\n")

            engines = ocr_result.get('engines', {})

            for engine_name, engine_result in engines.items():
                self.ocr_results_text.insert(tk.END, f"🔧 {engine_name.upper()}:\n")
                self.ocr_results_text.insert(tk.END, "-" * 30 + "\n")

                if 'error' in engine_result:
                    self.ocr_results_text.insert(tk.END, f"   ❌ Error: {engine_result['error']}\n\n")
                else:
                    # For enhanced Tesseract results
                    if 'all_results' in engine_result:
                        self.ocr_results_text.insert(tk.END, f"   📊 Configuration Tests:\n")
                        for config_name, config_result in engine_result['all_results'].items():
                            confidence = config_result.get('confidence', 0)
                            text = config_result.get('text', '')
                            self.ocr_results_text.insert(tk.END, f"      {config_name}: {confidence:.3f} - '{text}'\n")

                        best_result = engine_result.get('best_result', {})
                        if best_result:
                            self.ocr_results_text.insert(tk.END, f"   🏆 Best Result:\n")
                            self.ocr_results_text.insert(tk.END, f"      Text: '{best_result.get('text', '')}'\n")
                            self.ocr_results_text.insert(tk.END, f"      Confidence: {best_result.get('confidence', 0):.3f}\n")
                            self.ocr_results_text.insert(tk.END, f"      Config: {best_result.get('config', 'N/A')}\n")
                    else:
                        # Standard OCR results
                        text = engine_result.get('text', '')
                        confidence = engine_result.get('confidence', 0)
                        self.ocr_results_text.insert(tk.END, f"   📝 Text: '{text}'\n")
                        self.ocr_results_text.insert(tk.END, f"   🎯 Confidence: {confidence:.3f}\n")

                        if 'detections' in engine_result:
                            self.ocr_results_text.insert(tk.END, f"   🔍 Detections: {engine_result['detections']}\n")

                self.ocr_results_text.insert(tk.END, "\n")

        except Exception as e:
            self.ocr_results_text.insert(tk.END, f"Error displaying OCR results: {str(e)}\n")

    def _update_heatmap_display(self, heatmap_results):
        """Update the heatmap tab with template matching heatmaps"""
        try:
            # Clear previous content
            for widget in self.heatmap_container.winfo_children():
                widget.destroy()

            if 'error' in heatmap_results:
                error_label = ttk.Label(self.heatmap_container, text=f"Error generating heatmaps: {heatmap_results['error']}")
                error_label.pack(pady=20)
                return

            heatmaps = heatmap_results.get('heatmaps', {})
            if not heatmaps:
                no_data_label = ttk.Label(self.heatmap_container, text="No heatmap data available")
                no_data_label.pack(pady=20)
                return

            # Create matplotlib figure for heatmaps
            fig = Figure(figsize=(15, 10), dpi=100)

            plot_count = len(heatmaps)
            cols = min(3, plot_count)
            rows = (plot_count + cols - 1) // cols

            for i, (method_name, heatmap_data) in enumerate(heatmaps.items()):
                if 'error' not in heatmap_data:
                    ax = fig.add_subplot(rows, cols, i + 1)

                    heatmap_array = np.array(heatmap_data['heatmap'])

                    # Create heatmap
                    im = ax.imshow(heatmap_array, cmap='hot', interpolation='nearest')
                    ax.set_title(f'{method_name}\nMax: {heatmap_data["max_val"]:.3f}')
                    ax.set_xlabel('X Position')
                    ax.set_ylabel('Y Position')

                    # Add colorbar
                    plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)

            plt.tight_layout()

            # Add to GUI
            canvas = FigureCanvasTkAgg(fig, self.heatmap_container)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        except Exception as e:
            error_label = ttk.Label(self.heatmap_container, text=f"Error displaying heatmaps: {str(e)}")
            error_label.pack(pady=20)

    def _update_fp_analysis_display(self, fp_analysis):
        """Update the false positive analysis tab"""
        try:
            self.fp_analysis_text.delete(1.0, tk.END)

            self.fp_analysis_text.insert(tk.END, "🚨 FALSE POSITIVE ANALYSIS\n")
            self.fp_analysis_text.insert(tk.END, "=" * 50 + "\n\n")

            # Potential Issues
            issues = fp_analysis.get('potential_issues', [])
            if issues:
                self.fp_analysis_text.insert(tk.END, "⚠️  POTENTIAL ISSUES:\n")
                self.fp_analysis_text.insert(tk.END, "-" * 30 + "\n")
                for issue in issues:
                    self.fp_analysis_text.insert(tk.END, f"• {issue}\n")
                self.fp_analysis_text.insert(tk.END, "\n")

            # Suggestions
            suggestions = fp_analysis.get('suggestions', [])
            if suggestions:
                self.fp_analysis_text.insert(tk.END, "💡 SUGGESTIONS:\n")
                self.fp_analysis_text.insert(tk.END, "-" * 30 + "\n")
                for suggestion in suggestions:
                    self.fp_analysis_text.insert(tk.END, f"• {suggestion}\n")
                self.fp_analysis_text.insert(tk.END, "\n")

            # Detailed Analysis Results
            if 'negative_template_check' in fp_analysis:
                neg_check = fp_analysis['negative_template_check']
                if neg_check and 'error' not in neg_check:
                    self.fp_analysis_text.insert(tk.END, "🚫 NEGATIVE TEMPLATE CHECK:\n")
                    self.fp_analysis_text.insert(tk.END, "-" * 30 + "\n")
                    matches_found = neg_check.get('matches_found', 0)
                    self.fp_analysis_text.insert(tk.END, f"Negative matches found: {matches_found}\n")

                    if matches_found > 0:
                        negative_matches = neg_check.get('negative_matches', [])
                        for match in negative_matches:
                            template = match.get('template', 'Unknown')
                            confidence = match.get('confidence', 0)
                            location = match.get('location', (0, 0))
                            self.fp_analysis_text.insert(tk.END, f"  • {template}: {confidence:.3f} at {location}\n")
                    self.fp_analysis_text.insert(tk.END, "\n")

            if 'validation_template_check' in fp_analysis:
                val_check = fp_analysis['validation_template_check']
                if val_check and 'error' not in val_check:
                    self.fp_analysis_text.insert(tk.END, "✅ VALIDATION TEMPLATE CHECK:\n")
                    self.fp_analysis_text.insert(tk.END, "-" * 30 + "\n")
                    all_validated = val_check.get('all_validated', False)
                    validated_count = val_check.get('validated_count', 0)
                    total_templates = val_check.get('total_templates', 0)

                    status = "PASS" if all_validated else "FAIL"
                    self.fp_analysis_text.insert(tk.END, f"Validation Status: {status} ({validated_count}/{total_templates})\n")

                    validation_results = val_check.get('validation_results', [])
                    for result in validation_results:
                        template = result.get('template', 'Unknown')
                        confidence = result.get('confidence', 0)
                        validated = result.get('validated', False)
                        status_icon = "✅" if validated else "❌"
                        self.fp_analysis_text.insert(tk.END, f"  {status_icon} {template}: {confidence:.3f}\n")
                    self.fp_analysis_text.insert(tk.END, "\n")

            if not issues and not suggestions:
                self.fp_analysis_text.insert(tk.END, "✅ NO POTENTIAL ISSUES DETECTED\n")
                self.fp_analysis_text.insert(tk.END, "The detection appears to be reliable.\n")

        except Exception as e:
            self.fp_analysis_text.insert(tk.END, f"Error displaying FP analysis: {str(e)}\n")


def main():
    """Test the diagnostics interface"""
    root = tk.Tk()
    root.withdraw()  # Hide main window

    diagnostics = TemplateDetectionDiagnostics(root)
    diagnostics.show_diagnostics_window()

    root.mainloop()


if __name__ == "__main__":
    main()
