#!/usr/bin/env python3
"""Simple test to verify Good_setting_map integration"""

import sys
import os
import json

def main():
    print("=" * 50)
    print("TESTING GOOD_SETTING_MAP INTEGRATION")
    print("=" * 50)
    
    # Test 1: Check Good_setting_map.json
    good_setting_path = os.path.join("templates", "Good_setting_map.json")
    if os.path.exists(good_setting_path):
        print("✅ Good_setting_map.json found")
        with open(good_setting_path, 'r') as f:
            coords = json.load(f)
        print(f"✅ Contains {len(coords)} coordinates")
        for piece, pos in coords.items():
            print(f"   {piece}: ({pos[0]}, {pos[1]})")
    else:
        print("❌ Good_setting_map.json NOT found")
        return
    
    # Test 2: Import map trade module
    try:
        print("\n📦 Importing MapTradeModule...")
        from modules.map_trade import MapTradeModule
        print("✅ Import successful")
        
        # Test 3: Create instance
        print("\n🔧 Creating module instance...")
        module = MapTradeModule()
        print("✅ Module created")
        
        # Test 4: Check coordinates
        print("\n📍 Checking map piece coordinates:")
        for piece, data in module.map_piece_regions.items():
            big_map_pos = data['big_map_pos']
            print(f"   {piece}: big_map_pos=({big_map_pos[0]}, {big_map_pos[1]})")
        
        # Test 5: Check Fast OCR
        fast_ocr_available = getattr(module, '_fast_ocr_available', False)
        print(f"\n🚀 Fast OCR available: {fast_ocr_available}")
        
        print("\n" + "=" * 50)
        print("🎉 ALL TESTS PASSED!")
        print("✅ Map Trade Module now uses Good_setting_map coordinates")
        print("✅ Fast Map OCR system integrated")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
