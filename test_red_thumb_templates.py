#!/usr/bin/env python3
"""
Test script to check red thumb templates and their properties
"""

import cv2
import os
import warnings

# Suppress warnings
warnings.filterwarnings("ignore", message=".*libpng warning.*")
warnings.filterwarnings("ignore", message=".*sBIT.*")
os.environ['OPENCV_LOG_LEVEL'] = 'ERROR'

def test_red_thumb_templates():
    """Test all red thumb templates"""
    print("🔍 Testing Red Thumb Templates")
    print("=" * 50)
    
    red_thumb_templates = [
        "Red_thumb.png",           # User-created correct template
        "red_thumb_100x40.png",    # Old template
        "red_thumb_80x30.png",
        "red_thumb_50x25.png", 
        "red_thumb_30x15.png"
    ]
    
    for template_name in red_thumb_templates:
        template_path = os.path.join("templates", template_name)
        print(f"\n📁 Template: {template_name}")
        
        if os.path.exists(template_path):
            print(f"   ✅ File exists: {template_path}")
            
            # Get file size
            file_size = os.path.getsize(template_path)
            print(f"   📏 File size: {file_size} bytes")
            
            # Try to load with OpenCV
            try:
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    template = cv2.imread(template_path)
                
                if template is not None:
                    height, width = template.shape[:2]
                    print(f"   🖼️  Image dimensions: {width}x{height}")
                    print(f"   🎨 Color channels: {template.shape[2] if len(template.shape) > 2 else 1}")
                    
                    # Check if it's mostly red
                    if len(template.shape) == 3:
                        # BGR format - check if red channel is dominant
                        b, g, r = cv2.split(template)
                        avg_red = r.mean()
                        avg_green = g.mean()
                        avg_blue = b.mean()
                        print(f"   🔴 Average BGR: B={avg_blue:.1f}, G={avg_green:.1f}, R={avg_red:.1f}")
                        
                        if avg_red > avg_green and avg_red > avg_blue:
                            print(f"   ✅ Template appears to be red-dominant")
                        else:
                            print(f"   ⚠️  Template may not be red-dominant")
                    
                else:
                    print(f"   ❌ Failed to load image with OpenCV")
                    
            except Exception as e:
                print(f"   ❌ Error loading template: {e}")
        else:
            print(f"   ❌ File not found: {template_path}")
    
    print("\n" + "=" * 50)
    print("🎯 Recommendation:")
    print("   - Red_thumb.png should be the primary template")
    print("   - It should be located around coordinates (1533, 814)")
    print("   - Template should be red-dominant in color")

if __name__ == "__main__":
    test_red_thumb_templates()
