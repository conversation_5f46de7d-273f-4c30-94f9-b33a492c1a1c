# Comprehensive Crash Recovery and Debugging System

## Overview

The Crash Recovery System provides robust recovery from both module-level failures and game-level issues while preventing infinite retry loops. It automatically handles module crashes, game window detection, ESC recovery sequences, and game launching.

## Key Features

### 🔍 **Window Focus Verification**
- Detects if "Last War-Survival Game" window is currently focused and active
- Uses multiple window title patterns for reliable detection
- Verifies window visibility and reasonable size (>100x100 pixels)

### 🔄 **Intelligent Recovery Logic**

#### **When Module Execution Fails (Game Running):**
1. **Log Failure**: Detailed error information with timestamps and module names
2. **Execute ESC Recovery**: Return to main menu using ESC sequences
3. **Restart Module**: Module automatically retries on next cycle
4. **Track Failures**: Count consecutive failures per module
5. **Pause Protection**: Pause automation after 3 consecutive failures

#### **When Game Window Not Detected:**
1. **Log Window Status**: Record that game window was not found
2. **Launch Game**: Execute `"C:\Users\<USER>\Desktop\Last War：Survival Game.lnk"`
3. **Wait for Load**: Exactly 30 seconds for game to fully load
4. **Re-activate Scanner**: Resume automation with centralized scanner
5. **Track Launch Attempts**: Monitor consecutive game launch attempts

### 🛡️ **Failure Protection Systems**

#### **Module Failure Protection:**
- **Consecutive Failure Tracking**: Each module tracked independently
- **3-Strike Rule**: Pause automation after 3 consecutive failures
- **Failure Reset**: Reset counter on successful execution
- **Detailed Logging**: All failures logged with error details and timestamps

#### **Game Launch Failure Protection:**
- **2-Attempt Limit**: Maximum 2 consecutive launch attempts
- **10-Minute Pause**: Automatic pause after 2 failed launches
- **Retry Logic**: Resume launch attempts after pause period
- **Launch Tracking**: Monitor all launch attempts and wait periods

### 📊 **Comprehensive Logging**

All events are logged with detailed information:
- **Crash Events**: Timestamps, module names, error details, error types
- **Window Detection**: Found/not found status with window details
- **ESC Recovery**: Attempts, results, and recovery sequences
- **Game Launch**: Attempts, wait periods, success/failure status
- **Module Failures**: Consecutive failure counts and tracking
- **System Status**: Automation pause/resume events with reasons

## Integration Points

### **Main Controller Integration**
```python
# Automatic initialization in MainController
self.crash_recovery = get_crash_recovery_system(self.logger)
self.crash_recovery.set_main_controller(self)
self.crash_recovery.set_centralized_scanner(self.centralized_scanner)
```

### **Module Execution Error Handling**
```python
# Integrated into module execution loop
try:
    success = module.run(screen_data)
    if success:
        self.crash_recovery.reset_module_failure_count(module.name)
    else:
        should_continue = self.crash_recovery.handle_module_failure(
            module.name, Exception("Module execution returned False")
        )
except Exception as module_error:
    should_continue = self.crash_recovery.handle_module_failure(
        module.name, module_error
    )
```

### **Mouse Override System Management**
- **Automatic Disable**: Mouse override disabled during recovery sequences
- **Cooldown Management**: 3-second cooldown after recovery completion
- **Context Management**: Proper enable/disable around all recovery operations

### **GUI Status Integration**
- **Status Display**: Crash recovery status shown in main status label
- **Status Tree**: Dedicated crash recovery entry in module status tree
- **Priority Display**: Crash recovery status takes priority over other status

## Configuration

### **Game Configuration**
```python
# Game shortcut path (configurable)
self.game_shortcut_path = r"C:\Users\<USER>\Desktop\Last War：Survival Game.lnk"
self.game_window_title = "Last War-Survival Game"
self.game_load_wait_time = 30.0  # Wait 30 seconds for game to load
```

### **Failure Limits**
```python
# Module failure limits
self.max_module_failures = 3  # Pause after 3 consecutive failures
self.max_game_launch_attempts = 2  # Pause after 2 failed launches
self.game_launch_pause_duration = 600.0  # 10 minutes in seconds
```

## Usage Examples

### **Testing the System**
```bash
# Run comprehensive tests
python test_crash_recovery_system.py

# Test specific components
python -c "from crash_recovery_system import get_crash_recovery_system; 
           crs = get_crash_recovery_system(); 
           print(crs.is_game_window_active())"
```

### **Manual Recovery Operations**
```python
# Get crash recovery system
from crash_recovery_system import get_crash_recovery_system
crash_recovery = get_crash_recovery_system()

# Check game window status
is_active, status = crash_recovery.is_game_window_active()

# Execute ESC recovery
success = crash_recovery.execute_esc_recovery()

# Launch game manually
launch_success = crash_recovery.launch_game()

# Reset module failure count
crash_recovery.reset_module_failure_count("module_name")

# Get system status
status = crash_recovery.get_status()
```

## Status Information

### **System Status Dictionary**
```python
{
    'automation_paused': False,           # Is automation currently paused
    'pause_reason': '',                   # Reason for pause (if paused)
    'module_failure_counts': {            # Failure count per module
        'module_name': 2
    },
    'game_launch_attempts': 0,            # Current consecutive launch attempts
    'last_game_launch_pause': 0,          # Timestamp of last pause
    'game_launch_pause_remaining': 0      # Seconds remaining in pause
}
```

### **GUI Status Display**
- **Main Status**: Shows crash recovery events with highest priority
- **Status Tree**: Dedicated "🚨 Crash Recovery" entry with detailed information
- **Color Coding**: Red for paused, Orange for pauses/cooldowns, Green for normal

## Recovery Sequences

### **ESC Recovery Sequence**
1. **Disable Mouse Override**: Prevent interference during recovery
2. **Execute ESC Presses**: 5 ESC presses with 1-second delays
3. **Centralized Scanner**: Use scanner's ESC recovery if available
4. **Fallback Logic**: Simple ESC sequence if centralized recovery fails
5. **Re-enable Mouse Override**: 3-second cooldown after completion

### **Game Launch Sequence**
1. **Verify Shortcut**: Check if game shortcut exists
2. **Launch Process**: Execute shortcut using subprocess
3. **Wait Period**: 30-second wait for game to fully load
4. **Window Verification**: Check if game window is now active
5. **Scanner Reactivation**: Reset scanner and resume automation

## Error Handling

### **Graceful Degradation**
- **Missing Dependencies**: Window detection disabled if pygetwindow unavailable
- **Shortcut Not Found**: Game launching disabled, manual intervention required
- **Recovery Failures**: Continue automation with logging, don't crash system
- **Exception Handling**: All recovery operations wrapped in try-catch blocks

### **Infinite Loop Prevention**
- **Failure Limits**: Hard limits on consecutive failures and launch attempts
- **Pause Periods**: Mandatory wait periods between retry attempts
- **Manual Override**: GUI controls to pause/resume automation manually
- **Logging Limits**: Prevent log spam with rate limiting

## Monitoring and Debugging

### **Log Messages**
- `[CRASH_RECOVERY]`: General crash recovery system messages
- `[MODULE_FAILURE]`: Module failure events and handling
- `[WINDOW_CHECK]`: Game window detection results
- `[ESC_RECOVERY]`: ESC recovery sequence events
- `[GAME_LAUNCH]`: Game launching attempts and results
- `[AUTOMATION_PAUSE]`: System pause/resume events

### **Debug Information**
- **Detailed Tracebacks**: Full exception information for all crashes
- **Timing Information**: Execution times and wait periods
- **Window Details**: Window titles, sizes, and visibility status
- **Recovery Results**: Success/failure status of all recovery attempts

## Best Practices

### **Configuration**
- **Verify Shortcut Path**: Ensure game shortcut exists and is correct
- **Test Window Detection**: Run tests to verify window detection works
- **Monitor Logs**: Watch for crash recovery messages during operation
- **Adjust Limits**: Modify failure limits based on system stability

### **Troubleshooting**
- **Check Dependencies**: Ensure pygetwindow is installed for window detection
- **Verify Game Path**: Confirm game shortcut path is correct
- **Monitor Memory**: Watch for memory issues that might cause crashes
- **Review Logs**: Check detailed logs for specific failure patterns

## Future Enhancements

### **Planned Features**
- **Smart Recovery**: AI-based recovery decision making
- **Performance Monitoring**: Track recovery success rates
- **Custom Recovery**: Module-specific recovery sequences
- **Remote Monitoring**: Web-based crash recovery dashboard
- **Predictive Analysis**: Predict failures before they occur

This crash recovery system provides comprehensive protection against automation failures while maintaining system stability and preventing infinite retry loops.
