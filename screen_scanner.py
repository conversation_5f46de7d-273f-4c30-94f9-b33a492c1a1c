"""
Screen scanning module for Last War automation
Handles screen capture, OCR, and image template matching
"""
import cv2
import numpy as np
import pyautogui
import pytesseract
from PIL import Image
import os
import logging
from typing import Dict, List, Tuple, Optional, Any
import time


class ScreenScanner:
    """Handles screen scanning, OCR, and template matching"""
    
    def __init__(self, templates_path: str = "templates/", screenshots_path: str = "screenshots/"):
        self.templates_path = templates_path
        self.screenshots_path = screenshots_path
        self.logger = logging.getLogger("LastWar.ScreenScanner")
        
        # Ensure directories exist
        os.makedirs(templates_path, exist_ok=True)
        os.makedirs(screenshots_path, exist_ok=True)
        
        # Load templates
        self.templates = self._load_templates()
        
        # Configure pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.1
        
    def _load_templates(self) -> Dict[str, np.ndarray]:
        """Load all template images from templates directory"""
        templates = {}
        
        if not os.path.exists(self.templates_path):
            self.logger.warning(f"Templates directory {self.templates_path} not found")
            return templates
            
        for filename in os.listdir(self.templates_path):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                template_path = os.path.join(self.templates_path, filename)
                template_name = os.path.splitext(filename)[0]
                
                try:
                    template = cv2.imread(template_path, cv2.IMREAD_COLOR)
                    if template is not None:
                        templates[template_name] = template
                        self.logger.debug(f"Loaded template: {template_name}")
                    else:
                        self.logger.warning(f"Failed to load template: {filename}")
                except Exception as e:
                    self.logger.error(f"Error loading template {filename}: {str(e)}")
                    
        self.logger.info(f"Loaded {len(templates)} templates")
        return templates
    
    def capture_screen_to_memory(self, region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        Capture screenshot directly to memory cache - NO DISK SAVING!

        Args:
            region: Tuple of (left, top, width, height) for specific region

        Returns:
            np.ndarray: Screenshot as OpenCV image in memory only
        """
        try:
            if region:
                screenshot = pyautogui.screenshot(region=region)
            else:
                screenshot = pyautogui.screenshot()

            # Convert PIL to OpenCV format - KEEP IN MEMORY ONLY
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            # NO DISK SAVING - pure memory cache approach!
            return screenshot_cv

        except Exception as e:
            self.logger.error(f"Error capturing screen to memory: {str(e)}")
            return None

    def capture_screen(self, region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """Legacy method - redirects to memory-only capture"""
        return self.capture_screen_to_memory(region)
    
    def find_template(self, screenshot: np.ndarray, template_name: str, 
                     threshold: float = 0.8) -> List[Tuple[int, int, int, int]]:
        """
        Find template matches in screenshot
        
        Args:
            screenshot: Screenshot to search in
            template_name: Name of template to find
            threshold: Matching threshold (0.0 to 1.0)
            
        Returns:
            List of (x, y, width, height) tuples for matches
        """
        if template_name not in self.templates:
            self.logger.warning(f"[WARNING] Template {template_name} not loaded")
            return []

        template = self.templates[template_name]

        try:
            # Validate inputs before OpenCV operations
            if screenshot is None:
                self.logger.error(f"Screenshot is None for template {template_name}")
                return []

            if not isinstance(screenshot, np.ndarray):
                self.logger.error(f"Screenshot is not a numpy array for template {template_name}: {type(screenshot)}")
                return []

            if template is None:
                self.logger.error(f"Template {template_name} is None")
                return []

            if not isinstance(template, np.ndarray):
                self.logger.error(f"Template {template_name} is not a numpy array: {type(template)}")
                return []

            # Check dimensions
            if len(screenshot.shape) < 2 or len(template.shape) < 2:
                self.logger.error(f"Invalid dimensions - screenshot: {screenshot.shape}, template: {template.shape}")
                return []

            # Perform template matching
            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
            locations = np.where(result >= threshold)
            
            matches = []
            h, w = template.shape[:2]
            
            for pt in zip(*locations[::-1]):  # Switch x and y
                matches.append((pt[0], pt[1], w, h))
            
            # Remove overlapping matches
            matches = self._remove_overlapping_matches(matches)
            
            self.logger.debug(f"Found {len(matches)} matches for template {template_name}")
            return matches
            
        except Exception as e:
            self.logger.error(f"Error finding template {template_name}: {str(e)}")
            return []
    
    def _remove_overlapping_matches(self, matches: List[Tuple[int, int, int, int]], 
                                  overlap_threshold: float = 0.5) -> List[Tuple[int, int, int, int]]:
        """Remove overlapping template matches"""
        if len(matches) <= 1:
            return matches
        
        # Sort by confidence or position
        matches = sorted(matches, key=lambda x: x[0])  # Sort by x position
        
        filtered_matches = []
        for match in matches:
            x1, y1, w1, h1 = match
            
            # Check if this match overlaps significantly with any existing match
            overlaps = False
            for existing in filtered_matches:
                x2, y2, w2, h2 = existing
                
                # Calculate overlap
                overlap_x = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
                overlap_y = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
                overlap_area = overlap_x * overlap_y
                
                area1 = w1 * h1
                area2 = w2 * h2
                
                if overlap_area > overlap_threshold * min(area1, area2):
                    overlaps = True
                    break
            
            if not overlaps:
                filtered_matches.append(match)
        
        return filtered_matches
    
    def extract_text(self, screenshot: np.ndarray, region: Optional[Tuple[int, int, int, int]] = None) -> str:
        """
        Extract text from screenshot using OCR
        
        Args:
            screenshot: Screenshot to extract text from
            region: Optional region (x, y, width, height) to extract text from
            
        Returns:
            str: Extracted text
        """
        try:
            # Check if pytesseract is available
            try:
                pytesseract.get_tesseract_version()
            except Exception:
                # Tesseract not available, return empty string silently
                return ""

            if region:
                x, y, w, h = region
                roi = screenshot[y:y+h, x:x+w]
            else:
                roi = screenshot

            # Convert to PIL Image for pytesseract
            pil_image = Image.fromarray(cv2.cvtColor(roi, cv2.COLOR_BGR2RGB))

            # Extract text
            text = pytesseract.image_to_string(pil_image, config='--psm 6')

            return text.strip()

        except Exception as e:
            self.logger.debug(f"Error extracting text: {str(e)}")
            return ""
    
    def scan_screen_cache_optimized(self, required_templates: List[str] = None, region: Tuple[int, int, int, int] = None, template_thresholds: Dict[str, float] = None, enhanced_detection: bool = False, debug_screenshots: bool = False) -> Dict[str, Any]:
        """
        REVOLUTIONARY cache-based scanning with enhanced multi-method detection!

        1. Take ONE screenshot to memory cache
        2. Analyze once for all required templates
        3. Use multiple detection methods with fallback priority
        4. Return true/false for each template with confidence scores
        5. No disk I/O, pure memory operations

        Args:
            required_templates: List of template names to scan for
            region: Tuple of (left, top, width, height) for specific screen region
            template_thresholds: Custom thresholds per template
            enhanced_detection: Enable multi-method detection (template + color + OCR)
            debug_screenshots: Save debug screenshots for troubleshooting

        Returns:
            Dict with template detection results (true/false for each)
        """
        # STEP 1: ONE screenshot to memory cache only
        screenshot = self.capture_screen_to_memory(region=region)
        if screenshot is None:
            return {'error': 'Failed to capture screen to cache'}

        # STEP 2: Initialize results with enhanced structure
        scan_results = {
            'timestamp': time.time(),
            'screenshot': None,  # Don't store screenshot - memory optimization
            'templates_detected': {},  # True/False for each template
            'templates_found': {},     # Legacy compatibility
            'text_regions': {},
            'screen_size': screenshot.shape[:2],
            'detection_details': {},   # Enhanced: confidence scores and method used
            'debug_info': {}          # Enhanced: debugging information
        }

        # STEP 3: Only analyze templates that are actually needed
        templates_to_scan = required_templates if required_templates else []

        if not templates_to_scan:
            # No templates needed - ultra-fast path
            self.logger.debug("[CACHE] Ultra-fast path: No templates to scan")
            return scan_results

        # STEP 4: Save debug screenshot if requested
        if debug_screenshots:
            debug_filename = f"debug_screenshot_{int(time.time())}.png"
            debug_path = os.path.join("debug_screenshots", debug_filename)
            os.makedirs("debug_screenshots", exist_ok=True)
            cv2.imwrite(debug_path, screenshot)
            scan_results['debug_info']['screenshot_path'] = debug_path
            self.logger.debug(f"[DEBUG] Saved debug screenshot: {debug_path}")

        # STEP 5: Analyze cached screenshot for all required templates
        self.logger.debug(f"[CACHE] Cache analysis: Scanning {len(templates_to_scan)} templates (enhanced={enhanced_detection})")

        for template_name in templates_to_scan:
            if template_name in self.templates:
                # Get threshold for this template
                threshold = template_thresholds.get(template_name, 0.8) if template_thresholds else 0.8

                if enhanced_detection:
                    # Use enhanced multi-method detection
                    detection_result = self._enhanced_template_detection(screenshot, template_name, threshold, debug_screenshots)
                    detected = detection_result['detected']
                    scan_results['detection_details'][template_name] = detection_result
                else:
                    # Use standard template matching
                    matches = self.find_template(screenshot, template_name, threshold)
                    detected = bool(matches and len(matches) > 0)
                    scan_results['detection_details'][template_name] = {
                        'detected': detected,
                        'method': 'template_matching',
                        'confidence': 0.0,
                        'matches': matches
                    }

                # Store detection results
                scan_results['templates_detected'][template_name] = detected

                # Legacy compatibility
                if detected:
                    matches = scan_results['detection_details'][template_name].get('matches', [])
                    scan_results['templates_found'][template_name] = matches
                    scan_results['templates_found'][template_name + '_detected'] = True
                else:
                    scan_results['templates_found'][template_name + '_detected'] = False

                detection_method = scan_results['detection_details'][template_name]['method']
                confidence = scan_results['detection_details'][template_name]['confidence']
                self.logger.debug(f"[TEMPLATE] {template_name}: {'DETECTED' if detected else 'NOT FOUND'} (method: {detection_method}, confidence: {confidence:.3f})")
            else:
                # Template not loaded
                scan_results['templates_detected'][template_name] = False
                scan_results['templates_found'][template_name + '_detected'] = False
                scan_results['detection_details'][template_name] = {
                    'detected': False,
                    'method': 'template_not_loaded',
                    'confidence': 0.0,
                    'error': f'Template {template_name} not loaded'
                }
                self.logger.warning(f"[WARNING] Template {template_name} not loaded")

        return scan_results

    def _enhanced_template_detection(self, screenshot: np.ndarray, template_name: str, threshold: float, debug_screenshots: bool = False) -> Dict[str, Any]:
        """
        Enhanced multi-method template detection with fallback priority:
        1. Template matching (primary)
        2. Color-based detection (secondary)
        3. OCR text detection (tertiary)

        Args:
            screenshot: Screenshot to analyze
            template_name: Name of template to detect
            threshold: Template matching threshold
            debug_screenshots: Save debug images for troubleshooting

        Returns:
            Dict with detection results, method used, and confidence score
        """
        detection_result = {
            'detected': False,
            'method': 'none',
            'confidence': 0.0,
            'matches': [],
            'debug_info': {}
        }

        # Load enhanced detection config for this template
        enhanced_config = self._get_enhanced_detection_config(template_name)

        # METHOD 1: Template Matching (Primary)
        try:
            matches = self.find_template(screenshot, template_name, threshold)
            if matches and len(matches) > 0:
                # Calculate confidence based on template matching result
                template = self.templates[template_name]
                result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
                max_confidence = np.max(result)

                detection_result.update({
                    'detected': True,
                    'method': 'template_matching',
                    'confidence': float(max_confidence),
                    'matches': matches
                })

                self.logger.debug(f"[ENHANCED] {template_name}: Template matching SUCCESS (confidence: {max_confidence:.3f})")
                return detection_result
        except Exception as e:
            self.logger.error(f"[ENHANCED] Template matching failed for {template_name}: {str(e)}")

        # METHOD 2: Color-based Detection (Secondary)
        if enhanced_config.get('color_detection'):
            try:
                color_detected, color_confidence = self._detect_by_color(screenshot, template_name, enhanced_config['color_detection'])
                if color_detected:
                    detection_result.update({
                        'detected': True,
                        'method': 'color_detection',
                        'confidence': color_confidence,
                        'matches': []  # Color detection doesn't provide exact coordinates
                    })

                    self.logger.debug(f"[ENHANCED] {template_name}: Color detection SUCCESS (confidence: {color_confidence:.3f})")
                    return detection_result
            except Exception as e:
                self.logger.error(f"[ENHANCED] Color detection failed for {template_name}: {str(e)}")

        # METHOD 3: OCR Text Detection (Tertiary)
        if enhanced_config.get('ocr_detection'):
            try:
                ocr_detected, ocr_confidence = self._detect_by_ocr(screenshot, template_name, enhanced_config['ocr_detection'])
                if ocr_detected:
                    detection_result.update({
                        'detected': True,
                        'method': 'ocr_detection',
                        'confidence': ocr_confidence,
                        'matches': []  # OCR detection doesn't provide exact coordinates
                    })

                    self.logger.debug(f"[ENHANCED] {template_name}: OCR detection SUCCESS (confidence: {ocr_confidence:.3f})")
                    return detection_result
            except Exception as e:
                self.logger.error(f"[ENHANCED] OCR detection failed for {template_name}: {str(e)}")

        # All methods failed
        self.logger.debug(f"[ENHANCED] {template_name}: All detection methods FAILED")

        # Create debug visualization if requested
        if debug_screenshots:
            try:
                debug_vis_filename = f"debug_enhanced_{template_name}_{int(time.time())}.png"
                debug_vis_path = os.path.join("debug_screenshots", debug_vis_filename)

                # Create comprehensive debug visualization
                debug_vis = self.create_enhanced_debug_visualization(screenshot, template_name, detection_result, debug_vis_path)

                # Also create individual heatmap
                heatmap_filename = f"debug_heatmap_{template_name}_{int(time.time())}.png"
                heatmap_path = os.path.join("debug_screenshots", heatmap_filename)
                self.create_detection_heatmap(screenshot, template_name, threshold, heatmap_path)

                detection_result['debug_info'] = {
                    'debug_visualization_path': debug_vis_path,
                    'heatmap_path': heatmap_path
                }

                self.logger.debug(f"[ENHANCED] Created debug visualizations for {template_name}")

            except Exception as e:
                self.logger.error(f"[ENHANCED] Error creating debug visualizations: {str(e)}")

        return detection_result

    def _get_enhanced_detection_config(self, template_name: str) -> Dict[str, Any]:
        """
        Get enhanced detection configuration for a specific template from unified config.

        Args:
            template_name: Name of template

        Returns:
            Dict with enhanced detection settings
        """
        try:
            # Load unified configuration
            import json
            config_path = "unified_config.json"

            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)

                # Get enhanced detection config for this template
                enhanced_detection = config.get('enhanced_detection', {})
                template_configs = enhanced_detection.get('template_configs', {})
                template_config = template_configs.get(template_name, {})

                if template_config.get('enhanced_enabled', False):
                    # Convert unified config format to internal format
                    converted_config = {}

                    # Color detection configuration
                    if template_config.get('color_detection', {}).get('enabled', False):
                        color_config = template_config['color_detection']
                        converted_config['color_detection'] = {
                            'enabled': True,
                            'color_ranges': color_config.get('color_ranges', []),
                            'min_area': color_config.get('min_area', 500),
                            'confidence_threshold': color_config.get('confidence_threshold', 0.7)
                        }

                    # OCR detection configuration
                    if template_config.get('ocr_detection', {}).get('enabled', False):
                        ocr_config = template_config['ocr_detection']
                        converted_config['ocr_detection'] = {
                            'enabled': True,
                            'text_patterns': ocr_config.get('text_patterns', []),
                            'confidence_threshold': ocr_config.get('confidence_threshold', 0.8),
                            'preprocessing': ocr_config.get('preprocessing', [])
                        }

                    self.logger.debug(f"[CONFIG] Loaded enhanced detection config for {template_name}")
                    return converted_config
                else:
                    self.logger.debug(f"[CONFIG] Enhanced detection disabled for {template_name}")

            else:
                self.logger.warning(f"[CONFIG] Unified config file not found: {config_path}")

        except Exception as e:
            self.logger.error(f"[CONFIG] Error loading enhanced detection config for {template_name}: {str(e)}")

        # Fallback to default configurations for ESC recovery templates
        default_configs = {
            'events_button': {
                'color_detection': {
                    'enabled': True,
                    'color_ranges': [
                        {'lower': [100, 50, 50], 'upper': [130, 255, 255], 'color_space': 'HSV'},
                        {'lower': [50, 100, 150], 'upper': [150, 200, 255], 'color_space': 'RGB'}
                    ],
                    'min_area': 500,
                    'confidence_threshold': 0.7
                },
                'ocr_detection': {
                    'enabled': True,
                    'text_patterns': ['events', 'event', 'daily'],
                    'confidence_threshold': 0.8,
                    'preprocessing': ['invert', 'contrast']
                }
            },
            'quit_game_dialog': {
                'color_detection': {
                    'enabled': True,
                    'color_ranges': [
                        {'lower': [40, 40, 40], 'upper': [80, 80, 80], 'color_space': 'RGB'},
                        {'lower': [200, 200, 200], 'upper': [255, 255, 255], 'color_space': 'RGB'}
                    ],
                    'min_area': 1000,
                    'confidence_threshold': 0.6
                },
                'ocr_detection': {
                    'enabled': True,
                    'text_patterns': ['quit', 'exit', 'close', 'cancel', 'confirm'],
                    'confidence_threshold': 0.8,
                    'preprocessing': ['contrast', 'denoise']
                }
            }
        }

        self.logger.debug(f"[CONFIG] Using default enhanced detection config for {template_name}")
        return default_configs.get(template_name, {})

    def _detect_by_color(self, screenshot: np.ndarray, template_name: str, color_config: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Detect template using color-based analysis.

        Args:
            screenshot: Screenshot to analyze
            template_name: Name of template
            color_config: Color detection configuration

        Returns:
            Tuple of (detected: bool, confidence: float)
        """
        if not color_config.get('enabled', False):
            return False, 0.0

        try:
            total_area = 0
            max_confidence = 0.0

            for color_range in color_config.get('color_ranges', []):
                color_space = color_range.get('color_space', 'RGB')
                lower = np.array(color_range['lower'])
                upper = np.array(color_range['upper'])

                # Convert color space if needed
                if color_space == 'HSV':
                    image = cv2.cvtColor(screenshot, cv2.COLOR_BGR2HSV)
                else:
                    image = screenshot

                # Create color mask
                mask = cv2.inRange(image, lower, upper)

                # Find contours
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                # Calculate total area of detected regions
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area >= color_config.get('min_area', 100):
                        total_area += area

            # Calculate confidence based on detected area
            screen_area = screenshot.shape[0] * screenshot.shape[1]
            area_ratio = total_area / screen_area
            confidence = min(area_ratio * 10, 1.0)  # Scale area ratio to confidence

            detected = confidence >= color_config.get('confidence_threshold', 0.5)

            return detected, confidence

        except Exception as e:
            self.logger.error(f"[COLOR] Color detection error for {template_name}: {str(e)}")
            return False, 0.0

    def _detect_by_ocr(self, screenshot: np.ndarray, template_name: str, ocr_config: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Detect template using OCR text analysis.

        Args:
            screenshot: Screenshot to analyze
            template_name: Name of template
            ocr_config: OCR detection configuration

        Returns:
            Tuple of (detected: bool, confidence: float)
        """
        if not ocr_config.get('enabled', False):
            return False, 0.0

        try:
            # Import OCR libraries (optional dependencies)
            try:
                import pytesseract
                import easyocr
            except ImportError:
                self.logger.warning(f"[OCR] OCR libraries not available for {template_name}")
                return False, 0.0

            # Preprocess image for OCR
            processed_image = self._preprocess_for_ocr(screenshot, ocr_config.get('preprocessing', []))

            # Try multiple OCR methods
            detected_texts = []

            # Method 1: Tesseract OCR
            try:
                tesseract_text = pytesseract.image_to_string(processed_image, config='--psm 6').lower().strip()
                if tesseract_text:
                    detected_texts.append(tesseract_text)
            except Exception as e:
                self.logger.debug(f"[OCR] Tesseract failed for {template_name}: {str(e)}")

            # Method 2: EasyOCR (fallback)
            try:
                reader = easyocr.Reader(['en'])
                results = reader.readtext(processed_image)
                for (bbox, text, confidence) in results:
                    if confidence > 0.5:  # Only use high-confidence results
                        detected_texts.append(text.lower().strip())
            except Exception as e:
                self.logger.debug(f"[OCR] EasyOCR failed for {template_name}: {str(e)}")

            # Check for text patterns
            text_patterns = ocr_config.get('text_patterns', [])
            max_confidence = 0.0

            for detected_text in detected_texts:
                for pattern in text_patterns:
                    if pattern.lower() in detected_text:
                        # Calculate confidence based on text match quality
                        match_ratio = len(pattern) / max(len(detected_text), 1)
                        confidence = min(match_ratio * 1.2, 1.0)  # Boost confidence for exact matches
                        max_confidence = max(max_confidence, confidence)

            detected = max_confidence >= ocr_config.get('confidence_threshold', 0.8)

            return detected, max_confidence

        except Exception as e:
            self.logger.error(f"[OCR] OCR detection error for {template_name}: {str(e)}")
            return False, 0.0

    def _preprocess_for_ocr(self, image: np.ndarray, preprocessing_steps: List[str]) -> np.ndarray:
        """
        Preprocess image for better OCR results.

        Args:
            image: Input image
            preprocessing_steps: List of preprocessing steps to apply

        Returns:
            Preprocessed image
        """
        processed = image.copy()

        try:
            for step in preprocessing_steps:
                if step == 'invert':
                    processed = cv2.bitwise_not(processed)
                elif step == 'contrast':
                    processed = cv2.convertScaleAbs(processed, alpha=1.5, beta=0)
                elif step == 'denoise':
                    processed = cv2.fastNlMeansDenoising(processed)
                elif step == 'grayscale':
                    if len(processed.shape) == 3:
                        processed = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)
                elif step == 'threshold':
                    if len(processed.shape) == 3:
                        processed = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)
                    _, processed = cv2.threshold(processed, 127, 255, cv2.THRESH_BINARY)

        except Exception as e:
            self.logger.error(f"[OCR] Preprocessing error: {str(e)}")

        return processed

    def create_detection_heatmap(self, screenshot: np.ndarray, template_name: str, threshold: float = 0.8, save_path: str = None) -> np.ndarray:
        """
        Create a visual heatmap showing template matching results.

        Args:
            screenshot: Screenshot to analyze
            template_name: Name of template to match
            threshold: Matching threshold
            save_path: Optional path to save heatmap image

        Returns:
            Heatmap visualization as numpy array
        """
        try:
            if template_name not in self.templates:
                self.logger.warning(f"[HEATMAP] Template {template_name} not loaded")
                return screenshot

            template = self.templates[template_name]

            # Perform template matching
            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)

            # Normalize result to 0-255 range for visualization
            heatmap = cv2.normalize(result, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)

            # Apply colormap for better visualization
            heatmap_colored = cv2.applyColorMap(heatmap, cv2.COLORMAP_JET)

            # Resize heatmap to match screenshot dimensions
            h, w = screenshot.shape[:2]
            heatmap_resized = cv2.resize(heatmap_colored, (w, h))

            # Blend heatmap with original screenshot
            alpha = 0.6  # Transparency for heatmap overlay
            blended = cv2.addWeighted(screenshot, 1-alpha, heatmap_resized, alpha, 0)

            # Draw threshold line and detection boxes
            locations = np.where(result >= threshold)
            template_h, template_w = template.shape[:2]

            for pt in zip(*locations[::-1]):
                # Draw detection rectangle
                cv2.rectangle(blended, pt, (pt[0] + template_w, pt[1] + template_h), (0, 255, 0), 2)

                # Add confidence score text
                confidence = result[pt[1], pt[0]]
                cv2.putText(blended, f'{confidence:.3f}', (pt[0], pt[1]-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

            # Add template info text
            info_text = f'Template: {template_name} | Threshold: {threshold:.3f}'
            cv2.putText(blended, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # Save heatmap if path provided
            if save_path:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                cv2.imwrite(save_path, blended)
                self.logger.debug(f"[HEATMAP] Saved heatmap: {save_path}")

            return blended

        except Exception as e:
            self.logger.error(f"[HEATMAP] Error creating heatmap for {template_name}: {str(e)}")
            return screenshot

    def create_enhanced_debug_visualization(self, screenshot: np.ndarray, template_name: str, detection_result: Dict[str, Any], save_path: str = None) -> np.ndarray:
        """
        Create comprehensive debug visualization showing all detection methods.

        Args:
            screenshot: Screenshot to analyze
            template_name: Name of template
            detection_result: Result from enhanced detection
            save_path: Optional path to save visualization

        Returns:
            Debug visualization as numpy array
        """
        try:
            # Create a larger canvas for multiple visualizations
            h, w = screenshot.shape[:2]
            canvas_height = h * 2
            canvas_width = w * 2
            canvas = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)

            # Top-left: Original screenshot
            canvas[0:h, 0:w] = screenshot
            cv2.putText(canvas, 'Original Screenshot', (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # Top-right: Template matching heatmap
            if template_name in self.templates:
                heatmap = self.create_detection_heatmap(screenshot, template_name, 0.8)
                canvas[0:h, w:w*2] = heatmap
                cv2.putText(canvas, 'Template Matching Heatmap', (w+10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # Bottom-left: Color detection visualization
            color_vis = self._create_color_detection_visualization(screenshot, template_name)
            canvas[h:h*2, 0:w] = color_vis
            cv2.putText(canvas, 'Color Detection', (10, h+25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # Bottom-right: Detection summary
            summary_vis = self._create_detection_summary_visualization(screenshot, template_name, detection_result)
            canvas[h:h*2, w:w*2] = summary_vis
            cv2.putText(canvas, 'Detection Summary', (w+10, h+25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # Save visualization if path provided
            if save_path:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                cv2.imwrite(save_path, canvas)
                self.logger.debug(f"[DEBUG_VIS] Saved debug visualization: {save_path}")

            return canvas

        except Exception as e:
            self.logger.error(f"[DEBUG_VIS] Error creating debug visualization for {template_name}: {str(e)}")
            return screenshot

    def _create_color_detection_visualization(self, screenshot: np.ndarray, template_name: str) -> np.ndarray:
        """Create visualization for color detection results."""
        try:
            enhanced_config = self._get_enhanced_detection_config(template_name)
            color_config = enhanced_config.get('color_detection', {})

            if not color_config.get('enabled', False):
                # Return grayscale version if color detection disabled
                gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
                return cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)

            # Create combined mask for all color ranges
            combined_mask = np.zeros(screenshot.shape[:2], dtype=np.uint8)

            for color_range in color_config.get('color_ranges', []):
                color_space = color_range.get('color_space', 'RGB')
                lower = np.array(color_range['lower'])
                upper = np.array(color_range['upper'])

                # Convert color space if needed
                if color_space == 'HSV':
                    image = cv2.cvtColor(screenshot, cv2.COLOR_BGR2HSV)
                else:
                    image = screenshot

                # Create color mask
                mask = cv2.inRange(image, lower, upper)
                combined_mask = cv2.bitwise_or(combined_mask, mask)

            # Create colored visualization
            color_vis = screenshot.copy()
            color_vis[combined_mask > 0] = [0, 255, 0]  # Highlight detected areas in green

            # Blend with original
            result = cv2.addWeighted(screenshot, 0.7, color_vis, 0.3, 0)

            return result

        except Exception as e:
            self.logger.error(f"[COLOR_VIS] Error creating color visualization: {str(e)}")
            return screenshot

    def _create_detection_summary_visualization(self, screenshot: np.ndarray, template_name: str, detection_result: Dict[str, Any]) -> np.ndarray:
        """Create summary visualization with detection results."""
        try:
            # Create black background for text
            h, w = screenshot.shape[:2]
            summary = np.zeros((h, w, 3), dtype=np.uint8)

            # Add title
            cv2.putText(summary, f'Detection Results: {template_name}', (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            y_offset = 60
            line_height = 25

            # Detection status
            detected = detection_result.get('detected', False)
            status_color = (0, 255, 0) if detected else (0, 0, 255)
            cv2.putText(summary, f'Detected: {detected}', (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, status_color, 1)
            y_offset += line_height

            # Method used
            method = detection_result.get('method', 'unknown')
            cv2.putText(summary, f'Method: {method}', (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            y_offset += line_height

            # Confidence score
            confidence = detection_result.get('confidence', 0.0)
            cv2.putText(summary, f'Confidence: {confidence:.3f}', (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            y_offset += line_height * 2

            # Enhanced detection config info
            enhanced_config = self._get_enhanced_detection_config(template_name)

            cv2.putText(summary, 'Enhanced Detection Config:', (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
            y_offset += line_height

            # Color detection status
            color_enabled = enhanced_config.get('color_detection', {}).get('enabled', False)
            color_color = (0, 255, 0) if color_enabled else (128, 128, 128)
            cv2.putText(summary, f'Color Detection: {color_enabled}', (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color_color, 1)
            y_offset += line_height

            # OCR detection status
            ocr_enabled = enhanced_config.get('ocr_detection', {}).get('enabled', False)
            ocr_color = (0, 255, 0) if ocr_enabled else (128, 128, 128)
            cv2.putText(summary, f'OCR Detection: {ocr_enabled}', (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, ocr_color, 1)
            y_offset += line_height * 2

            # Timestamp
            timestamp = time.strftime('%H:%M:%S')
            cv2.putText(summary, f'Generated: {timestamp}', (10, h-20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (128, 128, 128), 1)

            return summary

        except Exception as e:
            self.logger.error(f"[SUMMARY_VIS] Error creating summary visualization: {str(e)}")
            return np.zeros((screenshot.shape[0], screenshot.shape[1], 3), dtype=np.uint8)

    def scan_screen(self, required_templates: List[str] = None, region: Tuple[int, int, int, int] = None, template_thresholds: Dict[str, float] = None) -> Dict[str, Any]:
        """Legacy method - redirects to cache-optimized scanning"""
        return self.scan_screen_cache_optimized(required_templates, region=region, template_thresholds=template_thresholds)

    def cleanup_old_screenshots(self):
        """
        Clean up old screenshot files from disk (they shouldn't exist with cache system)
        """
        try:
            if os.path.exists(self.screenshots_path):
                screenshot_files = [f for f in os.listdir(self.screenshots_path) if f.startswith('screenshot_')]
                if screenshot_files:
                    self.logger.info(f"🧹 Cleaning up {len(screenshot_files)} old screenshot files")
                    for file in screenshot_files:
                        file_path = os.path.join(self.screenshots_path, file)
                        os.remove(file_path)
                    self.logger.info("✅ Screenshot cleanup completed")
                else:
                    self.logger.debug("✅ No old screenshots to clean up")
        except Exception as e:
            self.logger.error(f"Error cleaning up screenshots: {str(e)}")

    def scan_screen_fast(self) -> Dict[str, Any]:
        """
        Ultra-fast scan - minimal templates only
        """
        # Only scan for essential templates needed for basic navigation
        essential_templates = ['events_button', 'squad_4_4', 'Base']
        return self.scan_screen(essential_templates)
