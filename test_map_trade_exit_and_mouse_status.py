#!/usr/bin/env python3
"""
Test script for Map Trade Exit Sequence and Mouse Override Status
"""

import time
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_map_trade_exit_sequence():
    """Test the enhanced map trade exit sequence"""
    print("🗺️ MAP TRADE EXIT SEQUENCE TEST")
    print("=" * 40)
    
    try:
        # Import map trade module
        from modules.map_trade import MapTradeModule
        from unified_config_manager import UnifiedConfigManager
        
        print("✅ Map trade module imported successfully")
        
        # Create module instance
        config_manager = UnifiedConfigManager()
        config_data = config_manager.load_config()
        
        map_trade = MapTradeModule(config_data)
        print("✅ Map trade module initialized")
        
        # Test the enhanced exit sequence method
        print("\n🧪 Testing enhanced exit sequence logic...")
        print("This will test the 'ESC until quit dialog or main menu' functionality")
        
        response = input("Do you want to test the exit sequence? (y/n): ").lower().strip()
        if response == 'y':
            print("\n⚠️ IMPORTANT: Make sure the game is open and you're in a state where ESC is needed")
            print("The test will press ESC keys until it finds main menu or quit dialog")
            
            confirm = input("Ready to start ESC sequence test? (y/n): ").lower().strip()
            if confirm == 'y':
                print("\nStarting enhanced exit sequence in 3 seconds...")
                for i in range(3, 0, -1):
                    print(f"{i}...")
                    time.sleep(1)
                
                # Test the exit sequence
                result = map_trade._enhanced_exit_sequence()
                
                if result:
                    print("✅ Enhanced exit sequence completed successfully!")
                    print("   - Should have reached main menu or handled quit dialog")
                else:
                    print("❌ Enhanced exit sequence failed")
                    print("   - Check logs for details")
            else:
                print("⏭️ Skipping exit sequence test")
        else:
            print("⏭️ Skipping exit sequence test")
        
        return True
        
    except Exception as e:
        print(f"❌ Map trade exit test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mouse_override_gui_status():
    """Test mouse override status in GUI"""
    print("\n🖱️ MOUSE OVERRIDE GUI STATUS TEST")
    print("=" * 40)
    
    try:
        # Test mouse controller status
        from mouse_override_controller import get_mouse_controller
        from main_controller import MainController
        
        print("✅ Mouse controller imports successful")
        
        # Initialize main controller
        main_ctrl = MainController()
        print("✅ Main controller initialized")
        
        # Test status retrieval
        mouse_status = main_ctrl.get_mouse_override_status()
        print(f"✅ Initial mouse status: {mouse_status}")
        
        # Test disable/enable cycle with status monitoring
        print("\n🔄 Testing disable/enable cycle...")
        
        print("1. Disabling mouse override...")
        main_ctrl.mouse_controller.disable_mouse_override("GUI status test")
        time.sleep(0.5)
        
        status = main_ctrl.get_mouse_override_status()
        print(f"   Status after disable: enabled={status['enabled']}, active_overrides={status['active_overrides']}")
        
        if status['active_overrides'] > 0:
            print("   ✅ Disable detected correctly")
        else:
            print("   ❌ Disable not detected")
        
        print("2. Re-enabling with cooldown...")
        main_ctrl.mouse_controller.enable_mouse_override("GUI status test complete", cooldown=3.0)
        time.sleep(0.5)
        
        status = main_ctrl.get_mouse_override_status()
        print(f"   Status after enable: enabled={status['enabled']}, in_cooldown={status['in_cooldown']}")
        
        if status['in_cooldown']:
            print(f"   ✅ Cooldown detected correctly ({status['cooldown_remaining']:.1f}s remaining)")
        else:
            print("   ❌ Cooldown not detected")
        
        # Monitor cooldown countdown
        print("3. Monitoring cooldown countdown...")
        for i in range(4):
            status = main_ctrl.get_mouse_override_status()
            remaining = status.get('cooldown_remaining', 0)
            print(f"   Cooldown remaining: {remaining:.1f}s")
            time.sleep(1)
        
        print("✅ Mouse override status test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Mouse override GUI status test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_esc_sequence_protection():
    """Test ESC sequence mouse override protection"""
    print("\n🔑 ESC SEQUENCE PROTECTION TEST")
    print("=" * 40)
    
    try:
        from mouse_override_controller import get_mouse_controller, mouse_override_disabled
        
        controller = get_mouse_controller()
        print("✅ Mouse controller obtained")
        
        # Test context manager protection
        print("\n🛡️ Testing context manager protection...")
        
        print("Initial status:")
        status = controller.get_status()
        print(f"  enabled: {status['enabled']}, active_overrides: {status['active_overrides']}")
        
        print("Entering protected ESC context...")
        with mouse_override_disabled("ESC sequence test"):
            status = controller.get_status()
            print(f"  Inside context - enabled: {status['enabled']}, active_overrides: {status['active_overrides']}")
            
            if status['active_overrides'] > 0:
                print("  ✅ Mouse override correctly disabled in context")
            else:
                print("  ❌ Mouse override not disabled in context")
            
            # Simulate ESC key press (without actually pressing it)
            print("  Simulating ESC key press...")
            time.sleep(1)
        
        print("Exited protected ESC context...")
        time.sleep(0.5)  # Allow time for re-enable
        
        status = controller.get_status()
        print(f"  After context - enabled: {status['enabled']}, in_cooldown: {status.get('in_cooldown', False)}")
        
        if status.get('in_cooldown', False):
            print("  ✅ Mouse override correctly re-enabled with cooldown")
        else:
            print("  ⚠️ Mouse override re-enabled but no cooldown detected")
        
        print("✅ ESC sequence protection test completed!")
        return True
        
    except Exception as e:
        print(f"❌ ESC sequence protection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 MAP TRADE EXIT & MOUSE OVERRIDE STATUS TESTS")
    print("=" * 60)
    
    # Run tests
    test1_result = test_map_trade_exit_sequence()
    test2_result = test_mouse_override_gui_status()
    test3_result = test_esc_sequence_protection()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    if test1_result:
        print("✅ Map Trade Exit Sequence: PASSED")
        print("   - Enhanced exit sequence with proper ESC looping")
        print("   - ESC until quit dialog or main menu functionality")
    else:
        print("❌ Map Trade Exit Sequence: FAILED")
    
    if test2_result:
        print("✅ Mouse Override GUI Status: PASSED")
        print("   - Status updates working correctly")
        print("   - Disable/enable detection working")
        print("   - Cooldown monitoring working")
    else:
        print("❌ Mouse Override GUI Status: FAILED")
    
    if test3_result:
        print("✅ ESC Sequence Protection: PASSED")
        print("   - Context manager protection working")
        print("   - Automatic re-enable with cooldown")
    else:
        print("❌ ESC Sequence Protection: FAILED")
    
    print("\n" + "=" * 60)
    
    if test1_result and test2_result and test3_result:
        print("🎯 ALL TESTS PASSED!")
        print("\nYour fixes are working correctly:")
        print("✅ Map trade exit sequence now loops ESC until main menu/quit dialog")
        print("✅ Mouse override status updates properly in GUI")
        print("✅ ESC sequences are protected from mouse override false positives")
    else:
        print("⚠️ SOME TESTS FAILED - Check error messages above")
        print("\nIf GUI status is still not updating:")
        print("1. Check GUI logs for mouse override debug messages")
        print("2. Verify controller initialization in GUI")
        print("3. Check status update thread is running")
    
    print("\nPress Enter to exit...")
    input()
