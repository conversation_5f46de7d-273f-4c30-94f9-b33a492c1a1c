#!/usr/bin/env python3
"""
Comprehensive diagnostic script to identify and fix OCR accuracy discrepancy
between Config Helper's Fast Map OCR testing and live Map Trade module execution
"""

import os
import json
import time
import cv2
import numpy as np
import pyautogui
from typing import Dict, Optional, <PERSON><PERSON>

def diagnose_ocr_systems():
    """Comprehensive diagnosis of OCR accuracy discrepancy"""
    print("🔍 OCR ACCURACY DISCREPANCY DIAGNOSIS")
    print("=" * 60)
    
    # Step 1: Verify Good_setting_map.json coordinates
    print("\n1️⃣ VERIFYING GOOD_SETTING_MAP COORDINATES")
    print("-" * 40)
    
    good_setting_path = os.path.join("templates", "Good_setting_map.json")
    if os.path.exists(good_setting_path):
        with open(good_setting_path, 'r') as f:
            good_coordinates = json.load(f)
        
        print("✅ Good_setting_map.json found with coordinates:")
        for piece, coords in good_coordinates.items():
            print(f"   {piece}: ({coords[0]}, {coords[1]})")
    else:
        print("❌ Good_setting_map.json NOT found!")
        return False
    
    # Step 2: Test Fast Map OCR system availability
    print("\n2️⃣ TESTING FAST MAP OCR SYSTEM")
    print("-" * 40)
    
    try:
        from fast_map_ocr import get_fast_map_ocr
        fast_ocr = get_fast_map_ocr()
        print("✅ Fast Map OCR system imported successfully")
        
        # Check configuration
        config = fast_ocr.config
        print(f"   Capture size: {config['capture_size']}px")
        print(f"   Scale factor: {config['scale_factor']}x")
        print(f"   OCR configs: {len(fast_ocr.ocr_configs)} configurations")
        print(f"   Validation: {'Enabled' if config['validation_enabled'] else 'Disabled'}")
        
    except Exception as e:
        print(f"❌ Fast Map OCR system failed: {e}")
        return False
    
    # Step 3: Test Map Trade module initialization
    print("\n3️⃣ TESTING MAP TRADE MODULE INITIALIZATION")
    print("-" * 40)
    
    try:
        from modules.map_trade import MapTradeModule
        map_trade = MapTradeModule()
        print("✅ Map Trade module initialized successfully")
        
        # Check if Good_setting_map coordinates were loaded
        print("   Checking coordinate loading...")
        for piece_name in ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']:
            if piece_name in map_trade.map_piece_regions:
                big_map_pos = map_trade.map_piece_regions[piece_name]['big_map_pos']
                expected_pos = good_coordinates[piece_name]
                
                if big_map_pos == (expected_pos[0], expected_pos[1]):
                    print(f"   ✅ {piece_name}: {big_map_pos} (matches Good_setting_map)")
                else:
                    print(f"   ❌ {piece_name}: {big_map_pos} (expected {expected_pos})")
        
        # Check Fast Map OCR availability
        fast_ocr_available = getattr(map_trade, '_fast_ocr_available', False)
        print(f"   Fast OCR available: {'✅ Yes' if fast_ocr_available else '❌ No'}")
        
    except Exception as e:
        print(f"❌ Map Trade module initialization failed: {e}")
        return False
    
    # Step 4: Compare OCR processing methods
    print("\n4️⃣ COMPARING OCR PROCESSING METHODS")
    print("-" * 40)
    
    # Test with a sample coordinate (M1)
    test_coord = good_coordinates['M1']
    print(f"Testing with M1 coordinate: {test_coord}")
    
    try:
        # Capture image using Map Trade method (35x35)
        print("   📸 Capturing with Map Trade method (35x35)...")
        map_trade_image = map_trade._capture_number_region(test_coord[0], test_coord[1], 35)
        print(f"   Map Trade capture: {map_trade_image.shape}")
        
        # Save debug images
        cv2.imwrite("debug_map_trade_capture.png", map_trade_image)
        print("   💾 Saved: debug_map_trade_capture.png")
        
        # Test Config Helper method (simulate 35x35 capture)
        print("   📸 Capturing with Config Helper method (35x35)...")
        x = test_coord[0] - 17  # 35//2
        y = test_coord[1] - 17
        screenshot = pyautogui.screenshot(region=(x, y, 35, 35))
        config_helper_image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        print(f"   Config Helper capture: {config_helper_image.shape}")
        
        # Save debug images
        cv2.imwrite("debug_config_helper_capture.png", config_helper_image)
        print("   💾 Saved: debug_config_helper_capture.png")
        
        # Compare images
        if np.array_equal(map_trade_image, config_helper_image):
            print("   ✅ Images are identical")
        else:
            print("   ⚠️ Images differ - this may explain accuracy difference")
            
    except Exception as e:
        print(f"   ❌ Image capture comparison failed: {e}")
    
    # Step 5: Test OCR methods with same image
    print("\n5️⃣ TESTING OCR METHODS WITH SAME IMAGE")
    print("-" * 40)
    
    try:
        # Test Fast Map OCR directly
        print("   🧠 Testing Fast Map OCR directly...")
        fast_ocr.set_debug_mode(True)
        fast_result = fast_ocr.recognize_map_piece(map_trade_image, "M1_TEST")
        print(f"   Fast OCR result: {fast_result}")
        
        # Test Map Trade OCR method
        print("   🧠 Testing Map Trade OCR method...")
        map_trade._current_piece_id = "M1_TEST"
        map_trade_result = map_trade._extract_number_from_image(map_trade_image)
        print(f"   Map Trade OCR result: {map_trade_result}")
        
        # Test traditional OCR fallback
        print("   🧠 Testing traditional OCR fallback...")
        traditional_result = map_trade._extract_number_traditional_ocr(map_trade_image)
        print(f"   Traditional OCR result: {traditional_result}")
        
        # Compare results
        if fast_result == map_trade_result:
            print("   ✅ Fast OCR and Map Trade results match")
        else:
            print(f"   ⚠️ Results differ: Fast={fast_result}, Map Trade={map_trade_result}")
            
    except Exception as e:
        print(f"   ❌ OCR method testing failed: {e}")
    
    # Step 6: Check OCR configuration differences
    print("\n6️⃣ CHECKING OCR CONFIGURATION DIFFERENCES")
    print("-" * 40)
    
    try:
        # Check Fast Map OCR configuration in Map Trade
        if hasattr(map_trade, 'fast_map_ocr'):
            map_trade_fast_config = map_trade.fast_map_ocr.config
            print("   Map Trade Fast OCR config:")
            print(f"     Debug mode: {map_trade_fast_config['debug_mode']}")
            print(f"     Validation: {map_trade_fast_config['validation_enabled']}")
            print(f"     Scale factor: {map_trade_fast_config['scale_factor']}")
            
            # Check if Config Helper uses different settings
            print("   Config Helper typically uses:")
            print("     Debug mode: True (for testing)")
            print("     Validation: Variable (checkbox controlled)")
            print("     Scale factor: 2 (same)")
            
            if not map_trade_fast_config['debug_mode']:
                print("   ⚠️ Map Trade has debug mode disabled - may affect accuracy")
                
        else:
            print("   ❌ Map Trade doesn't have fast_map_ocr attribute")
            
    except Exception as e:
        print(f"   ❌ Configuration check failed: {e}")
    
    print("\n" + "=" * 60)
    print("📊 DIAGNOSIS SUMMARY")
    print("=" * 60)
    
    return True

def fix_ocr_accuracy_issues():
    """Apply fixes for identified OCR accuracy issues"""
    print("\n🔧 APPLYING OCR ACCURACY FIXES")
    print("=" * 40)
    
    try:
        from modules.map_trade import MapTradeModule
        
        # Create a test instance to verify fixes
        print("Creating Map Trade instance for testing...")
        map_trade = MapTradeModule()
        
        # Fix 1: Ensure Fast Map OCR debug mode is enabled for better accuracy
        if hasattr(map_trade, 'fast_map_ocr'):
            print("✅ Enabling Fast Map OCR debug mode for better accuracy...")
            map_trade.fast_map_ocr.set_debug_mode(True)
            map_trade.fast_map_ocr.set_validation(True)
        
        # Fix 2: Verify coordinate loading
        good_setting_path = os.path.join("templates", "Good_setting_map.json")
        if os.path.exists(good_setting_path):
            with open(good_setting_path, 'r') as f:
                good_coordinates = json.load(f)
            
            print("✅ Verifying Good_setting_map coordinate integration...")
            for piece_name, expected_coords in good_coordinates.items():
                if piece_name in map_trade.map_piece_regions:
                    current_coords = map_trade.map_piece_regions[piece_name]['big_map_pos']
                    if current_coords != (expected_coords[0], expected_coords[1]):
                        print(f"⚠️ Fixing {piece_name} coordinates: {current_coords} → {expected_coords}")
                        map_trade.map_piece_regions[piece_name]['big_map_pos'] = (expected_coords[0], expected_coords[1])
        
        print("✅ OCR accuracy fixes applied successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Failed to apply fixes: {e}")
        return False

if __name__ == "__main__":
    print("🚀 OCR ACCURACY DIAGNOSTIC TOOL")
    print("Investigating discrepancy between Config Helper and Map Trade OCR accuracy")
    print("=" * 80)
    
    # Run comprehensive diagnosis
    diagnosis_success = diagnose_ocr_systems()
    
    if diagnosis_success:
        # Apply fixes
        fix_success = fix_ocr_accuracy_issues()
        
        if fix_success:
            print("\n🎉 DIAGNOSIS AND FIXES COMPLETED!")
            print("The OCR accuracy discrepancy should now be resolved.")
            print("\nNext steps:")
            print("1. Test the Map Trade module with live automation")
            print("2. Compare results with Config Helper testing")
            print("3. Monitor OCR accuracy during actual trades")
        else:
            print("\n⚠️ DIAGNOSIS COMPLETED BUT FIXES FAILED")
            print("Manual intervention may be required.")
    else:
        print("\n❌ DIAGNOSIS FAILED")
        print("Please check the error messages above and resolve issues manually.")
