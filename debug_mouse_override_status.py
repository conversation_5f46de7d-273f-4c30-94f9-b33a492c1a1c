#!/usr/bin/env python3
"""
Debug script to test mouse override controller status
"""

import time
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mouse_override_status():
    """Test mouse override controller status functionality"""
    print("🔍 MOUSE OVERRIDE STATUS DEBUG TEST")
    print("=" * 50)
    
    try:
        # Test 1: Import mouse controller
        print("\n1. Testing mouse controller import...")
        from mouse_override_controller import get_mouse_controller, initialize_mouse_controller
        print("✅ Mouse controller imports successful")
        
        # Test 2: Initialize controller
        print("\n2. Testing controller initialization...")
        controller = get_mouse_controller()
        if controller is None:
            print("⚠️ Controller is None, initializing...")
            # Create a mock main controller for testing
            class MockMainController:
                def __init__(self):
                    self.logger = None
            
            mock_main = MockMainController()
            controller = initialize_mouse_controller(mock_main)
        
        print(f"✅ Controller initialized: {type(controller)}")
        
        # Test 3: Get status
        print("\n3. Testing status retrieval...")
        status = controller.get_status()
        print(f"✅ Status retrieved: {status}")
        
        # Test 4: Test disable/enable cycle
        print("\n4. Testing disable/enable cycle...")
        
        print("Initial status:")
        status = controller.get_status()
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        print("\nDisabling mouse override...")
        controller.disable_mouse_override("Debug test")
        time.sleep(0.5)
        
        status = controller.get_status()
        print("Status after disable:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        print("\nRe-enabling mouse override...")
        controller.enable_mouse_override("Debug test complete", cooldown=2.0)
        time.sleep(0.5)
        
        status = controller.get_status()
        print("Status after enable:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        # Test 5: Test main controller integration
        print("\n5. Testing main controller integration...")
        try:
            from main_controller import MainController
            main_ctrl = MainController()
            
            print("✅ Main controller created")
            
            # Test get_mouse_override_status method
            mouse_status = main_ctrl.get_mouse_override_status()
            print(f"✅ Main controller mouse status: {mouse_status}")
            
            # Test disable/enable through main controller
            print("\nTesting through main controller...")
            main_ctrl.mouse_controller.disable_mouse_override("Main controller test")
            time.sleep(0.5)
            
            mouse_status = main_ctrl.get_mouse_override_status()
            print(f"Status after main controller disable: {mouse_status}")
            
            main_ctrl.mouse_controller.enable_mouse_override("Main controller test complete")
            time.sleep(0.5)
            
            mouse_status = main_ctrl.get_mouse_override_status()
            print(f"Status after main controller enable: {mouse_status}")
            
        except Exception as e:
            print(f"❌ Main controller test failed: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n✅ All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """Test GUI integration with mouse override status"""
    print("\n🖥️ GUI INTEGRATION TEST")
    print("=" * 30)
    
    try:
        # Test GUI status update method
        print("Testing GUI status update logic...")
        
        # Mock controller with mouse override status
        class MockController:
            def __init__(self):
                self.mouse_controller = get_mouse_controller()
            
            def get_mouse_override_status(self):
                return self.mouse_controller.get_status()
            
            def get_status(self):
                return {
                    'running': True,
                    'uptime': 100,
                    'total_scans': 50,
                    'total_executions': 10
                }
        
        mock_controller = MockController()
        
        # Test status retrieval
        mouse_status = mock_controller.get_mouse_override_status()
        print(f"✅ Mock controller mouse status: {mouse_status}")
        
        # Test different states
        print("\nTesting different mouse override states...")
        
        # State 1: Normal enabled
        print("State 1: Normal enabled")
        status = mock_controller.get_mouse_override_status()
        print(f"  enabled: {status['enabled']}")
        print(f"  module_executing: {status['module_executing']}")
        print(f"  active_overrides: {status['active_overrides']}")
        
        # State 2: Disabled for module
        print("\nState 2: Disabled for module")
        mock_controller.mouse_controller.disable_mouse_override("Test module")
        status = mock_controller.get_mouse_override_status()
        print(f"  enabled: {status['enabled']}")
        print(f"  module_executing: {status['module_executing']}")
        print(f"  active_overrides: {status['active_overrides']}")
        print(f"  override_reasons: {status['override_reasons']}")
        
        # State 3: Re-enabled with cooldown
        print("\nState 3: Re-enabled with cooldown")
        mock_controller.mouse_controller.enable_mouse_override("Test module complete", cooldown=3.0)
        status = mock_controller.get_mouse_override_status()
        print(f"  enabled: {status['enabled']}")
        print(f"  in_cooldown: {status['in_cooldown']}")
        print(f"  cooldown_remaining: {status['cooldown_remaining']:.1f}")
        
        print("\n✅ GUI integration test completed!")
        return True
        
    except Exception as e:
        print(f"❌ GUI integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 MOUSE OVERRIDE CONTROLLER DEBUG")
    print("=" * 60)
    
    # Run tests
    test1_result = test_mouse_override_status()
    test2_result = test_gui_integration()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    if test1_result:
        print("✅ Mouse Override Status Test: PASSED")
    else:
        print("❌ Mouse Override Status Test: FAILED")
    
    if test2_result:
        print("✅ GUI Integration Test: PASSED")
    else:
        print("❌ GUI Integration Test: FAILED")
    
    if test1_result and test2_result:
        print("\n🎯 ALL TESTS PASSED - Mouse override controller is working correctly!")
        print("\nIf GUI status is not updating, check:")
        print("1. GUI status update thread is running")
        print("2. Controller is properly initialized in GUI")
        print("3. No exceptions in GUI status update loop")
    else:
        print("\n❌ SOME TESTS FAILED - Check error messages above")
    
    print("\nPress Enter to exit...")
    input()
