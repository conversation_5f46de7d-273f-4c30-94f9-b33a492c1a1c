#!/usr/bin/env python3
"""
Quick test to verify confidence scoring fixes
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fast_map_ocr import FastMapOCR

def test_confidence_calculation():
    """Test the confidence calculation directly"""
    print("Testing Fast Map OCR Confidence Calculation")
    print("=" * 50)
    
    ocr = FastMapOCR()
    ocr.set_debug_mode(True)
    
    # Test cases that were problematic
    test_cases = [
        # (number, text, config_idx, expected_behavior)
        (7, "7", 0, "Should have moderate confidence"),
        (17, "17", 0, "Should have LOW confidence (OCR error pattern)"),
        (18, "18", 0, "Should have LOW confidence (OCR error pattern)"),
        (138, "138", 1, "Should have VERY LOW confidence (large number)"),
        (19, "19", 0, "Should have moderate confidence"),
        (3, "3", 0, "Should have moderate confidence"),
    ]
    
    print("\nTesting confidence calculation:")
    for number, text, config_idx, expected in test_cases:
        confidence = ocr._calculate_confidence(number, text, config_idx, "TEST")
        validation_passed = ocr._validate_number(number, text, "TEST")
        
        print(f"Number: {number:3}, Text: '{text:3}', Confidence: {confidence:.3f}, Validation: {'PASS' if validation_passed else 'FAIL'}")
        print(f"  Expected: {expected}")
        print()
    
    print("=" * 50)
    print("Key expectations:")
    print("- Numbers 17, 18, 138 should have LOW confidence or FAIL validation")
    print("- Numbers 7, 19, 3 should have moderate confidence and PASS validation")
    print("- No confidence should exceed 0.5")

if __name__ == "__main__":
    test_confidence_calculation()
