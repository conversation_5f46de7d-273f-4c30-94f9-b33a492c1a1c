#!/usr/bin/env python3
"""
Quick test to verify libpng warning suppression is working
"""

import warnings
import os

# Apply the same suppression as in our modules
warnings.filterwarnings("ignore", message=".*libpng warning.*")
warnings.filterwarnings("ignore", message=".*sBIT.*")
warnings.filterwarnings("ignore", category=UserWarning, module="PIL")
os.environ['OPENCV_LOG_LEVEL'] = 'ERROR'

def test_image_loading():
    """Test image loading operations that typically trigger libpng warnings"""
    print("🧪 Testing libpng warning suppression...")
    
    try:
        import cv2
        import numpy as np
        import pyautogui
        
        # Test 1: Screenshot capture (common source of warnings)
        print("📸 Testing screenshot capture...")
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            screenshot = pyautogui.screenshot()
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            libpng_warnings = [warning for warning in w if 'libpng' in str(warning.message).lower()]
            if libpng_warnings:
                print(f"⚠️  Found {len(libpng_warnings)} libpng warnings during screenshot")
            else:
                print("✅ No libpng warnings during screenshot")
        
        # Test 2: Template loading (if templates exist)
        print("🖼️  Testing template loading...")
        template_files = [
            "templates/red_thumb_100x40.png",
            "templates/Red_thumb.png",
            "templates/cancel_exchange_100x40.png"
        ]
        
        for template_file in template_files:
            if os.path.exists(template_file):
                print(f"   Loading {template_file}...")
                with warnings.catch_warnings(record=True) as w:
                    warnings.simplefilter("always")
                    template = cv2.imread(template_file)
                    
                    libpng_warnings = [warning for warning in w if 'libpng' in str(warning.message).lower()]
                    if libpng_warnings:
                        print(f"   ⚠️  Found {len(libpng_warnings)} libpng warnings")
                    else:
                        print(f"   ✅ No libpng warnings")
            else:
                print(f"   ⏭️  Skipping {template_file} (not found)")
        
        print("\n🎉 libpng warning suppression test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    test_image_loading()
