#!/usr/bin/env python3
"""
Test script to verify that the map trade module mouse override fix is working correctly.
This script tests that programmatic clicks are properly registered to avoid false positives.
"""

import sys
import os
import time
import logging
from unittest.mock import Mock, patch

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_map_trade_safe_click():
    """Test that map trade module properly registers programmatic clicks"""
    print("🧪 Testing Map Trade Mouse Override Fix...")
    
    try:
        # Import required modules
        from modules.map_trade import MapTradeModule
        from unified_config_manager import UnifiedConfigManager
        
        # Create mock controller with register_programmatic_click method
        mock_controller = Mock()
        mock_controller.register_programmatic_click = Mock()
        
        # Create unified config manager
        config_manager = UnifiedConfigManager()
        
        # Create map trade module instance
        map_trade = MapTradeModule(config_manager)
        
        # Set the controller reference (simulating what main controller does)
        map_trade.controller = mock_controller
        
        print("✅ Map trade module created successfully")
        
        # Test safe click functionality
        test_x, test_y = 100, 200
        
        with patch('pyautogui.click') as mock_pyautogui_click:
            # Call _safe_click method
            map_trade._safe_click(test_x, test_y)
            
            # Verify that register_programmatic_click was called
            mock_controller.register_programmatic_click.assert_called_once_with(test_x, test_y)
            print(f"✅ Programmatic click registered correctly at ({test_x}, {test_y})")
            
            # Verify that pyautogui.click was called
            mock_pyautogui_click.assert_called_once_with(test_x, test_y)
            print(f"✅ PyAutoGUI click executed correctly at ({test_x}, {test_y})")
        
        # Test safe click without registration
        with patch('pyautogui.click') as mock_pyautogui_click:
            mock_controller.register_programmatic_click.reset_mock()
            
            # Call _safe_click with register_movement=False
            map_trade._safe_click(test_x, test_y, register_movement=False)
            
            # Verify that register_programmatic_click was NOT called
            mock_controller.register_programmatic_click.assert_not_called()
            print("✅ Programmatic click registration correctly skipped when disabled")
            
            # Verify that pyautogui.click was still called
            mock_pyautogui_click.assert_called_once_with(test_x, test_y)
            print("✅ PyAutoGUI click still executed when registration disabled")
        
        print("\n🎉 All tests passed! Map trade mouse override fix is working correctly.")
        print("\n📋 Summary of fixes:")
        print("   • All pyautogui.click() calls replaced with _safe_click()")
        print("   • _safe_click() properly registers programmatic movements")
        print("   • Controller reference fixed from 'main_controller' to 'controller'")
        print("   • Mouse override system will now ignore map trade clicks")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_click_replacement_coverage():
    """Test that all pyautogui.click calls have been replaced"""
    print("\n🔍 Checking click replacement coverage...")
    
    try:
        # Read the map trade module file
        with open('modules/map_trade.py', 'r') as f:
            content = f.read()
        
        # Count remaining pyautogui.click calls (excluding imports and comments)
        lines = content.split('\n')
        pyautogui_clicks = []
        
        for i, line in enumerate(lines, 1):
            if 'pyautogui.click(' in line and not line.strip().startswith('#'):
                # Skip the fallback click in _safe_click method
                if '_safe_click' in lines[max(0, i-10):i+5]:
                    continue
                pyautogui_clicks.append((i, line.strip()))
        
        if pyautogui_clicks:
            print(f"⚠️  Found {len(pyautogui_clicks)} remaining pyautogui.click calls:")
            for line_num, line in pyautogui_clicks:
                print(f"   Line {line_num}: {line}")
            return False
        else:
            print("✅ All pyautogui.click calls have been replaced with _safe_click")
            return True
            
    except Exception as e:
        print(f"❌ Coverage check failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("MAP TRADE MOUSE OVERRIDE FIX - TEST SUITE")
    print("=" * 60)
    
    # Run tests
    test1_passed = test_map_trade_safe_click()
    test2_passed = test_click_replacement_coverage()
    
    print("\n" + "=" * 60)
    if test1_passed and test2_passed:
        print("🎉 ALL TESTS PASSED - Mouse override fix is ready!")
        print("\n💡 Next steps:")
        print("   1. Run the automation system")
        print("   2. Trigger map trade module")
        print("   3. Verify no mouse override activation in logs")
        print("   4. Look for '[SAFE_CLICK] Registered programmatic click' messages")
    else:
        print("❌ SOME TESTS FAILED - Please review the issues above")
    print("=" * 60)
